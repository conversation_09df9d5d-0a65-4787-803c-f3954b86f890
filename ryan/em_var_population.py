#%%
from DataYatesV1 import (plot_stas, print_batch, LNPModel, 
                         fit_lnp_lbfgs, PoissonMaskedLoss, get_free_device,
                         CombinedEmbeddedDataset, split_inds_by_trial,
                         calc_dset_sta, calc_dset_stc, thresholded_centroid,
                         ensure_tensor)

from DataYatesV1.models.losses import PoissonBPSAggregator, MaskedLoss, calc_poisson_bits_per_spike

import optuna
from optuna.samplers import TPESampler
from tqdm import tqdm
from DataYatesV1 import get_session, DictDataset, CombinedEmbeddedDataset, get_gaborium_sta_ste
import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from DataYatesV1 import enable_autoreload
from copy import deepcopy
import gc
enable_autoreload()

from DataYatesV1.utils.modeling.general import get_valid_dfs
from torch.utils.data import DataLoader

subject = 'Allen'
date = '2022-04-13'
sess = get_session(subject, date)

device = get_free_device(1)

#%%
# inclusion criteria
# 1) Good isolation (refractory + amp truncation)
# 2) Orientation selective (from gratings)
# 3) enough spikes in fixrsvp
# NOTE: picked by hand for now

cids = np.array([33, 89, 92, 98, 101, 113, 116, 122, 126])
cids = np.array([126])
n_lags = 20
n_trials = 30

top_percentile = 20
# inclusion = np.load(sess.sess_dir / 'inclusion' / 'inclusion.npz')

# cids = np.unique(sess.ks_results.spike_clusters)
# min_contam_pct = inclusion['min_contam_pct']
# contam_threshold = 60
# med_miss_pct = inclusion['med_miss_pct']
# miss_pct_threshold = 35
# qc_cids = np.where((min_contam_pct < contam_threshold) & (med_miss_pct < miss_pct_threshold))[0]
# print(f'{len(qc_cids)} / {len(cids)} units pass')

# #%%

# fixrsvp = DictDataset.load(sess.sess_dir / 'shifter' / 'fixrsvp_shifted.dset')
# fixrsvp_n_spikes = fixrsvp['robs'].sum(dim=0).numpy()
# spikes_threshold = 500 # arbitrary

# #%%
# from scipy.ndimage import gaussian_filter
# stas, stes = get_gaborium_sta_ste(sess, n_lags, qc_cids)
# # Find maximum energy lag for each cluster
# signal = np.abs(stes - np.median(stes, axis=(2,3), keepdims=True))
# sigma = [0, 2, 2, 2]
# signal = gaussian_filter(signal, sigma)
# noise = np.median(signal[:,0], axis=(1,2))
# cid_snr = np.max(signal, axis=(1,2,3)) / noise
# plot_stas(signal[:,:,None])
# snr_thresh = 7

# #%%
# good_cids = np.where((cid_snr > snr_thresh) & (fixrsvp_n_spikes[qc_cids] > spikes_threshold))[0]
# print(f'{len(good_cids)} / {len(qc_cids)} units are good')
# cids = qc_cids[good_cids]

# %%

train_val_split = .6
batch_size = 4096
crop_radius = 15

cache = Path(f'/mnt/ssd/YatesMarmoV1/tmp/em_var')
cache.mkdir(parents=True, exist_ok=True)
recalc = False
if recalc:
    # delete cache
    import shutil
    shutil.rmtree(cache)
    cache.mkdir(parents=True, exist_ok=True)

def load_data(sess, n_lags, train_val_split, cid):
    gaborium_dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
    gaborium_dset['stim'] = (gaborium_dset['stim'].float() - 127) / 255

    gaborium_dset['dfs'] = get_valid_dfs(gaborium_dset, n_lags)
    gaborium_dset['robs'] = gaborium_dset['robs'][:,[cid]]
    gaborium_inds = gaborium_dset['dfs'].squeeze().nonzero(as_tuple=True)[0]
    print(f'Gaborium dataset size: {len(gaborium_inds)} / {len(gaborium_dset)} ({len(gaborium_inds)/len(gaborium_dset)*100:.2f}%)')

    train_inds, val_inds = split_inds_by_trial(gaborium_dset, gaborium_inds, train_val_split)

    return gaborium_dset, train_inds, val_inds

def crop_dataset(dset, sta, ste, crop_radius):
    assert dset['robs'].shape[1] == 1, 'Only implemented for single unit'
    sta = sta[0]
    ste = ste[0]
    
    energy_lags = ste.mean((1,2))
    max_lag = energy_lags.argmax()
    print(f'Max lag: {max_lag}')
    max_pix = thresholded_centroid(ste[max_lag].numpy())
    print(f'Max pix: {max_pix}')
    max_pix = np.round(max_pix).astype(int)
    roi = [[max_pix[0]-crop_radius, max_pix[0]+crop_radius+1], [max_pix[1]-crop_radius, max_pix[1]+crop_radius+1]]  
    
    fig, axs = plt.subplots(1, 2, figsize=(10, 5))
    axs[0].imshow(sta[max_lag].numpy(), cmap='gray')
    axs[0].scatter(max_pix[1], max_pix[0], c='r', s=100)
    axs[0].plot([roi[1][0], roi[1][1], roi[1][1], roi[1][0], roi[1][0]], 
                [roi[0][0], roi[0][0], roi[0][1], roi[0][1], roi[0][0]], c='r')
    axs[0].set_title('STA')
    axs[1].imshow(ste[max_lag].numpy(), cmap='gray')
    axs[1].scatter(max_pix[1], max_pix[0], c='r', s=100)
    axs[1].plot([roi[1][0], roi[1][1], roi[1][1], roi[1][0], roi[1][0]], 
                [roi[0][0], roi[0][0], roi[0][1], roi[0][1], roi[0][0]], c='r')
    plt.show()

    dset['stim'] = dset['stim'][:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]].contiguous()
    sta = sta[:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]]
    ste = ste[:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]]
    return dset, sta, ste, roi

from DataYatesV1.utils.modeling.reg import l1, laplacian, locality_conv
class StandardModel(torch.nn.Module):
    def __init__(self, dims, n_units, base_fr=None, lap_xy=0, lap_t=0, local=0, l2=0):
        super(StandardModel, self).__init__()
        self.bias = torch.nn.Parameter(torch.zeros(n_units))
        assert len(dims) == 3
        self.lap_xy = lap_xy
        self.lap_t = lap_t
        self.local = local
        self.l2 = l2

        n_lags, n_y, n_x = dims
        self.n_units = n_units
        self.dims = dims

        self.linear_filter = nn.Parameter(torch.randn(n_units, n_lags, n_y, n_x))
        self.energy_filters = nn.ParameterList([
            torch.randn(n_units, n_lags, n_y, n_x) for _ in range(2)
        ])

        if base_fr is not None:
            self.bias.data = torch.log(torch.exp(base_fr) - 1)
        
    def reg_loss(self):
        reg_loss = 0
        if self.lap_xy > 0:
                reg_loss += self.lap_xy * laplacian(self.linear_filter, dims=[-2, -1])
                for e in self.energy_filters:
                    reg_loss += self.lap_xy * laplacian(e, dims=[-2, -1])
        if self.lap_t > 0:
            reg_loss += self.lap_t * laplacian(self.linear_filter, dims=1)
            for e in self.energy_filters:
                reg_loss += self.lap_t * laplacian(e, dims=1)
        if self.local > 0:
            reg_loss += self.local * locality_conv(self.linear_filter, dims=[-2, -1])
            for e in self.energy_filters:
                reg_loss += self.local * locality_conv(e, dims=[-2, -1])
        if self.l2 > 0:
            reg_loss += self.l2 * (torch.norm(self.linear_filter, p=2) + sum(torch.norm(e, p=2) for e in self.energy_filters))

        return reg_loss

    def forward(self, x):
        # x['stim']: (batch, n_lags, H, W)
        n_samples = len(x['stim'])
        g = x['stim'].view(n_samples, -1) @ self.linear_filter.view(self.n_units, -1).T
        for e in self.energy_filters:
            g += torch.pow(x['stim'].view(n_samples, -1) @ e.view(self.n_units, -1).T, 2)
        g += self.bias
        x['rhat'] = F.softplus(g)
        return x

    def plot_weights(self):
        # Extract feature weights.
        # The feature weights are from a 1x1 conv: shape (n_units, in_channels, 1, 1)
        # Squeeze to (n_units, in_channels)
        linear_weights = self.linear_filter.detach().cpu().numpy().squeeze()
        l_max = np.max(np.abs(linear_weights))
        n_lags = len(linear_weights)
        # Extract spatial weights: expected shape (n_units, H, W)
        e1_weights = self.energy_filters[0].detach().cpu().numpy().squeeze()
        e1_max = np.max(np.abs(e1_weights))
        e2_weights = self.energy_filters[1].detach().cpu().numpy().squeeze()
        e2_max = np.max(np.abs(e2_weights))
        e_max = np.max([e1_max, e2_max])


        fig, axs = plt.subplots(3, linear_weights.shape[0], figsize=(n_lags, 3))
        for i in range(n_lags):
            im1 = axs[0, i].imshow(linear_weights[i], cmap='coolwarm_r', interpolation='nearest', vmin=-l_max, vmax=l_max)
            im2 = axs[1, i].imshow(e1_weights[i], cmap='coolwarm_r', interpolation='nearest', vmin=-e_max, vmax=e_max)
            im3 = axs[2, i].imshow(e2_weights[i], cmap='coolwarm_r', interpolation='nearest', vmin=-e_max, vmax=e_max)
            axs[2,i].set_xlabel(f'{i}')
            if i == 0:
                axs[0, i].set_ylabel('Linear')
                axs[1, i].set_ylabel('Energy 1')
                axs[2, i].set_ylabel('Energy 2')
            if i == n_lags - 1:
                fig.colorbar(im1, ax=axs[0, i], fraction=0.046, pad=0.04)
                fig.colorbar(im2, ax=axs[1, i], fraction=0.046, pad=0.04)
                fig.colorbar(im3, ax=axs[2, i], fraction=0.046, pad=0.04)
        for ax in axs.flatten():
            ax.set_xticks([])
            ax.set_yticks([])
        return fig, axs

def get_initial_filters(sta, eig_filters, dset, inds, batch_size = 2048, n_steps=10, device='cpu'):
    filters = torch.cat([
        sta[None,...],
        eig_filters[0, :2]
    ], dim=0).to(device)

    plot_stas(filters[:,:,None], row_labels=['STA', 'Eig 1', 'Eig 2'])
    plt.show()

    n_lags = filters.shape[1]
    keys_lags = {
        'robs': 0,
        'stim': np.arange(n_lags),
    }
    train_dset = CombinedEmbeddedDataset(dset, inds, keys_lags, device)

    robs = []
    filter_outputs = []
    for i in tqdm(range(0, len(train_dset), batch_size)):
        batch = train_dset[i:i+batch_size]
        robs.append(batch['robs'])
        stim = batch['stim']
        filter_outputs.append(torch.einsum('tlyx, flyx->tf', stim, filters))
    robs = torch.cat(robs, dim=0)
    filter_outputs = torch.cat(filter_outputs, dim=0)

    def calc_nonlinearity_1d(gen, robs, n_bins=20):
        bin_edges = np.percentile(gen.cpu().numpy(), np.linspace(0, 100, 21))
        bins = (bin_edges[1:] + bin_edges[:-1]) / 2
        nonlin = torch.zeros(len(bins))
        for i in range(len(bins)):
            mask = (gen > bin_edges[i]) & (gen < bin_edges[i+1])
            nonlin[i] = robs[mask].mean()
        return bins, nonlin
        
    l_bins, l_nonlin = calc_nonlinearity_1d(filter_outputs[:,0], robs)
    e1_bins, e1_nonlin = calc_nonlinearity_1d(filter_outputs[:,1], robs)
    e2_bins, e2_nonlin = calc_nonlinearity_1d(filter_outputs[:,2], robs)

    fig, axs = plt.subplots(1,2, figsize=(10, 5))
    axs[0].plot(l_bins, l_nonlin, label='Linear')
    axs[1].plot(e1_bins, e1_nonlin, label='Eig 1')
    axs[1].plot(e2_bins, e2_nonlin, label='Eig 2')
    for ax in axs:
        ax.legend()
        ax.set_xlabel('Filter Output')
        ax.set_ylabel('spikes / bin')
    plt.show()

    stim = filter_outputs.clone()
    stim[1:] = stim[1:] ** 2
    data = {
        'stim': stim,
        'robs': robs.to(device),
    }

    lnp_model = fit_lnp_lbfgs(data, 
                              n_steps=n_steps, 
                              lbfgs_kwargs={'lr': .1, 'max_iter': 2000, 'max_eval': 2000, 'tolerance_change': 1e-6, 'tolerance_grad': 1e-6},
                              device=device)


    init_filters = filters.clone()
    init_filters[0] *= lnp_model.kernel.detach()[0,0]
    init_filters[1] *= torch.sqrt(torch.abs(lnp_model.kernel.detach()[0,1]))
    init_filters[2] *= torch.sqrt(torch.abs(lnp_model.kernel.detach()[0,2]))
    init_filters = init_filters.to('cpu')

    init_bias = lnp_model.bias.detach()
    init_bias = init_bias.to('cpu')

    torch.cuda.empty_cache()
    return init_filters, init_bias

def evaluate_model(model, data):
    bps_agg = PoissonBPSAggregator()
    with torch.no_grad():
        out = model(data)
        bps_agg(out)
        bps = bps_agg.closure().item()
    return bps

def fit_standard_model(train_data, model, device='cuda:0', batch_size=4096, 
                       loss_tol=1e-6, max_steps=10, verbose=True):
                       
    model.to(device)
    loss_fn = PoissonMaskedLoss()
    
    optim = torch.optim.LBFGS(model.parameters(), 
                            history_size=10,
                            max_iter=500,
                            tolerance_change=1e-6,
                            line_search_fn=None,
                            tolerance_grad=1e-6)

    def closure(pbar=None):
        optim.zero_grad()
        n_samples = len(train_data['robs'])
        total_loss = 0
        
        # Process in batches to avoid OOM
        for i in range(0, n_samples, batch_size):
            # Create batch with explicit device placement
            batch = {key: val[i:i+batch_size].to(device) for key, val in train_data.items()}
            
            # Forward pass
            out = model(batch)
            
            # Calculate batch loss (don't call backward yet)
            batch_loss = loss_fn({'rhat': out['rhat'], 'robs': batch['robs']})
            
            # Scale loss by batch proportion and accumulate
            batch_fraction = len(batch['robs']) / n_samples
            batch_loss = batch_loss * batch_fraction
            batch_loss.backward()
            
            total_loss += batch_loss.item()
            
        
        # Add regularization loss and backward
        reg_loss = model.reg_loss()
        reg_loss.backward()
        total_loss += reg_loss.item()
        
        if pbar is not None:
            pbar.update(1)
            
        return total_loss
    
    train_losses = []

    for i in range(max_steps): 
        with tqdm(desc=f'Epoch {i}', disable=not verbose) as pbar:
            train_loss = optim.step(lambda: closure(pbar))
        train_losses.append(float(train_loss))
        status = f'\tTrain loss {i}: {train_loss:.4e}'
        if i == 0:
            print(status)
        else:
            print(f'{status} ({train_losses[-1] - train_losses[-2]:.4e})')
            if train_losses[-1] - train_losses[-2] > 0:
                print(f'Loss diverging, stopping training')
                return False
            elif np.isnan(train_losses[-1]):
                print(f'Loss is NaN, stopping training')
                return False
            elif abs(train_losses[-1] - train_losses[-2]) < loss_tol:
                print(f'Loss converged, stopping training')
                return True
    return True

def mcfarland2016(robs, eyepos, n_bins=10, plot = False):
    '''
    Partition the variance due to fixational eye movements
    Inputs:
        robs: ndarray [n_trials, n_bins] binned responses for a single unit
        eye_pos: ndarray [n_trials, n_bins, n_lags, 2] eye position
        nbins: int, number of bins for eye position distance (uses percentile)
    '''
    assert (robs.shape[0] == eyepos.shape[0]) and (robs.shape[1] == eyepos.shape[1]), \
        'robs and eyepos must have the same number of trials and bins'


    total_var = np.var(robs)
    mean_rate = np.mean(robs)

    # Estimate rate variance from cross trial covariance
    trial_outer = robs[:,None,:] * robs[None,:,:]
    upper_mask = np.triu(np.ones(trial_outer.shape[:2], dtype=bool), k=1)
    trial_outer[~upper_mask] = np.nan
    rate_var = np.nanmean(trial_outer) - mean_rate**2

    ep_dist = eyepos[:,None,:] - eyepos[None,:,:]
    ep_dist = np.hypot(ep_dist[...,0], ep_dist[...,1])
    ep_dist = np.mean(ep_dist, axis=-1)
    ep_dist[~upper_mask] = np.nan

    ep_dist_flat = ep_dist.flatten()
    ep_dist_flat = ep_dist_flat[~np.isnan(ep_dist_flat)]

    ep_dist_thresholds = np.percentile(ep_dist_flat, np.linspace(0, 100, n_bins))
    cum_ep_thresholds = ep_dist_thresholds[1:]
    cum_rate_vars = []

    for thresh in cum_ep_thresholds:
        ep_dist_mask = ep_dist < thresh
        ep_rate_var = np.mean(trial_outer[ep_dist_mask]) - mean_rate**2
        cum_rate_vars.append(ep_rate_var)
    cum_rate_vars = np.array(cum_rate_vars)

    ep_bins = []
    bin_rate_vars = []
    for t0, t1 in zip(ep_dist_thresholds[:-1], ep_dist_thresholds[1:]):
        ep_dist_mask = np.logical_and(ep_dist >= t0, ep_dist < t1)
        ep_bins.append(np.median(ep_dist[ep_dist_mask]))
        bin_rate_vars.append(np.mean(trial_outer[ep_dist_mask]) - mean_rate**2)
    ep_bins = np.array(ep_bins)
    bin_rate_vars = np.array(bin_rate_vars)

    em_corrected_var = cum_rate_vars[0]
    alpha = rate_var / em_corrected_var

    if plot:
        fig, axs = plt.subplots(2, 1, figsize=(8,8), sharex=True)
        #axs[0].axhline(total_var, color='k', linestyle='--', label='Total Variance')
        axs[0].axhline(cum_rate_vars[0], color='g', linestyle='--', label='EM-Corrected Rate Variance')
        axs[0].axhline(rate_var, color='r', linestyle='--', label='Raw Rate Variance')
        axs[0].plot(ep_bins, bin_rate_vars, 'o-', label='Binned Rate Variance')
        axs[0].plot(cum_ep_thresholds, cum_rate_vars, 'o-', label='Cumulative Rate Variance')
        axs[0].legend()
        axs[0].set_ylabel('Variance (spikes^2)')
        axs[0].set_xlabel('Eye Position Distance (degrees)')
        axs[0].set_title(f'Variance decomposition. \n$\\alpha$ = {alpha:.2f}, Total variance = {total_var:.2f}')

        axs[1].hist(ep_dist_flat, bins=50)
        axs[1].set_xlabel('Eye Position Distance (degrees)')
        axs[1].set_ylabel('Count')
        axs[1].set_title('Eye Position Distance Distribution')
        plt.show()
    out = {
        'alpha': alpha,
        'total_var': total_var,
        'rate_var': rate_var,
        'em_corrected_var': em_corrected_var,
        'ep_bins': ep_bins,
        'bin_rate_vars': bin_rate_vars,
        'cum_ep_thresholds': cum_ep_thresholds,
        'cum_rate_vars': cum_rate_vars,
    }
    return out

#%%

for cid in cids:
    print(f'Starting unit {cid}')
    unit_cache = cache / f'{cid}'
    unit_cache.mkdir(parents=True, exist_ok=True)

    gaborium_dset, train_inds, val_inds = load_data(
        sess, n_lags, train_val_split, cid)
    
    sta_file = unit_cache / f'sta_{cid}.pt'
    if sta_file.exists():
        sta = torch.load(sta_file)
    else:
        sta = calc_dset_sta(gaborium_dset, train_inds, n_lags, device=device, verbose=1)
        torch.save(sta, sta_file)

    ste_file = unit_cache / f'ste_{cid}.pt'
    if ste_file.exists():
        ste = torch.load(ste_file)
    else:
        ste = calc_dset_sta(gaborium_dset, train_inds, n_lags, modifier=lambda x: x**2, device=device, verbose=1)
        torch.save(ste, ste_file)

    sta = ensure_tensor(sta, dtype=torch.float32)
    ste = ensure_tensor(ste, dtype=torch.float32)

    gaborium_dset, sta, ste, roi = crop_dataset(gaborium_dset, sta, ste, crop_radius)
 
    stc_file = unit_cache / f'stc_{cid}.pt'
    if not stc_file.exists():
        eig_filters, eig_vals, cov = calc_dset_stc(gaborium_dset, train_inds, n_lags, sta=sta[None,...],device=device, verbose=1)
        torch.save((eig_filters, eig_vals, cov), stc_file)
    else:
        eig_filters, eig_vals, cov = torch.load(stc_file)

    fig, axs = plt.subplots(2,1, figsize=(8, 8))
    axs[0].scatter(np.arange(eig_vals.shape[1]), eig_vals[0])
    plot_stas(eig_filters[0,:6,:,None], row_labels=[f'Eig {i}' for i in range(6)], ax = axs[1])
    plt.show()

    filters, bias = get_initial_filters(sta, eig_filters, 
                                        gaborium_dset, train_inds, 
                                        batch_size=2048, n_steps=10,
                                        device=device)


    keys_lags = {
        'robs': 0,
        'stim': np.arange(n_lags),
    }
    train_data = CombinedEmbeddedDataset(gaborium_dset,
                                        train_inds,
                                        keys_lags,
                                        device)[:]

    print_batch(train_data)

    val_data = CombinedEmbeddedDataset(gaborium_dset,
                                    val_inds,
                                    keys_lags,
                                    'cpu')[:]
    print_batch(val_data)
    torch.cuda.empty_cache()

    init_model = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1)

    # init weights
    init_model.linear_filter.data = filters[0].unsqueeze(0)
    init_model.energy_filters[0].data = filters[1].unsqueeze(0)
    init_model.energy_filters[1].data = filters[2].unsqueeze(0)
    init_model.bias.data = bias

    init_bps = evaluate_model(init_model, val_data)
    print(f"Initial model BPS: {init_bps:.4f}")
    fig, axs = init_model.plot_weights()
    fig.suptitle(f'Initial Model - BPS: {init_bps:.4f}')
    fig.tight_layout()
    fig.savefig(unit_cache / f'cid_{cid}_initial_model.png')
    plt.close(fig)

    torch.save(init_model.state_dict(), unit_cache / f'cid_{cid}_initial_model.pt')

    def objective(trial):
        """Objective function for Optuna hyperparameter optimization."""
        # Define hyperparameter search space
        reg = {
            'lap_xy': trial.suggest_float('lap_xy', 1e-5, 1e-1, log=True),
            'lap_t': trial.suggest_float('lap_t', 1e-5, 1e-1, log=True),
            'local': trial.suggest_float('local', 1e-1, 1e2, log=True)
        }
        
        # Create model with trial hyperparameters
        model = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1, **reg)
        
        # Initialize weights
        model.linear_filter.data = filters[0].unsqueeze(0).clone()
        model.energy_filters[0].data = filters[1].unsqueeze(0).clone()
        model.energy_filters[1].data = filters[2].unsqueeze(0).clone()
        model.bias.data = bias.clone()

        init_bps = evaluate_model(model, val_data)
        
        # Train model
        batch_size = 2**12
        res = fit_standard_model(train_data, model, device=device, batch_size=batch_size, verbose=False)
        model.to('cpu')

        # Save model
        torch.save(model.state_dict(), unit_cache / f'cid_{cid}_trial_{trial.number}_model.pt')

        # Evaluate on validation set
        val_bps = evaluate_model(model, val_data)

        # Plot weights
        fig, axs = model.plot_weights()
        fig.suptitle(f'Trial {trial.number} - BPS: {init_bps:.4f} => {val_bps:.4f}\nLap XY: {reg['lap_xy']:.2e}, Lap T: {reg['lap_t']:.2e}, Local: {reg['local']:.2e}')
        fig.tight_layout()
        fig.savefig(unit_cache / f'cid_{cid}_trial_{trial.number}.png')
        plt.close(fig)

        if not res:
            optuna.TrialPruned()

        return val_bps  # We want to maximize bits per spike

    # Create study
    n_startup = n_trials // 2
    study_name = f'{cid}_optuna_study'
    storage_file = unit_cache / f'{study_name}.db'
    storage_str = 'sqlite:///' + str(storage_file)

    if storage_file.exists():
        study = optuna.load_study(study_name=study_name, storage=storage_str)
        if len(study.trials) < n_trials:
            print(f'Found {len(study.trials)} trials in study, but {n_trials} were requested. Recreating study...')
            storage_file.unlink()
        
    if not storage_file.exists():
        study = optuna.create_study(
            direction='maximize',  # Maximize bits per spike
            sampler=TPESampler(seed=1002, multivariate=True, n_startup_trials=n_startup),
            study_name=study_name,
            storage=storage_str,
        )
        study.optimize(objective, n_trials=n_trials)

    # Plot optimization history
    plt.figure(figsize=(10, 6))
    optuna.visualization.matplotlib.plot_optimization_history(study)
    plt.title('Optimization History')
    plt.savefig(unit_cache / f'optimization_history_{cid}.png')
    plt.show()

    # Plot parameter importances
    plt.figure(figsize=(10, 6))
    optuna.visualization.matplotlib.plot_param_importances(study)
    plt.title('Parameter Importances')
    plt.savefig(unit_cache / f'param_importances_{cid}.png')
    plt.show()

    plt.style.use('default')

    # Free GPU memory
    del train_data, val_data, gaborium_dset
    torch.cuda.empty_cache()
    gc.collect()

    trials = study.get_trials()
    values = [t.value for t in trials]
    order = np.argsort(values)[::-1]

    n_top_trials = int(len(trials) * top_percentile / 100)
    print(f'Top {top_percentile}% of trials: {n_top_trials}')
    top_trials = [trials[i] for i in order[:n_top_trials]]
    lap_xys = np.log10(np.array([trial.params['lap_xy'] for trial in top_trials]))
    min_lap_xy = np.min(lap_xys)
    lap_xys -= min_lap_xy
    lap_ts = np.log10(np.array([trial.params['lap_t'] for trial in top_trials]))
    min_lap_t = np.min(lap_ts)
    lap_ts -= min_lap_t
    locals = np.log10(np.array([trial.params['local'] for trial in top_trials]))
    min_local = np.min(locals)
    locals -= min_local

    plt.figure()
    plt.scatter(lap_xys, lap_ts, c=locals)
    plt.xlabel('log10(lap_xy)')
    plt.ylabel('log10(lap_t)')
    plt.colorbar(label='log10(local)')
    plt.title(f'Top {n_top_trials} trials. \n Origin is {min_lap_xy:.2f}, {min_lap_t:.2f}, {min_local:.2f}')
    plt.show()

    reg_amount = np.sqrt(lap_xys**2 + lap_ts**2 + locals**2)
    most_reg_trial = top_trials[np.argmax(reg_amount)]
    best_state = torch.load(unit_cache / f'cid_{cid}_trial_{most_reg_trial.number}_model.pt')
    best_model = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1, **most_reg_trial.params)
    best_model.load_state_dict(best_state)

    fig, axs = best_model.plot_weights()
    fig.suptitle(f'Best Model Weights (BPS: {most_reg_trial.value:.4f})\nLap XY: {most_reg_trial.params['lap_xy']:.2e}, Lap T: {most_reg_trial.params['lap_t']:.2e}, Local: {most_reg_trial.params['local']:.2e}')
    fig.savefig(cache / f'best_weights_{cid}.png')
    plt.show()

    fixrsvp = DictDataset.load(sess.sess_dir / 'shifter' / 'fixrsvp_shifted.dset')
    fixrsvp['stim'] = (fixrsvp['stim'].float() - 127) / 255
    # crop stim
    fixrsvp['stim'] = fixrsvp['stim'][:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]].contiguous()
    fixrsvp['dfs'] = get_valid_dfs(fixrsvp, n_lags)
    fixrsvp['robs'] = fixrsvp['robs'][:,[cid]]
    fixrsvp_inds = fixrsvp['dfs'].squeeze().nonzero(as_tuple=True)[0]

    fixrsvp_keys_lags = {
        'robs': 0,
        'stim': np.arange(n_lags),
        'dfs': 0,
        'eyepos': np.arange(n_lags),
        'psth_inds': 0,
        'trial_inds': 0,
    }
    fixrsvp_dset = CombinedEmbeddedDataset(fixrsvp, fixrsvp_inds, fixrsvp_keys_lags)
    fixrsvp_data = fixrsvp_dset[:]

    def collate_fixrsvp(data, min_bins):

        assert 'robs' in data, 'robs must be in data'
        assert 'rhat' in data, 'rhat must be in data'
        assert 'eyepos' in data, 'eyepos must be in data'
        assert 'psth_inds' in data, 'psth_inds must be in data'
        assert 'trial_inds' in data, 'trial_inds must be in data'

        unique_trials = np.unique(data['trial_inds'])
        unique_psth_inds = np.unique(data['psth_inds'])
        robs = []
        rhat = []
        eyepos = []
        min_psth_ind = np.min(unique_psth_inds)
        required_inds = np.arange(min_psth_ind, min_bins)
        for iT in unique_trials: 
            trial_mask = (data['trial_inds'] == iT).numpy()
            trial_psth_inds = (data['psth_inds'][trial_mask]).numpy()
            if not np.isin(required_inds, trial_psth_inds).all():
                print(f'Skipping trial {iT}, has {len(trial_psth_inds)} only bins')
                continue

            trial_mask[trial_mask] = np.isin(trial_psth_inds, required_inds)

            trial_robs = data['robs'][trial_mask].T
            trial_rhat = data['rhat'][trial_mask].detach().T
            trial_eyepos = data['eyepos'][trial_mask]
            robs.append(trial_robs)
            rhat.append(trial_rhat)
            eyepos.append(trial_eyepos)
        robs = np.stack(robs, axis=1)
        rhat = np.stack(rhat, axis=1)
        eyepos = np.stack(eyepos, axis=0)
        print(f'Found {robs.shape[1]} trials for fixrsvp that are at least {min_trial_dur} seconds long.')
        return required_inds, robs, rhat, eyepos 

    min_trial_dur = .75 # include
    min_n_bins = int(min_trial_dur * 240)
    inds, robs, rhat_best, eyepos = collate_fixrsvp(best_model(fixrsvp_data), min_n_bins)

    np.savez(unit_cache / f'fixrsvp_{cid}.npz', inds=inds, robs=robs, rhat_best=rhat_best, eyepos=eyepos)

    t_trial = inds / 240
    psth = np.mean(robs, axis=1).squeeze()
    psth_ste = np.std(robs, axis=1) / np.sqrt(robs.shape[1]).squeeze()

    best_psth = np.mean(rhat_best, axis=1).squeeze()
    best_psth_ste = np.std(rhat_best, axis=1) / np.sqrt(rhat_best.shape[1])

    plt.figure()
    plt.plot(t_trial, psth, label='Data')
    plt.fill_between(t_trial, (psth - psth_ste).squeeze(), (psth + psth_ste).squeeze(), alpha=0.5)
    plt.plot(t_trial, best_psth, label='Best')
    plt.fill_between(t_trial, (best_psth - best_psth_ste).squeeze(), (best_psth + best_psth_ste).squeeze(), alpha=0.5)
    plt.xlabel('Time (s)')
    plt.ylabel('Firing Rate (Hz)')
    plt.title('FixRSVP PSTH')
    plt.legend()
    plt.savefig(unit_cache / f'fixrsvp_psth_{cid}.png')
    plt.show()

    results_data = mcfarland2016(robs[0], eyepos, plot=True)

    results_best = mcfarland2016(rhat_best[0], eyepos, plot=True)

    np.savez(unit_cache / f'mcfarland_{cid}.npz', results_data=results_data, results_best=results_best)

    # Plot McFarland analysis comparison between data and model
    fig, axs = plt.subplots(2, 1, figsize=(10, 10), sharex=True)

    # Colors for different metrics
    binned_color = 'C0'
    cumulative_color = 'C1'
    em_color = 'green'
    raw_color = 'red'

    # Line styles for data vs simulation
    data_style = '-'
    best_style = '--'
    mid_style = '-.'
    low_style = ':'

    # Top plot: Raw variance values
    # Binned rate variance
    axs[0].plot(results_data['ep_bins'], results_data['bin_rate_vars'], 'o-', 
            color=binned_color, linestyle=data_style, label='Binned EM-RV (Data)', linewidth=3, markersize=10)
    axs[0].plot(results_best['ep_bins'], results_best['bin_rate_vars'], 'o-', 
            color=binned_color, linestyle=best_style, label='Binned EM-RV (Best)', linewidth=3, markersize=10)

    # Horizontal lines for EM-corrected and raw rate variance
    axs[0].axhline(results_data['em_corrected_var'], color=em_color, linestyle=data_style, 
                zorder=0)
    axs[0].axhline(results_best['em_corrected_var'], color=em_color, linestyle=best_style, 
                zorder=0)

    axs[0].axhline(results_data['rate_var'], color=raw_color, linestyle=data_style, 
                zorder=0)
    axs[0].axhline(results_best['rate_var'], color=raw_color, linestyle=best_style, 
                zorder=0)

    axs[0].set_ylabel('Variance (spikes²)')
    axs[0].set_title(f'McFarland Analysis Comparison\nUnit: {cid}. Data α={results_data["alpha"]:.2f}, Best α={results_best["alpha"]:.2f}')
    lines = [
        plt.Line2D([0], [0], color=binned_color, linestyle=data_style, label='Data'),
        plt.Line2D([0], [0], color=binned_color, linestyle=best_style, label='Most Reg'),
        plt.Line2D([0], [0], color=binned_color, label='Binned EM-RV'),
        plt.Line2D([0], [0], color=em_color, label='EM-RV'),
        plt.Line2D([0], [0], color=raw_color, label='Raw RV'),
    ]
    axs[0].legend(handles=lines, loc='lower left')
    axs[0].grid(True, alpha=0.5)

    # Bottom plot: Normalized variance (divided by EM-corrected variance)
    # Binned rate variance (normalized)
    axs[1].plot(results_data['ep_bins'], 
            np.array(results_data['bin_rate_vars'])/results_data['em_corrected_var'], 'o-', 
            color=binned_color, linestyle=data_style, linewidth=3, markersize=10)
    axs[1].plot(results_best['ep_bins'], 
            np.array(results_best['bin_rate_vars'])/results_best['em_corrected_var'], 'o-', 
            color=binned_color, linestyle=best_style, linewidth=3, markersize=10)

    # Horizontal lines for normalized EM-corrected and raw rate variance
    axs[1].axhline(1.0, color=em_color, linestyle=data_style, zorder=0)  # EM-corrected normalized to 1.0
    axs[1].axhline(1.0, color=em_color, linestyle=best_style, zorder=0)
    axs[1].axhline(1.0, color=em_color, linestyle=mid_style, zorder=0)
    axs[1].axhline(1.0, color=em_color, linestyle=low_style, zorder=0)

    axs[1].axhline(results_data['rate_var']/results_data['em_corrected_var'], 
                color=raw_color, linestyle=data_style, zorder=0)
    axs[1].axhline(results_best['rate_var']/results_best['em_corrected_var'], 
                color=raw_color, linestyle=best_style, zorder=0)

    axs[1].set_xlabel('Eye Position Distance (degrees)')
    axs[1].set_ylabel('Normalized Variance')
    axs[1].grid(True, alpha=0.5)

    plt.tight_layout()
    plt.savefig(unit_cache / f'mcfarland_analysis_{cid}.png')
    plt.show()


# %%
# load mcfarland results and plot mean and sem for data and model fits
import glob
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# Find all mcfarland analysis files
mcfarland_files = list(cache.glob('*/mcfarland_*.npz'))
print(f"Found {len(mcfarland_files)} mcfarland analysis files")

# Initialize arrays to store binned data
all_ep_bins = []
all_data_norm_vars = []
all_model_norm_vars = []

# Load and process each file
for file_path in mcfarland_files:
    data = np.load(file_path, allow_pickle=True)
    results_data = data['results_data'].item()
    results_best = data['results_best'].item()
    
    # Store eye position bins
    all_ep_bins.append(results_data['ep_bins'])
    
    # Store normalized variances
    all_data_norm_vars.append(results_data['bin_rate_vars'] / results_data['em_corrected_var'])
    all_model_norm_vars.append(results_best['bin_rate_vars'] / results_best['em_corrected_var'])

# Find common x-axis bins by averaging bin positions across units
mean_ep_bins = np.mean(all_ep_bins, axis=0)

# Convert to arrays for easier computation
all_data_norm_vars = np.array(all_data_norm_vars)
all_model_norm_vars = np.array(all_model_norm_vars)

# Compute mean and SEM
data_mean = np.mean(all_data_norm_vars, axis=0)
data_sem = np.std(all_data_norm_vars, axis=0) / np.sqrt(len(all_data_norm_vars))

model_mean = np.mean(all_model_norm_vars, axis=0)
model_sem = np.std(all_model_norm_vars, axis=0) / np.sqrt(len(all_model_norm_vars))

#%% Plot population average
plt.figure(figsize=(10, 6))
plt.plot(mean_ep_bins, data_mean, 'o-', color='blue', label='Data', linewidth=2)
plt.fill_between(mean_ep_bins, data_mean - data_sem, data_mean + data_sem, color='blue', alpha=0.3)

plt.plot(mean_ep_bins, model_mean, 'o-', color='red', label='Standard Model', linewidth=2)
plt.fill_between(mean_ep_bins, model_mean - model_sem, model_mean + model_sem, color='red', alpha=0.3)

# Reference line at y=1 (normalized EM-corrected variance)
plt.axhline(1.0, color='black', linestyle='--', alpha=0.5)
plt.axhline(0.0, color='black', linestyle='--', alpha=0.5)

plt.xlabel('Eye Position Distance (degrees)')
plt.ylabel('Normalized Variance')
plt.title(f'Population Average Normalized Variance. N={len(mcfarland_files)}')
plt.legend()
plt.grid(True, alpha=0.5)
plt.tight_layout()
plt.savefig(cache / 'population_mcfarland_analysis.png')
plt.show()

#%%
# Also compute and plot the average alpha values
all_data_alphas = [np.load(f, allow_pickle=True)['results_data'].item()['alpha'] for f in mcfarland_files]
all_model_alphas = [np.load(f, allow_pickle=True)['results_best'].item()['alpha'] for f in mcfarland_files]

print(f"Data mean alpha: {np.mean(all_data_alphas):.3f} ± {np.std(all_data_alphas)/np.sqrt(len(all_data_alphas)):.3f}")
print(f"Model mean alpha: {np.mean(all_model_alphas):.3f} ± {np.std(all_model_alphas)/np.sqrt(len(all_model_alphas)):.3f}")

# Plot histogram of alpha values
plt.figure(figsize=(8, 6))
plt.hist(all_data_alphas, alpha=0.5, label='Data', bins=10, color='blue')
plt.hist(all_model_alphas, alpha=0.5, label='Standard Model', bins=10, color='red')
plt.xlim(0,.5)
plt.xlabel('Alpha Value')
plt.ylabel('Count')
plt.title('Distribution of Alpha Values')
plt.legend()
plt.grid(True, alpha=0.5)
plt.tight_layout()
plt.savefig(cache / 'population_alpha_distribution.png')
plt.show()

# %%
