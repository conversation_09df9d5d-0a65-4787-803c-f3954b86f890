#%%
from DataYatesV1 import (plot_stas, print_batch, LNPModel, 
                         fit_lnp_lbfgs, PoissonMaskedLoss, get_free_device,
                         PoissonBPSAggregator, CombinedEmbeddedDataset, split_inds_by_trial)
from tqdm import tqdm
from DataYatesV1 import get_session, DictDataset, CombinedEmbeddedDataset, MaskedLoss
import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from DataYatesV1 import enable_autoreload
from copy import deepcopy
enable_autoreload()

from DataYatesV1.utils.modeling.general import get_valid_dfs
from torch.utils.data import DataLoader

subject = 'Allen'
date = '2022-04-13'
sess = get_session(subject, date)

device = get_free_device()

# %%

n_lags = 20
train_val_split = .6
batch_size = 4096
# cid = 113 # best complex cell
# cid = 98 # other options
# cid = 114
# cid = 116
# cid = 122
# cid = 39
#cid = 33 # simple / complex cell
cid = 101 # simple/complex cell
cid = 57 # lgn afferent
crop_radius = 15

cache = Path(f'/mnt/ssd/YatesMarmoV1/tmp/complex_study/{cid}')
cache.mkdir(parents=True, exist_ok=True)

def load_data(n_lags, train_val_split, cid):
    gaborium_dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
    gaborium_dset['stim'] = (gaborium_dset['stim'].float() - 127) / 255

    gaborium_dset['dfs'] = get_valid_dfs(gaborium_dset, n_lags)
    gaborium_dset['robs'] = gaborium_dset['robs'][:,[cid]]
    gaborium_inds = gaborium_dset['dfs'].squeeze().nonzero(as_tuple=True)[0]
    print(f'Gaborium dataset size: {len(gaborium_inds)} / {len(gaborium_dset)} ({len(gaborium_inds)/len(gaborium_dset)*100:.2f}%)')

    train_inds, val_inds = split_inds_by_trial(gaborium_dset, gaborium_inds, train_val_split)

    return gaborium_dset, train_inds, val_inds

gaborium_dset, train_inds, val_inds = load_data(
    n_lags, train_val_split, cid)

#%%

def calc_dset_sta(dset, inds, lags, modifier=lambda x: x, batch_size=2048, device='cpu', verbose = 0):
    if np.isscalar(lags):
        lags = np.arange(lags)

    keys_lags = {
        'robs': 0,
        'stim': lags,
    }

    if 'dfs' in dset:
        keys_lags['dfs'] = 0
    
    mu = dset['stim'][inds].mean()
    dset['stim'] -= mu

    n_spikes = dset['robs'][inds].sum()
    n_y, n_x = dset['stim'].shape[1:3]
    n_units = dset['robs'].shape[1]
    ce_dset = CombinedEmbeddedDataset(dset, inds, keys_lags)

    sta = torch.zeros((n_units, len(lags), n_y, n_x), device=device)
    it = range(0, len(ce_dset), batch_size)
    if verbose:
        it = tqdm(it, desc='Calculating STA')
    for i in it:
        batch = ce_dset[i:i+batch_size]
        batch = {k: v.to(device) for k, v in batch.items()}
        robs = batch['robs']
        stim = modifier(batch['stim'])
        
        sta += torch.einsum('tc, tlyx->clyx', robs, stim) / n_spikes
    sta = sta.to('cpu')
    dset['stim'] += mu
    return sta

def thresholded_centroid(img, threshold=.5):
    img = img - img.min()
    img = img / img.max()
    img[img < threshold] = 0
    n_i, n_j = img.shape
    i_centroid = (np.arange(n_i)[:,None] * img).sum() / img.sum()
    j_centroid = (np.arange(n_j)[None,:] * img).sum() / img.sum()
    return i_centroid, j_centroid

def crop_dataset(dset, inds, crop_radius, cache_dir):

    assert dset['robs'].shape[1] == 1, 'Only implemented for single unit'
    stas_file = cache_dir / f'stas_{cid}.pt'
    stes_file = cache_dir / f'stes_{cid}.pt'
    if stas_file.exists() and stes_file.exists():
        stas = torch.load(stas_file)
        stes = torch.load(stes_file)
    else:
        stas = calc_dset_sta(gaborium_dset, inds, n_lags, device=device, verbose=1)
        torch.save(stas, stas_file)
        stes = calc_dset_sta(gaborium_dset, inds, n_lags, modifier=lambda x: x**2, device=device, verbose=1)
        torch.save(stes, stes_file)
    stas = stas[0]
    stes = stes[0]
    
    energy_lags = stes.mean(dim=(1,2))
    max_lag = energy_lags.argmax()
    print(f'Max lag: {max_lag}')
    max_pix = thresholded_centroid(stes[max_lag].numpy())
    print(f'Max pix: {max_pix}')
    max_pix = np.round(max_pix).astype(int)
    roi = [[max_pix[0]-crop_radius, max_pix[0]+crop_radius+1], [max_pix[1]-crop_radius, max_pix[1]+crop_radius+1]]  
    
    fig, axs = plt.subplots(1, 2, figsize=(10, 5))
    axs[0].imshow(stas[max_lag].numpy(), cmap='gray')
    axs[0].scatter(max_pix[1], max_pix[0], c='r', s=100)
    axs[0].plot([roi[1][0], roi[1][1], roi[1][1], roi[1][0], roi[1][0]], 
                [roi[0][0], roi[0][0], roi[0][1], roi[0][1], roi[0][0]], c='r')
    axs[0].set_title('STA')
    axs[1].imshow(stes[max_lag].numpy(), cmap='gray')
    axs[1].scatter(max_pix[1], max_pix[0], c='r', s=100)
    axs[1].plot([roi[1][0], roi[1][1], roi[1][1], roi[1][0], roi[1][0]], 
                [roi[0][0], roi[0][0], roi[0][1], roi[0][1], roi[0][0]], c='r')
    plt.show()

    dset['stim'] = dset['stim'][:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]].contiguous()
    stas = stas[:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]]
    stes = stes[:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]]
    return dset, stas, stes, roi

gaborium_dset, sta, ste, roi = crop_dataset(gaborium_dset, train_inds, crop_radius, cache)
#%%

def get_initial_filters(sta, dset, inds, batch_size = 2048, n_steps=10, device='cpu'):
    filters = sta[None,...]

    plot_stas(filters[:,:,None], row_labels=['STA'])
    plt.show()

    filters = filters.to(device)

    n_lags = filters.shape[1]
    keys_lags = {
        'robs': 0,
        'stim': np.arange(n_lags),
    }
    train_dset = CombinedEmbeddedDataset(dset, inds, keys_lags, device)

    robs = []
    filter_outputs = []
    for i in tqdm(range(0, len(train_dset), batch_size)):
        batch = train_dset[i:i+batch_size]
        robs.append(batch['robs'])
        stim = batch['stim']
        filter_outputs.append(torch.einsum('tlyx, flyx->tf', stim, filters))
    robs = torch.cat(robs, dim=0)
    filter_outputs = torch.cat(filter_outputs, dim=0)

    def calc_nonlinearity_1d(gen, robs, n_bins=20):
        bin_edges = np.percentile(gen.cpu().numpy(), np.linspace(0, 100, 21))
        bins = (bin_edges[1:] + bin_edges[:-1]) / 2
        nonlin = torch.zeros(len(bins))
        for i in range(len(bins)):
            mask = (gen > bin_edges[i]) & (gen < bin_edges[i+1])
            nonlin[i] = robs[mask].mean()
        return bins, nonlin
        
    l_bins, l_nonlin = calc_nonlinearity_1d(filter_outputs[:,0], robs)

    plt.figure()
    plt.plot(l_bins, l_nonlin, label='Linear')
    plt.xlabel('Filter Output')
    plt.ylabel('spikes / bin')
    plt.show()

    stim = filter_outputs.clone()
    data = {
        'stim': stim,
        'robs': robs.to(device),
    }

    lnp_model = fit_lnp_lbfgs(data, 
                              n_steps=n_steps, 
                              lbfgs_kwargs={'lr': 1, 'max_iter': 1000, 'max_eval': 1000, 'tolerance_change': 1e-6, 'tolerance_grad': 1e-6},
                              device=device)


    init_filters = filters.clone()
    init_filters[0] *= lnp_model.kernel.detach()[0,0]
    init_filters = init_filters.to('cpu')

    init_bias = lnp_model.bias.detach()
    init_bias = init_bias.to('cpu')

    torch.cuda.empty_cache()
    return init_filters, init_bias

filters, bias = get_initial_filters(sta, 
                                    gaborium_dset, train_inds, 
                                    batch_size=2048, n_steps=10,
                                    device=device)


#%%
keys_lags = {
    'robs': 0,
    'stim': np.arange(n_lags),
}
train_data = CombinedEmbeddedDataset(gaborium_dset,
                                    train_inds,
                                    keys_lags,
                                    device)[:]

print_batch(train_data)

val_data = CombinedEmbeddedDataset(gaborium_dset,
                                   val_inds,
                                   keys_lags,
                                   'cpu')[:]
print_batch(val_data)
torch.cuda.empty_cache()

#%%

from DataYatesV1.utils.modeling.reg import l1, laplacian, locality_conv
class LNPModel(torch.nn.Module):
    def __init__(self, dims, n_units, base_fr=None, lap_xy=0, lap_t=0, local=0):
        super(LNPModel, self).__init__()
        self.bias = torch.nn.Parameter(torch.zeros(n_units))
        assert len(dims) == 3
        self.lap_xy = lap_xy
        self.lap_t = lap_t
        self.local = local

        n_lags, n_y, n_x = dims
        self.n_units = n_units
        self.dims = dims

        self.filter = nn.Parameter(torch.randn(n_units, n_lags, n_y, n_x))

        if base_fr is not None:
            self.bias.data = torch.log(torch.exp(base_fr) - 1)
        
    def reg_loss(self):
        reg_loss = 0
        if self.lap_xy > 0:
            reg_loss += self.lap_xy * laplacian(self.filter, dims=[-2, -1])
        if self.lap_t > 0:
            reg_loss += self.lap_t * laplacian(self.filter, dims=1)
        if self.local > 0:
            reg_loss += self.local * locality_conv(self.filter, dims=[-2, -1])
        return reg_loss

    def forward(self, x):
        # x['stim']: (batch, n_lags, H, W)
        n_samples = len(x['stim'])
        g = x['stim'].view(n_samples, -1) @ self.filter.view(self.n_units, -1).T
        g += self.bias
        x['rhat'] = F.softplus(g)
        return x

    def plot_weights(self):
        # Extract feature weights.
        # The feature weights are from a 1x1 conv: shape (n_units, in_channels, 1, 1)
        # Squeeze to (n_units, in_channels)
        weights = self.filter.detach().cpu().numpy().squeeze()
        print(weights.shape)
        l_max = np.max(np.abs(weights))
        n_lags = len(weights)

        fig, axs = plt.subplots(1, weights.shape[0], figsize=(n_lags, 1))
        for i in range(n_lags):
            axs[i].imshow(weights[i], cmap='coolwarm_r', interpolation='nearest', vmin=-l_max, vmax=l_max)
            axs[i].set_xlabel(f'{i}')
            if i == 0:
                axs[i].set_ylabel('Linear')
        for ax in axs.flatten():
            ax.set_xticks([])
            ax.set_yticks([])
        return fig, axs

#%%
from DataYatesV1 import calc_poisson_bits_per_spike

def evaluate_model(model, data):
    with torch.no_grad():
        out = model(data)
        bps = calc_poisson_bits_per_spike(out['rhat'], out['robs']).item()
    return bps

def fit_lnp_model(train_data, model, device='cuda:0', batch_size=4096, 
                       loss_tol=1e-6, max_steps=10):
                       
    model.to(device)
    loss_fn = PoissonMaskedLoss()
    
    optim = torch.optim.LBFGS(model.parameters(), 
                            history_size=10,
                            max_iter=500,
                            tolerance_change=1e-6,
                            line_search_fn=None,
                            tolerance_grad=1e-6)

    def closure(pbar=None):
        optim.zero_grad()
        n_samples = len(train_data['robs'])
        total_loss = 0
        
        # Process in batches to avoid OOM
        for i in range(0, n_samples, batch_size):
            # Create batch with explicit device placement
            batch = {key: val[i:i+batch_size].to(device) for key, val in train_data.items()}
            
            # Forward pass
            out = model(batch)
            
            # Calculate batch loss (don't call backward yet)
            batch_loss = loss_fn({'rhat': out['rhat'], 'robs': batch['robs']})
            
            # Scale loss by batch proportion and accumulate
            batch_fraction = len(batch['robs']) / n_samples
            batch_loss = batch_loss * batch_fraction
            batch_loss.backward()
            
            total_loss += batch_loss.item()
            
        
        # Add regularization loss and backward
        reg_loss = model.reg_loss()
        reg_loss.backward()
        total_loss += reg_loss.item()
        
        if pbar is not None:
            pbar.update(1)
            
        return total_loss

    
    train_losses = []

    for i in range(max_steps): 
        with tqdm(desc=f'Epoch {i}') as pbar:
            train_loss = optim.step(lambda: closure(pbar))
        train_losses.append(float(train_loss))
        status = f'\tTrain loss {i}: {train_loss:.4e}'
        if i == 0:
            print(status)
        else:
            print(f'{status} ({train_losses[-1] - train_losses[-2]:.4e})')
            if train_losses[-1] - train_losses[-2] > 0:
                print(f'Loss diverging, stopping training')
                return False
            elif abs(train_losses[-1] - train_losses[-2]) < loss_tol:
                print(f'Loss converged, stopping training')
                return True
    return True

reg = {
    'lap_xy': 3e-3,
    'lap_t': 1e-3,
    'local': 10,#33,
}

lnp = LNPModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1,
                    **reg)
# init weights
lnp.filter.data = filters[0].unsqueeze(0)
lnp.bias.data = bias
_ = lnp.plot_weights()
plt.show()

lnp.to('cpu')
print(evaluate_model(lnp, val_data))

batch_size = 2**12
print(f'Fitting with batch size {batch_size}')
fit_lnp_model(train_data, lnp, device=device, batch_size=batch_size)
lnp.to('cpu')
print(evaluate_model(lnp, val_data))

_ = lnp.plot_weights()
plt.show()

# %%
import optuna
from optuna.samplers import TPESampler

def objective(trial):
    """Objective function for Optuna hyperparameter optimization."""
    # Define hyperparameter search space
    reg = {
        'lap_xy': trial.suggest_float('lap_xy', 1e-10, 1e-1, log=True),
        'lap_t': trial.suggest_float('lap_t', 1e-10, 1e-1, log=True),
        'local': trial.suggest_float('local', 1e-1, 1e3, log=True)
    }
    
    # Create model with trial hyperparameters
    model = LNPModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1, **reg)
    
    # Initialize weights
    model.filter.data = filters[0].unsqueeze(0)
    model.bias.data = bias
    
    # Train model
    batch_size = 2**12
    res = fit_lnp_model(train_data, model, device=device, batch_size=batch_size)
    if not res:
        optuna.TrialPruned()
    
    # Evaluate on validation set
    model.to('cpu')
    val_bps = evaluate_model(model, val_data)
    
    return val_bps  # We want to maximize bits per spike

# Create study
n_trials = 30
n_startup = n_trials // 2
study_name = f'lnp_model_{cid}'
storage_file = cache / f'{study_name}.db'
storage_str = 'sqlite:///' + str(storage_file)

if storage_file.exists():
    study = optuna.load_study(study_name=study_name, storage=storage_str)
    if len(study.trials) < n_trials:
        print(f'Found {len(study.trials)} trials in study, but {n_trials} were requested. Recreating study...')
        storage_file.unlink()

if not storage_file.exists():
    study = optuna.create_study(
        direction='maximize',  # Maximize bits per spike
        sampler=TPESampler(seed=1002, multivariate=True, n_startup_trials=n_startup),
        study_name=study_name,
        storage=storage_str,
    )
    study.optimize(objective, n_trials=n_trials)

# Plot optimization history
plt.figure(figsize=(10, 6))
optuna.visualization.matplotlib.plot_optimization_history(study)
plt.title('Optimization History')
plt.show()

# Plot parameter importances
plt.figure(figsize=(10, 6))
optuna.visualization.matplotlib.plot_param_importances(study)
plt.title('Parameter Importances')
plt.show()

plt.style.use('default')

#%%

# McFarland Variance decomposition
trials = study.get_trials()
values = [t.value for t in trials]
order = np.argsort(values)
values = np.array(values)[order]
trials = [trials[i] for i in order]

top_percentile = 20
n_top_trials = int(len(trials) * top_percentile / 100)
print(f'Top {top_percentile}% of trials: {n_top_trials}')
top_trials = trials[-n_top_trials:]
lap_xys = np.log10(np.array([trial.params['lap_xy'] for trial in top_trials]))
min_lap_xy = np.min(lap_xys)
lap_xys -= min_lap_xy
lap_ts = np.log10(np.array([trial.params['lap_t'] for trial in top_trials]))
min_lap_t = np.min(lap_ts)
lap_ts -= min_lap_t
locals = np.log10(np.array([trial.params['local'] for trial in top_trials]))
min_local = np.min(locals)
locals -= min_local

plt.figure()
plt.scatter(lap_xys, lap_ts, c=locals)
plt.xlabel('log10(lap_xy)')
plt.ylabel('log10(lap_t)')
plt.colorbar(label='log10(local)')
plt.title(f'Top {n_top_trials} trials. \n Origin is {min_lap_xy:.2f}, {min_lap_t:.2f}, {min_local:.2f}')
plt.show()
reg_amount = np.sqrt(lap_xys**2 + lap_ts**2 + locals**2)
most_reg_trial = top_trials[np.argmax(reg_amount)]

most_reg = most_reg_trial.params
mid_reg = deepcopy(most_reg)
mid_reg['local'] /= 10
low_reg = deepcopy(most_reg)
low_reg['local'] /= 100
print(f'Most regularized trial: {most_reg}')

# %%
init_model = LNPModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1,
                    **most_reg)
# init weights
init_model.filter.data = filters[0].unsqueeze(0)
init_model.bias.data = bias
init_bps = evaluate_model(init_model, val_data)
print(f"Initial model BPS: {init_bps:.4f}")

# fit  model
fit_model = LNPModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1,
                    **most_reg)


# init weights
fit_model.filter.data = filters[0].unsqueeze(0)
fit_model.bias.data = bias
fit_lnp_model(train_data, fit_model, device=device, batch_size=batch_size)
fit_model.to('cpu')
fit_bps = evaluate_model(fit_model, val_data)
print(f"Fit model BPS: {fit_bps:.4f}")

#%%
init_filters = init_model.filter.data.detach().cpu().numpy().squeeze()
init_max = np.max(np.abs(init_filters))
fit_filters = fit_model.filter.data.detach().cpu().numpy().squeeze()
fit_max = np.max(np.abs(fit_filters))
fig, axs = plt.subplots(2, n_lags, figsize=(n_lags, 3))
for i in range(n_lags):
    axs[0, i].imshow(init_filters[i], cmap='coolwarm', interpolation='nearest', vmin=-init_max, vmax=init_max)
    axs[1, i].imshow(fit_filters[i], cmap='coolwarm', interpolation='nearest', vmin=-fit_max, vmax=fit_max)
    for ax in axs[:, i]:
        ax.set_xticks([])
        ax.set_yticks([])
    axs[1,i].set_xlabel(f'lag {i}')
axs[0,0].set_ylabel('Initial')
axs[1,0].set_ylabel('Fit')
fig.suptitle(f'Initial vs Fit Weights\nLap XY: {most_reg["lap_xy"]:.2e}, Lap T: {most_reg["lap_t"]:.2e}, Local: {most_reg["local"]:.2e}\nInitial BPS: {init_bps:.4f}, Fit BPS: {fit_bps:.4f}')
plt.tight_layout()
plt.show()

# %%
