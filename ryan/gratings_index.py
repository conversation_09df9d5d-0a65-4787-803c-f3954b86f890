#%%
from DataYatesV1 import (plot_stas, print_batch, LNPModel, 
                         fit_lnp_lbfgs, PoissonMaskedLoss, get_free_device,
                         CombinedEmbeddedDataset, split_inds_by_trial,
                         calc_dset_sta, calc_dset_stc, thresholded_centroid,
                         ensure_tensor)

from DataYatesV1.models.losses import PoissonBPSAggregator, MaskedLoss, calc_poisson_bits_per_spike

import optuna
from optuna.samplers import TPESampler
from tqdm import tqdm
from DataYatesV1 import get_session, DictDataset, CombinedEmbeddedDataset, get_gaborium_sta_ste
import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from DataYatesV1 import enable_autoreload
from copy import deepcopy
import gc
enable_autoreload()

from DataYatesV1.utils.modeling.general import get_valid_dfs
from torch.utils.data import DataLoader

subject = 'Allen'
date = '2022-04-13'
sess = get_session(subject, date)

device = get_free_device(1)

#%%
# inclusion criteria
# 1) Good isolation (refractory + amp truncation)
# 2) Orientation selective (from gratings)
# 3) enough spikes in fixrsvp
# NOTE: picked by hand for now

cids = np.array([33, 89, 92, 98, 101, 113, 116, 122, 126])

n_lags = 20
dt = 1/240
lags = np.arange(n_lags) * dt

# %%

train_val_split = .6
batch_size = 4096
crop_radius = 15
def load_data(sess, n_lags, train_val_split):
    gratings_dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gratings_shifted.dset')
    gratings_dset['stim'] = (gratings_dset['stim'].float() - 127) / 255

    gratings_dset['dfs'] = get_valid_dfs(gratings_dset, n_lags)
    #gratings_dset['robs'] = gratings_dset['robs'][:,[cid]]
    gratings_inds = gratings_dset['dfs'].squeeze().nonzero(as_tuple=True)[0]
    print(f'Gratings dataset size: {len(gratings_inds)} / {len(gratings_dset)} ({len(gratings_inds)/len(gratings_dset)*100:.2f}%)')

    train_inds, val_inds = split_inds_by_trial(gratings_dset, gratings_inds, train_val_split)

    return gratings_dset, train_inds, val_inds

gratings_dset, train_inds, val_inds = load_data(
    sess, n_lags, train_val_split)

cid = 101
#%%

from DataYatesV1 import calc_sta

gratings_robs = gratings_dset['robs'].numpy()
sf = gratings_dset['sf'].numpy()
sfs = np.unique(sf)
ori = gratings_dset['ori'].numpy()
oris = np.unique(ori)
# one-hot embed sfs and oris
sf_ori_one_hot = np.zeros((len(gratings_robs), len(sfs), len(oris)))
for i in range(len(gratings_robs)):
    sf_idx = np.where(sfs == sf[i])[0][0]
    ori_idx = np.where(oris == ori[i])[0][0]
    sf_ori_one_hot[i, sf_idx, ori_idx] = 1

from DataYatesV1.utils.rf import calc_sta
from DataYatesV1 import plot_stas
gratings_sta = calc_sta(sf_ori_one_hot, gratings_robs.astype(np.float64), 
                        n_lags, dfs=gratings_dset['dfs'].numpy().squeeze(),
               reverse_correlate=False, progress=True).numpy() / dt

plot_stas(gratings_sta[cid][None, :, None, :, :])

#%%

sf_sta = calc_sta(sf_ori_one_hot.sum(2, keepdims=True), gratings_robs.astype(np.float64), 
                        n_lags, dfs=gratings_dset['dfs'].numpy().squeeze(),
               reverse_correlate=False, progress=True).numpy().squeeze() / dt

#%%

temporal_tuning = np.linalg.norm(sf_sta, axis=2)
print(temporal_tuning.shape)
peak_lags = np.argmax(temporal_tuning, axis=1)
print(peak_lags)
sf_tuning = np.linalg.norm(sf_sta[:,peak_lags], axis=1)
print(sf_tuning.shape)
peak_sf = np.argmax(sf_tuning, axis=1)
print(peak_sf)
sf_snr = sf_tuning[np.arange(len(sf_tuning)), peak_sf] / np.mean(sf_tuning, axis=1)
print(sf_snr.shape)
ori_tuning = gratings_sta[np.arange(len(gratings_sta)), peak_lags, peak_sf]
peak_ori = np.argmax(ori_tuning, axis=1)
ori_snr = ori_tuning[np.arange(len(ori_tuning)), peak_ori] / np.mean(ori_tuning, axis=1)
print(ori_snr.shape)

#%%

phases = []
spikes = []
for iU in tqdm(range(len(gratings_sta))):
    sf_idx = peak_sf[iU]
    ori_idx = peak_ori[iU]
    lag = peak_lags[iU]

    sf_ori_idx = np.where(sf_ori_one_hot[:, sf_idx, ori_idx] > 0)[0]
    sf_ori_idx = sf_ori_idx[(sf_ori_idx + lag) < len(gratings_robs)] # only keep indices that have enough frames after lag

    # Compute phase for each frame
    stim_phases = gratings_dset['stim_phase'][sf_ori_idx].numpy().squeeze()
    _,n_y, n_x = stim_phases.shape
    stim_phases = stim_phases[:,n_y//2, n_x//2]  # take center pixel as phase
    # NOTE: Could center on STA / STE peak but in practice it doesn't matter
    stim_spikes = gratings_robs[sf_ori_idx + lag, iU]
    filters = gratings_dset['dfs'].numpy().squeeze()[sf_ori_idx + lag]  # use the same indices as spikes

    invalid = (stim_phases <= 0) | (filters == 0) # -1 indicates off screen or probe, 0 indicates sampled out of ROI
    stim_phases = stim_phases[~invalid]
    stim_spikes = stim_spikes[~invalid]
    phases.append(stim_phases)
    spikes.append(stim_spikes)

#%%
n_phase_bins = 8
phase_bin_edges = np.linspace(0, 2*np.pi, n_phase_bins + 1)
phase_bins = np.rad2deg((phase_bin_edges[:-1] + phase_bin_edges[1:]) / 2)
n_phases = np.zeros((len(gratings_sta), n_phase_bins))
n_spikes = np.zeros((len(gratings_sta), n_phase_bins))
phase_response = np.zeros((len(gratings_sta), n_phase_bins))
phase_response_ste = np.zeros((len(gratings_sta), n_phase_bins))
for iU in tqdm(range(len(gratings_sta))):
    unit_phases = phases[iU]
    unit_spikes = spikes[iU]
    # Count spikes per phase bin
    phase_bin_inds = np.digitize(unit_phases, phase_bin_edges) - 1  # bin index for each phase
    for i in range(n_phase_bins):
        n_phases[iU, i] = np.sum(phase_bin_inds == i)
        n_spikes[iU, i] = unit_spikes[phase_bin_inds == i].sum() / dt
        phase_response_ste[iU, i] = unit_spikes[phase_bin_inds == i].std() / np.sqrt(n_phases[iU,i]) / dt
    phase_response[iU] = n_spikes[iU] / n_phases[iU]

#%%
iU = 101
plt.figure()
plt.scatter(phases[iU], spikes[iU])
plt.show()

#%%
import numpy as np

def fit_sine(phases, spikes, omega=1.0, variance_source='observed_y'):
    """
    Fits a sine wave of the form f(x) = K*sin(omega*x) + L*cos(omega*x) + C
    (which can be rewritten as A*sin(omega*x + phi_0) + C) to the data
    using ordinary least squares (OLS). It then calculates the standard errors
    of the fitted parameters (K, L, C), derived quantities (Amplitude A,
    Phase offset phi_0), and a Modulation Index (MI).

    The method for calculating parameter variances depends on the 'variance_source'
    argument. For 'observed_y' or 'fitted_y', it uses a heteroscedasticity-
    consistent covariance estimator based on the idea that if beta_hat = M * y,
    then Cov(beta_hat) = M * Cov(y) * M^T, where Var(y_i) is approximated.
    For 'mse', it uses the standard OLS homoscedastic assumption.

    Parameters:
    -----------
    phases : array_like
        Observed phase values (x-values). These are typically in radians.
        If omega is not 1.0, these are the raw phase values, and the function
        will compute omega*phases.
    spikes : array_like
        Observed spike counts (y-values) corresponding to each phase.
    omega : float, optional
        Angular frequency of the sine wave (default is 1.0). If 'phases'
        already represent omega*x (i.e., phases are pre-multiplied by omega),
        then omega should be set to 1.0.
    variance_source : str, optional
        Method for estimating the variance of observations (Var(y_i)) when
        calculating the standard errors of the fitted parameters.
        - 'observed_y': Var(y_i) is approximated by y_i (the observed spike count).
                          This is suitable for Poisson-like data. Values are clipped
                          at a minimum of 1e-6 to prevent issues with zero counts. (Default)
        - 'fitted_y': Var(y_i) is approximated by the fitted y_i value. Clipped at 1e-6.
        - 'mse': Assumes homoscedastic errors (Var(y_i) = constant sigma_e^2).
                 sigma_e^2 is estimated by the Mean Squared Error (MSE) from OLS.
                 The covariance matrix of parameters is then MSE * (X^T X)^-1.

    Returns:
    --------
    dict
        A dictionary containing the fitted parameters, their standard errors,
        and other relevant statistics:
        - 'K': float, coefficient for the sin(omega*x) term.
        - 'L': float, coefficient for the cos(omega*x) term.
        - 'C': float, constant offset (baseline).
        - 'K_se': float, standard error of K.
        - 'L_se': float, standard error of L.
        - 'C_se': float, standard error of C.
        - 'amplitude': float, Amplitude A = sqrt(K^2 + L^2).
        - 'amplitude_se': float, Standard error of A, calculated via error propagation.
        - 'phase_offset_rad': float, Phase offset phi_0 = atan2(L, K) in radians.
                                The model can be written as A*sin(omega*x + phi_0) + C.
        - 'phase_offset_rad_se': float, Standard error of phi_0, via error propagation.
        - 'modulation_index': float, Modulation Index MI = 2A / (A+C).
                                NaN if A+C is close to zero, or if A or C is NaN.
        - 'modulation_index_se': float, Standard error of MI, via error propagation.
                                   NaN if MI is NaN or its variance cannot be computed.
        - 'y_fit': numpy.ndarray, The fitted spike counts (predicted y-values).
        - 'R_squared': float, Coefficient of determination (goodness of fit).
        - 'covariance_matrix_params': numpy.ndarray, The covariance matrix for [K, L, C].
        - 'condition_number_XTX': float, Condition number of the X^T*X matrix.
                                     High values (>1e8 to 1e10) may indicate
                                     multicollinearity and unstable parameter estimates.
    Raises:
    -------
    ValueError:
        If the number of data points is less than the number of parameters (3),
        if the X^T*X matrix is singular (e.g., phases are not distinct enough),
        or if 'variance_source' is an invalid option.
    """
    # Ensure inputs are numpy arrays for consistent operations
    phases = np.asarray(phases)
    spikes = np.asarray(spikes)
    n_points = len(phases)

    # Check if there are enough data points to fit the three parameters (K, L, C)
    if n_points < 3:
        raise ValueError("At least 3 data points are required to fit K, L, and C.")

    # Construct the design matrix X for the OLS regression.
    # The model is y = K*sin(omega*x) + L*cos(omega*x) + C*1
    # So, each row in X corresponds to an observation (phase, spike_count)
    # and columns are [sin(omega*phase_i), cos(omega*phase_i), 1]
    X = np.vstack([
        np.sin(omega * phases),  # First regressor: sin(omega*x)
        np.cos(omega * phases),  # Second regressor: cos(omega*x)
        np.ones(n_points)        # Third regressor: constant term (for C)
    ]).T  # Transpose to get observations in rows, regressors in columns

    # Calculate X^T * X (often denoted XTX)
    # This matrix is crucial for OLS parameter estimation.
    XTX = X.T @ X  # '@' is the matrix multiplication operator in Python 3.5+

    # Calculate the condition number of XTX.
    # The condition number gives an indication of the sensitivity of the solution
    # of a linear system to errors in the data. A high condition number
    # (e.g., > 1e8 or 1e10) suggests multicollinearity, meaning the regressors
    # are highly correlated, which can lead to unstable parameter estimates.
    condition_number_XTX = np.linalg.cond(XTX)
    if condition_number_XTX > 1e10: # A common threshold for concern
        print(f"Warning: Condition number of X^T*X is high ({condition_number_XTX:.2e}), "
              "results might be unstable. This can happen if phases are not well distributed "
              "or if omega is chosen such that sin(omega*x) and cos(omega*x) become "
              "linearly dependent for the given phases.")

    # Calculate the inverse of XTX, (X^T * X)^-1
    # This is needed for both parameter estimation and their covariance matrix.
    try:
        XTX_inv = np.linalg.inv(XTX)
    except np.linalg.LinAlgError:
        # This error occurs if XTX is singular (or nearly singular), meaning it cannot be inverted.
        # This typically happens if the columns of X are linearly dependent (perfect multicollinearity).
        # For example, if all phase values are the same or separated by multiples of 2pi/omega.
        raise ValueError("X^T*X matrix is singular. Cannot compute fit. "
                         "Check if phase values are sufficiently distinct and well-distributed.")

    # --- OLS Parameter Estimation ---
    # The OLS estimator for beta = [K, L, C]^T is beta_hat = (X^T*X)^-1 * X^T * y
    beta_hat = XTX_inv @ X.T @ spikes
    K, L, C = beta_hat[0], beta_hat[1], beta_hat[2]

    # Calculate the fitted y values (y_fit = X * beta_hat)
    y_fit = X @ beta_hat

    # --- Goodness of Fit: R-squared ---
    # R-squared = 1 - (Sum of Squared Residuals / Total Sum of Squares)
    # Sum of Squared Residuals (SSR) = sum((y_i - y_fit_i)^2)
    ss_residual = np.sum((spikes - y_fit)**2)
    # Total Sum of Squares (SST) = sum((y_i - mean(y))^2)
    ss_total = np.sum((spikes - np.mean(spikes))**2)
    
    if ss_total == 0:
        # Handle the case where all spike counts are the same.
        # If ss_total is 0, it means all y values are identical.
        # If ss_residual is also (close to) 0, the model fits perfectly (R^2=1).
        # Otherwise, the model does not explain any variance (R^2=0 can be ambiguous here,
        # but usually implies ss_residual > 0 if ss_total is 0, unless y_fit also all same).
        # A robust way is to say if residual is also zero, R2 is 1, else 0.
        r_squared = 1.0 if ss_residual < 1e-9 else 0.0 # Using a small tolerance for float comparison
    else:
        r_squared = 1.0 - (ss_residual / ss_total)

    # --- Covariance Matrix of Parameters [K, L, C] ---
    # This matrix provides variances of K, L, C on its diagonal,
    # and covariances (e.g., Cov(K,L)) on its off-diagonal.
    if variance_source == 'mse':
        # Homoscedastic assumption: Var(y_i) = sigma_e^2 (constant error variance)
        # Estimate sigma_e^2 using Mean Squared Error (MSE)
        # MSE = SSR / (degrees of freedom)
        # Degrees of freedom = n_points - number_of_parameters (which is 3: K, L, C)
        if n_points <= 3:
            # Not enough degrees of freedom to estimate MSE reliably.
            covariance_matrix_params = np.full((3,3), np.nan) # Fill with NaNs
            print("Warning: Cannot estimate MSE with <=3 data points for 3 parameters. "
                  "Parameter SEs will be NaN.")
        else:
            mse = ss_residual / (n_points - 3)
            # Cov(beta_hat) = MSE * (X^T*X)^-1
            covariance_matrix_params = mse * XTX_inv
    elif variance_source in ['observed_y', 'fitted_y']:
        # Heteroscedasticity-consistent covariance matrix (Eicker-Huber-White type)
        # Assumes errors can have non-constant variance.
        # Cov(beta_hat) = (X^T*X)^-1 * X^T * D * X * ((X^T*X)^-1)^T
        # where D is a diagonal matrix with Var(y_i) on the diagonal.
        # The formula can also be written as M * D * M^T where M = (X^T*X)^-1 * X^T.
        
        if variance_source == 'observed_y':
            # Approximate Var(y_i) = y_i (suitable for Poisson-distributed data)
            # Clip at a small positive value to avoid issues if y_i=0 (Var(0)=0 can be problematic).
            var_y_i = np.maximum(1e-6, spikes)
        else: # variance_source == 'fitted_y'
            # Approximate Var(y_i) = fitted_y_i
            var_y_i = np.maximum(1e-6, y_fit)
            
        D = np.diag(var_y_i) # Diagonal matrix of individual observation variances
        
        # M = (X^T*X)^-1 * X^T
        M = XTX_inv @ X.T
        # Cov(beta_hat) = M * D * M^T
        covariance_matrix_params = M @ D @ M.T
    else:
        raise ValueError("Invalid variance_source. Choose 'observed_y', 'fitted_y', or 'mse'.")

    # Extract variances of K, L, C from the diagonal of the covariance matrix
    var_K_val = covariance_matrix_params[0, 0]
    var_L_val = covariance_matrix_params[1, 1]
    var_C_val = covariance_matrix_params[2, 2]
    
    # Standard errors are the square roots of these variances.
    # Ensure variance is non-negative before taking sqrt; otherwise, SE is NaN.
    K_se = np.sqrt(var_K_val) if var_K_val >= 0 else np.nan
    L_se = np.sqrt(var_L_val) if var_L_val >= 0 else np.nan
    C_se = np.sqrt(var_C_val) if var_C_val >= 0 else np.nan

    # --- Amplitude (A) and its Standard Error ---
    # A = sqrt(K^2 + L^2)
    amplitude = np.sqrt(K**2 + L**2)
    amplitude_se = np.nan # Initialize to NaN
    var_A = np.nan        # Initialize variance of A to NaN

    # For error propagation, we need derivatives of A with respect to K and L.
    # dA/dK = K / sqrt(K^2+L^2) = K / A
    # dA/dL = L / sqrt(K^2+L^2) = L / A
    # To avoid division by zero if A is very small, use a clipped amplitude_denom.
    amplitude_denom = max(amplitude, 1e-9) 

    dAdK = K / amplitude_denom
    dAdL = L / amplitude_denom
    
    # Check if component variances/covariance are NaN (e.g., if MSE calculation failed)
    if np.isnan(var_K_val) or np.isnan(var_L_val) or np.isnan(covariance_matrix_params[0,1]):
        # var_A will remain NaN, and thus amplitude_se will remain NaN
        pass
    else:
        cov_KL = covariance_matrix_params[0, 1] # Covariance between K and L
        # Var(A) approx (dA/dK)^2*Var(K) + (dA/dL)^2*Var(L) + 2*(dA/dK)*(dA/dL)*Cov(K,L)
        var_A = (dAdK**2 * var_K_val) + \
                (dAdL**2 * var_L_val) + \
                (2 * dAdK * dAdL * cov_KL)
        if var_A >= 0:
            amplitude_se = np.sqrt(var_A)
        # If var_A computed is negative (due to numerical issues or model misspecification),
        # amplitude_se remains NaN.

    # Warning if amplitude is very small, as its SE might be unreliable.
    if amplitude < 1e-9 and not np.isnan(amplitude): # Check if amplitude itself isn't already NaN
         print("Warning: Amplitude is close to zero. Standard error for amplitude and phase may be unreliable or NaN.")

    # --- Phase Offset (phi_0) and its Standard Error ---
    # The model can be written as A*sin(omega*x + phi_0) + C.
    # K = A*cos(phi_0), L = A*sin(phi_0) => phi_0 = atan2(L, K)
    # atan2 is used for numerical stability and correct quadrant.
    phase_offset_rad = np.arctan2(L, K)
    phase_offset_rad_se = np.nan # Initialize to NaN
    
    # For error propagation, derivatives of phi_0 w.r.t. K and L:
    # d(phi_0)/dK = -L / (K^2+L^2) = -L / A^2
    # d(phi_0)/dL =  K / (K^2+L^2) =  K / A^2
    # Use amplitude_denom^2 for A^2 to avoid division by zero.
    amplitude_sq_denom = amplitude_denom**2

    dphidK = -L / amplitude_sq_denom
    dphidL = K / amplitude_sq_denom

    if np.isnan(var_K_val) or np.isnan(var_L_val) or np.isnan(covariance_matrix_params[0,1]):
        # phase_offset_rad_se will remain NaN
        pass
    else:
        cov_KL = covariance_matrix_params[0, 1] # Cov(K,L)
        # Var(phi_0) approx (dphi/dK)^2*Var(K) + (dphi/dL)^2*Var(L) + 2*(dphi/dK)*(dphi/dL)*Cov(K,L)
        var_phi0 = (dphidK**2 * var_K_val) + \
                   (dphidL**2 * var_L_val) + \
                   (2 * dphidK * dphidL * cov_KL)
        if var_phi0 >= 0:
            phase_offset_rad_se = np.sqrt(var_phi0)
        # If var_phi0 is negative, phase_offset_rad_se remains NaN.


    # --- Modulation Index (MI) and its Standard Error ---
    # MI = 2*A / (A+C)
    modulation_index = np.nan
    modulation_index_se = np.nan
    cov_AC = np.nan # Covariance between Amplitude (A) and Offset (C)

    # Calculate Cov(A,C) using error propagation:
    # Cov(A,C) approx (dA/dK)*Cov(K,C) + (dA/dL)*Cov(L,C)
    # dAdK and dAdL were computed earlier for amplitude_se.
    if not (np.isnan(K) or np.isnan(L) or \
            np.isnan(covariance_matrix_params[0,2]) or np.isnan(covariance_matrix_params[1,2]) or \
            np.isnan(dAdK) or np.isnan(dAdL) ): # dAdK/L can be NaN if K/L are NaN or amplitude is NaN initially
        cov_KC = covariance_matrix_params[0, 2]  # Cov(K, C)
        cov_LC = covariance_matrix_params[1, 2]  # Cov(L, C)
        cov_AC = dAdK * cov_KC + dAdL * cov_LC
    
    # Proceed if amplitude and C are valid numbers
    if np.isnan(amplitude) or np.isnan(C):
        # MI and MI_se remain NaN if A or C is NaN (e.g. due to upstream NaN K,L)
        pass # This state implies that K,L,C or their SEs might already be NaN
    else:
        # Denominator for MI: A+C
        denom_MI_val = amplitude + C
        
        # Check if A+C is too small (close to zero).
        # Since A (amplitude) >= 0 and C (offset, typically mean firing rate) >= 0,
        # A+C is usually non-negative.
        if denom_MI_val < 1e-9: 
            print(f"Warning: Sum of amplitude ({amplitude:.2e}) and C ({C:.2e}) is close to zero. "
                  "Modulation index and its SE are set to NaN.")
            # modulation_index and modulation_index_se remain np.nan
        else:
            # Calculate Modulation Index
            modulation_index = (2 * amplitude) / denom_MI_val
            
            # For standard error of MI, calculate partial derivatives:
            # d(MI)/dA = 2*C / (A+C)^2
            # d(MI)/dC = -2*A / (A+C)^2
            denom_MI_sq = denom_MI_val**2 # (A+C)^2
            dMI_dA = (2 * C) / denom_MI_sq
            dMI_dC = (-2 * amplitude) / denom_MI_sq

            # Check if required variances (Var(A), Var(C)) and Cov(A,C) are available (not NaN)
            # var_A was computed for amplitude_se
            # var_C_val is covariance_matrix_params[2,2] (variance of C)
            if np.isnan(var_A) or np.isnan(var_C_val) or np.isnan(cov_AC):
                # modulation_index_se remains NaN
                pass
            else:
                # Var(MI) approx (dMI/dA)^2*Var(A) + (dMI/dC)^2*Var(C) + 2*(dMI/dA)*(dMI/dC)*Cov(A,C)
                var_modulation_index = (dMI_dA**2 * var_A) + \
                                   (dMI_dC**2 * var_C_val) + \
                                   (2 * dMI_dA * dMI_dC * cov_AC)
                
                if var_modulation_index >= 0:
                    modulation_index_se = np.sqrt(var_modulation_index)
                else:
                    # This can happen due to numerical instability or if the model is ill-conditioned.
                    if not np.isnan(var_modulation_index): # Only print if it's a negative number, not already NaN
                        print(f"Warning: Calculated variance for modulation index is negative ({var_modulation_index:.2e}). "
                              "Setting SE to NaN. This may indicate issues with model stability or covariance estimates.")
                    # modulation_index_se remains NaN (or its initial np.nan state)
                    
    # --- Return Results ---
    return {
        'K': K, 'L': L, 'C': C,
        'K_se': K_se, 'L_se': L_se, 'C_se': C_se,
        'amplitude': amplitude, 'amplitude_se': amplitude_se,
        'phase_offset_rad': phase_offset_rad, 'phase_offset_rad_se': phase_offset_rad_se,
        'modulation_index': modulation_index, 'modulation_index_se': modulation_index_se,
        'y_fit': y_fit, 'R_squared': r_squared,
        'covariance_matrix_params': covariance_matrix_params,
        'condition_number_XTX': condition_number_XTX
    }

results = []
for iU in tqdm(range(len(gratings_sta))):
    unit_phases = phases[iU]
    unit_spikes = spikes[iU]
    if np.sum(unit_spikes) < 50:
        results.append(None)
        continue
    results.append(fit_sine(unit_phases, unit_spikes, omega=1.0, variance_source='observed_y'))

#%%
for iU in range(len(results)):
    res = results[iU]
    if res is None:
        continue
    amp = res['amplitude']
    amp_se = res['amplitude_se']
    phase_offset = res['phase_offset_rad']
    phase_offset_se = res['phase_offset_rad_se']
    C = res['C']
    mi = res['modulation_index']
    mi_se = res['modulation_index_se']
    if np.isnan(mi) or np.isnan(mi_se) or np.isnan(amp) or np.isnan(amp_se) or np.isnan(phase_offset) or np.isnan(phase_offset_se):
        continue

    smoothed_phases = np.linspace(0, 2*np.pi, 100)
    smoothed_fit = amp * np.sin(smoothed_phases + phase_offset) + C
    smoothed_fit_max = (amp+amp_se) * np.sin(smoothed_phases + phase_offset) + C
    smoothed_fit_min = (amp-amp_se) * np.sin(smoothed_phases + phase_offset) + C

    plt.figure()
    plt.errorbar(phase_bins, phase_response[iU], yerr=phase_response_ste[iU], fmt='o-', ecolor='C0', capsize=5, zorder=0)
    plt.plot(np.rad2deg(smoothed_phases), smoothed_fit/dt, color='red')
    plt.fill_between(np.rad2deg(smoothed_phases), smoothed_fit_min/dt, smoothed_fit_max/dt, color='red', alpha=0.2)
    ylim = plt.ylim()
    plt.ylim([0, ylim[1]])
    plt.xlabel('Phase (radians)')
    plt.ylabel('Spikes / second')
    plt.title(f'Unit {iU}\nModulation Index {mi:.2f} +/- {mi_se:.2f}')
    plt.show()
    plt.close()

#%%
import json

ln_dir = '/mnt/ssd/YatesMarmoV1/standard_model_fits/ray_ln_sweep_6040_30trials_noleak_only_fixations3/Allen_2022-04-13'
energy_dir = '/mnt/ssd/YatesMarmoV1/standard_model_fits/ray_energy_sweep_6040_30trials_noleak_only_fixations3/Allen_2022-04-13'

ln_bps = []
for iU in tqdm(range(len(gratings_sta))):
    try:
        with open(f'{ln_dir}/cell_{iU}_best/results.json', 'r') as f:
            fit_results = json.load(f)
            ln_bps.append(fit_results['val_bps'])
    except Exception as e:
        print(f'Failed to load LN results for unit {iU}')
        print(e)
        ln_bps.append(np.nan)
ln_bps = np.array(ln_bps)

energy_bps = []
for iU in tqdm(range(len(gratings_sta))):
    try:
        with open(f'{energy_dir}/cell_{iU}_best/results.json', 'r') as f:
            fit_results = json.load(f)
            energy_bps.append(fit_results['val_bps'])
    except Exception as e:
        print(f'Failed to load energy results for unit {iU}')
        print(e)
        energy_bps.append(np.nan)
energy_bps = np.array(energy_bps)

#%%
lf1 = lambda ln, en: (ln - en) / 2 / np.max(np.abs(np.stack([ln, en], axis=0)), axis=0)
lf2 = lambda ln, en: ln - en
lf = lf1
linear_index = lf(ln_bps, energy_bps)

mi = np.array(
    [res['modulation_index'] if res is not None else np.nan for res in results]
)

mi_se = np.array(
    [res['modulation_index_se'] if res is not None else np.nan for res in results]
)

#%%
l = np.arange(-1, 3, .1)
e = np.arange(-1, 3, .1)
L, E = np.meshgrid(l, e)
Z = lf(L, E)
plt.figure()
plt.contourf(L, E, Z, levels=20)
plt.colorbar(label='Linear Index')
plt.title('Linear Index Function')
#%%
plt.figure()
plt.scatter(ln_bps, energy_bps, c=linear_index)
plt.colorbar(label='Linear Index')
plt.plot([-1, 3], [-1, 3], color='k', linestyle='--', alpha=.5)
plt.xlim(-1, 3)
plt.ylim(-1, 3)
plt.xlabel('LN BPS')
plt.ylabel('Energy BPS')
plt.show()
#%%
# import qc
inclusion = np.load(sess.sess_dir / 'inclusion' / 'inclusion.npz')

fig, axs = plt.subplots(1, 2, figsize=(12, 5))
sc1 = axs[0].scatter(ln_bps, energy_bps, c=inclusion['min_contam_pct'])
fig.colorbar(sc1, label='Min Contamination %', ax=axs[0])
axs[0].plot([-1, 3], [-1, 3], color='k', linestyle='--', alpha=.5)
axs[0].set_xlim(-1, 3)
axs[0].set_ylim(-1, 3)
axs[0].set_xlabel('LN BPS')
axs[0].set_ylabel('Energy BPS')
axs[0].set_title('Contamination %')

sc2 = axs[1].scatter(ln_bps, energy_bps, c=inclusion['med_miss_pct'])
fig.colorbar(sc2, label='Median Missing %', ax=axs[1])
axs[1].plot([-1, 3], [-1, 3], color='k', linestyle='--', alpha=.5)
axs[1].set_xlim(-1, 3)
axs[1].set_ylim(-1, 3)
axs[1].set_xlabel('LN BPS')
axs[1].set_ylabel('Energy BPS')
axs[1].set_title(f'Missing %\n(some units missing because fewer than 1000 spikes)')
plt.tight_layout()
plt.show()

#%%
plt.figure()
plt.scatter(ori_snr, sf_snr)
plt.axvline(2, color='r', linestyle='--', alpha=.5, label='Orientation inclusion threshold')
plt.xlabel('Orientation SNR')
plt.ylabel('Spatial Frequency SNR')
plt.legend()
plt.show()
#%%

inclusion_mask = (inclusion['med_miss_pct'] < 40) & (inclusion['min_contam_pct'] < 80) & (ori_snr > 2)
inclusion_mask = np.ones_like(inclusion_mask, dtype=bool)
nan_mask = np.isnan(linear_index) | np.isnan(mi) | np.isnan(mi_se)
inclusion_mask = inclusion_mask & (~nan_mask)
#%%
plt.figure()
plt.scatter(ln_bps[inclusion_mask], energy_bps[inclusion_mask], c=linear_index[inclusion_mask])
plt.colorbar(label='Linear Index')
plt.plot([-1, 3], [-1, 3], color='k', linestyle='--', alpha=.5)
plt.xlim(-1, 3)
plt.ylim(-1, 3)
plt.xlabel('LN BPS')
plt.ylabel('Energy BPS')
plt.show()
#%%
from scipy.stats import pearsonr
r, p = pearsonr(linear_index[inclusion_mask], mi[inclusion_mask])
plt.figure()
plt.scatter(linear_index[inclusion_mask], 
            mi[inclusion_mask], 
            c=np.max(np.stack([ln_bps, energy_bps])[:,inclusion_mask], axis=0), 
            cmap='viridis')
# add error bars on modulation index
for iU in range(len(linear_index)):
    if inclusion_mask[iU]:
        plt.errorbar(linear_index[iU], mi[iU], yerr=mi_se[iU], fmt='o', ecolor='k', capsize=3, alpha=.3,zorder=0)
        plt.text(linear_index[iU]+.015, mi[iU], f'{iU}', ha='left', va='center', fontsize=8, color='k', alpha=.9, zorder=10)
plt.xlabel('Linear Index')
plt.ylabel('Modulation Index')
plt.colorbar(label='Max BPS')
plt.title(f'r = {r:.2f}, p = {p:.2e}')
plt.show()

#%%
plt.figure()
plt.scatter(ori_snr, sf_snr)
plt.xlabel('Orientation SNR')
plt.ylabel('Spatial Frequency SNR')
plt.show()

#%%
order = np.argsort(ori_snr)[::-1]

for iU in range(10):
    fig, axs = plt.subplots(5, 1, figsize=(8, 14))
    axs[0].plot(lags, temporal_tuning[order[iU]])
    axs[0].set_title(f'Unit: {order[iU]}, Temporal tuning')
    axs[0].set_xlabel('Lag (frames)')
    axs[0].set_ylabel('Spikes / second')
    axs[1].plot(sfs, sf_tuning[order[iU]])
    axs[1].set_title(f'Spatial Frequency tuning')
    axs[1].set_xlabel('Spatial Frequency (cycles/degree)')
    axs[1].set_ylabel('Spikes / second')
    axs[2].plot(oris, ori_tuning[order[iU]])
    axs[2].set_title(f'Orientation tuning')
    axs[2].set_xlabel('Orientation (degrees)')
    axs[2].set_ylabel('Spikes / second')
    axs[3].bar(phase_bins, phase_response[order[iU]], width=360/n_phase_bins, align='center')
    axs[3].errorbar(phase_bins, phase_response[order[iU]], yerr=phase_response_ste[order[iU]], fmt='ro-', ecolor='red', capsize=5)
    axs[3].set_title(f'Phase tuning')
    axs[3].set_xlabel('Phase (degrees)')
    axs[3].set_ylabel('Spikes / second')
    im = axs[4].imshow(gratings_sta[order[iU], peak_lags[order[iU]], :, :] * 240, cmap='viridis')
    axs[4].set_title(f'Spatial Frequency and Orientation tuning')
    axs[4].set_xlabel('Orientation (degrees)')
    axs[4].set_ylabel('Spatial Frequency (cycles/degree)')
    fig.colorbar(im, ax=axs[4], label='spikes / second', fraction=0.046, pad=0.04)
    fig.tight_layout()
    plt.show()
