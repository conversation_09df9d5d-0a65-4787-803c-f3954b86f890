#%%
from DataYatesV1 import get_complete_sessions, get_session
from DataYatesV1.data.qc import plot_min_contam_prop, plot_amplitude_truncation
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.backends.backend_pdf import PdfPages
from tqdm import tqdm

res_dir = Path('/mnt/ssd/YatesMarmoV1/results')

missing_threshold = 100
contamination_threshold = 50
sessions = get_complete_sessions()
pdf = PdfPages(res_dir / 'inclusion.pdf')
for sess in sessions:
    print(f'Exporting QC units for {sess}')

    # start looking at qc inclusion criteria
    # extract missing % and contamination %
    spike_times = sess.ks_results.spike_times
    spike_clusters = sess.ks_results.spike_clusters
    spike_amplitudes = sess.ks_results.spike_amplitudes
    cids = np.unique(spike_clusters)

    refractory = np.load(sess.sess_dir / 'qc' / 'refractory' / 'refractory.npz')
    refractory_periods = refractory['refractory_periods']
    min_contam_proportions = refractory['min_contam_props']
    min_contam_pct = np.array([np.min(min_contam_proportions[iU])*100 for iU in range(len(cids))])

    truncation = np.load(sess.sess_dir / 'qc' / 'amp_truncation' / 'truncation.npz')
    med_miss_pct = np.array([np.median(truncation['mpcts'][truncation['cid']==iU]) for iU in range(len(cids))])
    valid_blocks = np.load(sess.sess_dir / 'qc' / 'amp_truncation' / 'present.npz')['valid_blocks']

    quality_metric = np.sqrt((med_miss_pct/missing_threshold)**2 + (min_contam_pct/contamination_threshold)**2)

    quality_units = quality_metric < 1
    qc_units = cids[quality_units]

    print(f'{np.sum(quality_units)} / {len(cids)} units pass')

    out = {
        'qc_units' : qc_units,
        'quality_metric' : quality_metric,
        'min_contam_pct' : min_contam_pct,
        'med_miss_pct' : med_miss_pct,
        'missing_threshold' : missing_threshold,
        'contamination_threshold' : contamination_threshold,
    }

    save_dir = sess.sess_dir / 'inclusion'
    save_dir.mkdir(exist_ok=True)
    np.savez(save_dir / 'inclusion.npz', **out)

    sess_pdf = PdfPages(save_dir / f'{sess.name}_inclusion.pdf')

    from matplotlib.patches import Ellipse
    fig = plt.figure()
    plt.scatter(min_contam_pct[quality_units], med_miss_pct[quality_units], color='g', alpha=.5)
    plt.scatter(min_contam_pct[~quality_units], med_miss_pct[~quality_units], color='r', alpha=.5)
    # plot ellipse for threshold
    ax = plt.gca()
    ax.add_patch(
        Ellipse(
            (0,0),
            width=2*contamination_threshold,
            height=2*missing_threshold,
            facecolor='none',
            edgecolor='g',
            linestyle='--',
            linewidth=2,
        )
    )

    plt.xlim([-1, 101])
    plt.ylim([-1, 51])
    plt.xlabel('Contamination Rate (%)')
    plt.ylabel('Median Missing %')
    plt.title(f'Missing Threshold: {missing_threshold}, Contamination Threshold: {contamination_threshold}, {np.sum(quality_units)} / {len(cids)} units pass')
    sess_pdf.savefig(fig)
    pdf.savefig(fig)
    plt.close(fig)

    order = np.argsort(quality_metric)
    for iU in tqdm(order, desc='Generating unit quality plots'):
        cid = cids[iU]
        fig, axs = plt.subplots(3, 1, figsize=(8, 8), height_ratios=[.5, .5, 1])

        st_clu = spike_times[spike_clusters == cid]
        amp_clu = spike_amplitudes[spike_clusters == cid]

        # Plot 1: Missing percentage over time
        unit_mask = truncation['cid'] == iU
        window_times = truncation['window_blocks'][unit_mask]
        missing_pcts = truncation['mpcts'][unit_mask]

        plot_amplitude_truncation(st_clu, amp_clu, window_times, valid_blocks, missing_pcts, axs=axs)

        # Plot 2: ISI distribution
        plot_min_contam_prop(st_clu, min_contam_proportions[iU], refractory_periods, axs=axs[2])

        # Add title with quality metrics
        axs[0].set_title(f'Unit {cid}\n' + 
                    f'Contamination: {min_contam_pct[iU]:.1f}%, ' +
                    f'Median Missing: {med_miss_pct[iU]:.1f}%\n'+
                    f'Quality Metric: {quality_metric[iU]:.1f}')
        fig.tight_layout()
        sess_pdf.savefig(fig)
        plt.close(fig)
    sess_pdf.close()
pdf.close()
# %%
