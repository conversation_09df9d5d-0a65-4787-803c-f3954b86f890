#%%
from DataYatesV1 import (plot_stas, print_batch, LNPModel, 
                         fit_lnp_lbfgs, PoissonMaskedLoss, get_free_device,
                         PoissonBPSAggregator, CombinedEmbeddedDataset, split_inds_by_trial)
from tqdm import tqdm
from DataYatesV1 import get_session, DictDataset, CombinedEmbeddedDataset, MaskedLoss
import torch
from torch import nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from DataYatesV1 import enable_autoreload
from copy import deepcopy
enable_autoreload()

from DataYatesV1.utils.modeling.general import get_valid_dfs
from torch.utils.data import DataLoader

subject = 'Allen'
date = '2022-04-13'
sess = get_session(subject, date)

device = get_free_device()

# %%

n_lags = 20
train_val_split = .6
batch_size = 4096
# cid = 113 # best complex cell
# cid = 98 # other options
# cid = 114
# cid = 116
#cid = 122
# cid = 39
# cid = 33 # simple/complex cell
#cid = 106 # potential simple cell
#cid = 126 # potential simple cell
#cid = 57 # lgn afferent
cid = 101
crop_radius = 15


cache = Path(f'/mnt/ssd/YatesMarmoV1/tmp/complex_study/{cid}')
cache.mkdir(parents=True, exist_ok=True)
recalc = False
if recalc:
    # delete cache
    import shutil
    shutil.rmtree(cache)
    cache.mkdir(parents=True, exist_ok=True)

def load_data(n_lags, train_val_split, cid):
    gaborium_dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
    gaborium_dset['stim'] = (gaborium_dset['stim'].float() - 127) / 255

    gaborium_dset['dfs'] = get_valid_dfs(gaborium_dset, n_lags)
    gaborium_dset['robs'] = gaborium_dset['robs'][:,[cid]]
    gaborium_inds = gaborium_dset['dfs'].squeeze().nonzero(as_tuple=True)[0]
    print(f'Gaborium dataset size: {len(gaborium_inds)} / {len(gaborium_dset)} ({len(gaborium_inds)/len(gaborium_dset)*100:.2f}%)')

    train_inds, val_inds = split_inds_by_trial(gaborium_dset, gaborium_inds, train_val_split)

    return gaborium_dset, train_inds, val_inds

gaborium_dset, train_inds, val_inds = load_data(
    n_lags, train_val_split, cid)

#%%

def calc_dset_sta(dset, inds, lags, modifier=lambda x: x, batch_size=2048, device='cpu', verbose = 0):
    if np.isscalar(lags):
        lags = np.arange(lags)

    keys_lags = {
        'robs': 0,
        'stim': lags,
    }

    if 'dfs' in dset:
        keys_lags['dfs'] = 0
    
    mu = dset['stim'][inds].mean()
    dset['stim'] -= mu

    n_spikes = dset['robs'][inds].sum()
    n_y, n_x = dset['stim'].shape[1:3]
    n_units = dset['robs'].shape[1]
    ce_dset = CombinedEmbeddedDataset(dset, inds, keys_lags)

    sta = torch.zeros((n_units, len(lags), n_y, n_x), device=device)
    it = range(0, len(ce_dset), batch_size)
    if verbose:
        it = tqdm(it, desc='Calculating STA')
    for i in it:
        batch = ce_dset[i:i+batch_size]
        batch = {k: v.to(device) for k, v in batch.items()}
        robs = batch['robs']
        stim = modifier(batch['stim'])
        
        sta += torch.einsum('tc, tlyx->clyx', robs, stim) / n_spikes
    sta = sta.to('cpu')
    dset['stim'] += mu
    return sta

def thresholded_centroid(img, threshold=.5):
    img = img - img.min()
    img = img / img.max()
    img[img < threshold] = 0
    n_i, n_j = img.shape
    i_centroid = (np.arange(n_i)[:,None] * img).sum() / img.sum()
    j_centroid = (np.arange(n_j)[None,:] * img).sum() / img.sum()
    return i_centroid, j_centroid

def crop_dataset(dset, inds, crop_radius, cache_dir):

    assert dset['robs'].shape[1] == 1, 'Only implemented for single unit'
    stas_file = cache_dir / f'stas_{cid}.pt'
    stes_file = cache_dir / f'stes_{cid}.pt'
    if stas_file.exists() and stes_file.exists():
        stas = torch.load(stas_file)
        stes = torch.load(stes_file)
    else:
        stas = calc_dset_sta(gaborium_dset, inds, n_lags, device=device, verbose=1)
        torch.save(stas, stas_file)
        stes = calc_dset_sta(gaborium_dset, inds, n_lags, modifier=lambda x: x**2, device=device, verbose=1)
        torch.save(stes, stes_file)
    stas = stas[0]
    stes = stes[0]
    
    energy_lags = stes.mean(dim=(1,2))
    max_lag = energy_lags.argmax()
    print(f'Max lag: {max_lag}')
    max_pix = thresholded_centroid(stes[max_lag].numpy())
    print(f'Max pix: {max_pix}')
    max_pix = np.round(max_pix).astype(int)
    roi = [[max_pix[0]-crop_radius, max_pix[0]+crop_radius+1], [max_pix[1]-crop_radius, max_pix[1]+crop_radius+1]]  
    
    fig, axs = plt.subplots(1, 2, figsize=(10, 5))
    axs[0].imshow(stas[max_lag].numpy(), cmap='gray')
    axs[0].scatter(max_pix[1], max_pix[0], c='r', s=100)
    axs[0].plot([roi[1][0], roi[1][1], roi[1][1], roi[1][0], roi[1][0]], 
                [roi[0][0], roi[0][0], roi[0][1], roi[0][1], roi[0][0]], c='r')
    axs[0].set_title('STA')
    axs[1].imshow(stes[max_lag].numpy(), cmap='gray')
    axs[1].scatter(max_pix[1], max_pix[0], c='r', s=100)
    axs[1].plot([roi[1][0], roi[1][1], roi[1][1], roi[1][0], roi[1][0]], 
                [roi[0][0], roi[0][0], roi[0][1], roi[0][1], roi[0][0]], c='r')
    plt.show()

    dset['stim'] = dset['stim'][:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]].contiguous()
    stas = stas[:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]]
    stes = stes[:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]]
    return dset, stas, stes, roi

gaborium_dset, sta, ste, roi = crop_dataset(gaborium_dset, train_inds, crop_radius, cache)
#%%
def calc_dset_stc(dset, inds, lags, roi = None, sta=None, batch_size=2048, device='cpu', verbose = 0):
    """
    Calculate the spike-triggered covariance (STC) for a dataset.
    
    This function computes the STC matrix for neural responses to visual stimuli,
    which helps identify relevant stimulus dimensions beyond the STA.
    
    Parameters:
    -----------
    dset : DictDataset
        Dataset containing stimuli and neural responses
    inds : torch.Tensor
        Indices of valid samples to use
    lags : int or array-like
        Number of time lags or specific lags to use
    roi : list, optional
        Region of interest as [[y_start, y_end], [x_start, x_end]]
    sta : torch.Tensor, optional
        Pre-computed spike-triggered average
    batch_size : int, default=2048
        Batch size for processing
    device : str, default='cpu'
        Device to perform computation on ('cpu' or 'cuda:X')
    verbose : int, default=0
        Verbosity level (0=silent, 1=progress bar)
        
    Returns:
    --------
    V : torch.Tensor
        Eigenvectors (filters) of the STC matrix
    L : torch.Tensor
        Eigenvalues of the STC matrix
    cov : torch.Tensor
        The full covariance matrix
    """
    
    if np.isscalar(lags):
        lags = np.arange(lags)

    keys_lags = {
        'robs': 0,
        'stim': lags,
    }

    mu = dset['stim'][inds].mean()
    dset['stim'] -= mu

    if 'dfs' in dset:
        keys_lags['dfs'] = 0

    n_units = dset['robs'].shape[1]
    n_lags = len(lags)
    if roi is None:
        n_y, n_x = dset['stim'].shape[1:3]
        roi = [[0, n_y], [0, n_x]]
    else:
        n_y = roi[0][1] - roi[0][0]
        n_x = roi[1][1] - roi[1][0]

    if sta is None:
        sta = calc_dset_sta(dset, inds, lags, device=device, verbose=verbose)
    sta = sta[...,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]].to(device)
    sta = sta.flatten(start_dim=1)

    # Get total spike count for normalization
    n_spikes = dset['robs'][inds].sum().to(device)
    ce_dset = CombinedEmbeddedDataset(dset, inds, keys_lags)
    n_dims = n_lags * n_y * n_x
    cov = torch.zeros((n_units, n_dims, n_dims), device=device)
    it = range(0, len(ce_dset), batch_size)
    if verbose:
        it = tqdm(it, desc=f'Calculating STC of dim {n_dims}')
    for i in it:
        batch = ce_dset[i:i+batch_size]
        batch = {k: v.to(device) for k, v in batch.items()}
        robs = batch['robs'] / n_spikes
        stim = batch['stim'][...,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]]
        stim = stim.flatten(start_dim=1)
        for iU in range(n_units):
            # Compute stimulus deviation from STA, weighted by spike count
            s = (stim - sta[[iU]]) * torch.sqrt(robs[:,[iU]])
            cov[iU] += s.T @ s
        torch.cuda.empty_cache()

    # Eigendecomposition of covariance matrix
    L, V = torch.linalg.eigh(cov)
    L = L.to('cpu')
    L = L.flip(1)  # Sort eigenvalues in descending order
    V = V.to('cpu').reshape(n_units, n_lags, n_y, n_x, n_dims).permute(0, 4, 1, 2, 3)
    V = V.flip(1)  # Sort eigenvectors to match eigenvalues

    cov = cov.to('cpu')
    dset['stim'] += mu  # Restore original stimulus values

    return V, L, cov
 
stc_file = cache / f'stc_{cid}.pt'
if not stc_file.exists():
    eig_filters, eig_vals, cov = calc_dset_stc(gaborium_dset, train_inds, n_lags, sta=sta[None,...],device=device, verbose=1)
    torch.save((eig_filters, eig_vals, cov), stc_file)
else:
    eig_filters, eig_vals, cov = torch.load(stc_file)

fig, axs = plt.subplots(2,1, figsize=(8, 8))
axs[0].scatter(np.arange(eig_vals.shape[1]), eig_vals[0])
plot_stas(eig_filters[0,:6,:,None], row_labels=[f'Eig {i}' for i in range(6)], ax = axs[1])
plt.show()

#%%

def get_initial_filters(sta, eig_filters, dset, inds, batch_size = 2048, n_steps=10, device='cpu'):
    filters = torch.cat([
        sta[None,...],
        eig_filters[0, :2]
    ], dim=0).to(device)

    plot_stas(filters[:,:,None], row_labels=['STA', 'Eig 1', 'Eig 2'])
    plt.show()

    n_lags = filters.shape[1]
    keys_lags = {
        'robs': 0,
        'stim': np.arange(n_lags),
    }
    train_dset = CombinedEmbeddedDataset(dset, inds, keys_lags, device)

    robs = []
    filter_outputs = []
    for i in tqdm(range(0, len(train_dset), batch_size)):
        batch = train_dset[i:i+batch_size]
        robs.append(batch['robs'])
        stim = batch['stim']
        filter_outputs.append(torch.einsum('tlyx, flyx->tf', stim, filters))
    robs = torch.cat(robs, dim=0)
    filter_outputs = torch.cat(filter_outputs, dim=0)

    def calc_nonlinearity_1d(gen, robs, n_bins=20):
        bin_edges = np.percentile(gen.cpu().numpy(), np.linspace(0, 100, 21))
        bins = (bin_edges[1:] + bin_edges[:-1]) / 2
        nonlin = torch.zeros(len(bins))
        for i in range(len(bins)):
            mask = (gen > bin_edges[i]) & (gen < bin_edges[i+1])
            nonlin[i] = robs[mask].mean()
        return bins, nonlin
        
    l_bins, l_nonlin = calc_nonlinearity_1d(filter_outputs[:,0], robs)
    e1_bins, e1_nonlin = calc_nonlinearity_1d(filter_outputs[:,1], robs)
    e2_bins, e2_nonlin = calc_nonlinearity_1d(filter_outputs[:,2], robs)

    fig, axs = plt.subplots(1,2, figsize=(10, 5))
    axs[0].plot(l_bins, l_nonlin, label='Linear')
    axs[1].plot(e1_bins, e1_nonlin, label='Eig 1')
    axs[1].plot(e2_bins, e2_nonlin, label='Eig 2')
    for ax in axs:
        ax.legend()
        ax.set_xlabel('Filter Output')
        ax.set_ylabel('spikes / bin')
    plt.show()

    stim = filter_outputs.clone()
    stim[1:] = stim[1:] ** 2
    data = {
        'stim': stim,
        'robs': robs.to(device),
    }

    lnp_model = fit_lnp_lbfgs(data, 
                              n_steps=n_steps, 
                              lbfgs_kwargs={'lr': 1, 'max_iter': 1000, 'max_eval': 1000, 'tolerance_change': 1e-6, 'tolerance_grad': 1e-6},
                              device=device)


    init_filters = filters.clone()
    init_filters[0] *= lnp_model.kernel.detach()[0,0]
    init_filters[1] *= torch.sqrt(lnp_model.kernel.detach()[0,1])
    init_filters[2] *= torch.sqrt(lnp_model.kernel.detach()[0,2])
    init_filters = init_filters.to('cpu')

    init_bias = lnp_model.bias.detach()
    init_bias = init_bias.to('cpu')

    torch.cuda.empty_cache()
    return init_filters, init_bias

filters, bias = get_initial_filters(sta, eig_filters, 
                                    gaborium_dset, train_inds, 
                                    batch_size=2048, n_steps=10,
                                    device=device)


#%%
keys_lags = {
    'robs': 0,
    'stim': np.arange(n_lags),
}
train_data = CombinedEmbeddedDataset(gaborium_dset,
                                    train_inds,
                                    keys_lags,
                                    device)[:]

print_batch(train_data)

val_data = CombinedEmbeddedDataset(gaborium_dset,
                                   val_inds,
                                   keys_lags,
                                   'cpu')[:]
print_batch(val_data)
torch.cuda.empty_cache()

#%%

from DataYatesV1.utils.modeling.reg import l1, laplacian, locality_conv
class StandardModel(torch.nn.Module):
    def __init__(self, dims, n_units, base_fr=None, lap_xy=0, lap_t=0, local=0, l2=0):
        super(StandardModel, self).__init__()
        self.bias = torch.nn.Parameter(torch.zeros(n_units))
        assert len(dims) == 3
        self.lap_xy = lap_xy
        self.lap_t = lap_t
        self.local = local
        self.l2 = l2

        n_lags, n_y, n_x = dims
        self.n_units = n_units
        self.dims = dims

        self.linear_filter = nn.Parameter(torch.randn(n_units, n_lags, n_y, n_x))
        self.energy_filters = nn.ParameterList([
            torch.randn(n_units, n_lags, n_y, n_x) for _ in range(2)
        ])

        if base_fr is not None:
            self.bias.data = torch.log(torch.exp(base_fr) - 1)
        
    def reg_loss(self):
        reg_loss = 0
        if self.lap_xy > 0:
                reg_loss += self.lap_xy * laplacian(self.linear_filter, dims=[-2, -1])
                for e in self.energy_filters:
                    reg_loss += self.lap_xy * laplacian(e, dims=[-2, -1])
        if self.lap_t > 0:
            reg_loss += self.lap_t * laplacian(self.linear_filter, dims=1)
            for e in self.energy_filters:
                reg_loss += self.lap_t * laplacian(e, dims=1)
        if self.local > 0:
            reg_loss += self.local * locality_conv(self.linear_filter, dims=[-2, -1])
            for e in self.energy_filters:
                reg_loss += self.local * locality_conv(e, dims=[-2, -1])
        if self.l2 > 0:
            reg_loss += self.l2 * (torch.norm(self.linear_filter, p=2) + sum(torch.norm(e, p=2) for e in self.energy_filters))

        return reg_loss

    def forward(self, x):
        # x['stim']: (batch, n_lags, H, W)
        n_samples = len(x['stim'])
        g = x['stim'].view(n_samples, -1) @ self.linear_filter.view(self.n_units, -1).T
        for e in self.energy_filters:
            g += torch.pow(x['stim'].view(n_samples, -1) @ e.view(self.n_units, -1).T, 2)
        g += self.bias
        x['rhat'] = F.softplus(g)
        return x

    def plot_weights(self):
        # Extract feature weights.
        # The feature weights are from a 1x1 conv: shape (n_units, in_channels, 1, 1)
        # Squeeze to (n_units, in_channels)
        linear_weights = self.linear_filter.detach().cpu().numpy().squeeze()
        l_max = np.max(np.abs(linear_weights))
        n_lags = len(linear_weights)
        # Extract spatial weights: expected shape (n_units, H, W)
        e1_weights = self.energy_filters[0].detach().cpu().numpy().squeeze()
        e1_max = np.max(np.abs(e1_weights))
        e2_weights = self.energy_filters[1].detach().cpu().numpy().squeeze()
        e2_max = np.max(np.abs(e2_weights))

        fig, axs = plt.subplots(3, linear_weights.shape[0], figsize=(n_lags, 3))
        for i in range(n_lags):
            axs[0, i].imshow(linear_weights[i], cmap='coolwarm_r', interpolation='nearest', vmin=-l_max, vmax=l_max)
            axs[1, i].imshow(e1_weights[i], cmap='coolwarm_r', interpolation='nearest', vmin=-e1_max, vmax=e1_max)
            axs[2, i].imshow(e2_weights[i], cmap='coolwarm_r', interpolation='nearest', vmin=-e2_max, vmax=e2_max)
            axs[2,i].set_xlabel(f'{i}')
            if i == 0:
                axs[0, i].set_ylabel('Linear')
                axs[1, i].set_ylabel('Energy 1')
                axs[2, i].set_ylabel('Energy 2')
        for ax in axs.flatten():
            ax.set_xticks([])
            ax.set_yticks([])
        return fig, axs

#%%
from DataYatesV1 import calc_poisson_bits_per_spike

def evaluate_model(model, data):
    with torch.no_grad():
        out = model(data)
        bps = calc_poisson_bits_per_spike(out['rhat'], out['robs']).item()
    return bps

def fit_standard_model(train_data, model, device='cuda:0', batch_size=4096, 
                       loss_tol=1e-6, max_steps=10):
                       
    model.to(device)
    loss_fn = PoissonMaskedLoss()
    
    optim = torch.optim.LBFGS(model.parameters(), 
                            history_size=10,
                            max_iter=500,
                            tolerance_change=1e-6,
                            line_search_fn=None,
                            tolerance_grad=1e-6)

    def closure(pbar=None):
        optim.zero_grad()
        n_samples = len(train_data['robs'])
        total_loss = 0
        
        # Process in batches to avoid OOM
        for i in range(0, n_samples, batch_size):
            # Create batch with explicit device placement
            batch = {key: val[i:i+batch_size].to(device) for key, val in train_data.items()}
            
            # Forward pass
            out = model(batch)
            
            # Calculate batch loss (don't call backward yet)
            batch_loss = loss_fn({'rhat': out['rhat'], 'robs': batch['robs']})
            
            # Scale loss by batch proportion and accumulate
            batch_fraction = len(batch['robs']) / n_samples
            batch_loss = batch_loss * batch_fraction
            batch_loss.backward()
            
            total_loss += batch_loss.item()
            
        
        # Add regularization loss and backward
        reg_loss = model.reg_loss()
        reg_loss.backward()
        total_loss += reg_loss.item()
        
        if pbar is not None:
            pbar.update(1)
            
        return total_loss

    
    train_losses = []

    for i in range(max_steps): 
        with tqdm(desc=f'Epoch {i}') as pbar:
            train_loss = optim.step(lambda: closure(pbar))
        train_losses.append(float(train_loss))
        status = f'\tTrain loss {i}: {train_loss:.4e}'
        if i == 0:
            print(status)
        else:
            print(f'{status} ({train_losses[-1] - train_losses[-2]:.4e})')
            if train_losses[-1] - train_losses[-2] > 0:
                print(f'Loss diverging, stopping training')
                return False
            elif abs(train_losses[-1] - train_losses[-2]) < loss_tol:
                print(f'Loss converged, stopping training')
                return True
    return True

# reg = {
#     'lap_xy': 3e-3,
#     'lap_t': 1e-3,
#     'local': 33,
# }


# sm = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1,
#                     **reg)
# # init weights
# sm.linear_filter.data = filters[0].unsqueeze(0)
# sm.energy_filters[0].data = filters[1].unsqueeze(0)
# sm.energy_filters[1].data = filters[2].unsqueeze(0)
# sm.bias.data = bias
# _ = sm.plot_weights()
# plt.show()

# sm.to('cpu')
# print(evaluate_model(sm, val_data))

# batch_size = 2**12
# print(f'Fitting with batch size {batch_size}')
# fit_standard_model(train_data, sm, device=device, batch_size=batch_size)
# sm.to('cpu')
# print(evaluate_model(sm, val_data))

# _ = sm.plot_weights()
# plt.show()

# %%
import optuna
from optuna.samplers import TPESampler

def objective(trial):
    """Objective function for Optuna hyperparameter optimization."""
    # Define hyperparameter search space
    reg = {
        'lap_xy': trial.suggest_float('lap_xy', 1e-5, 1e-1, log=True),
        'lap_t': trial.suggest_float('lap_t', 1e-5, 1e-1, log=True),
        'local': trial.suggest_float('local', 1e-1, 1e2, log=True)
    }
    
    # Create model with trial hyperparameters
    model = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1, **reg)
    
    # Initialize weights
    model.linear_filter.data = filters[0].unsqueeze(0)
    model.energy_filters[0].data = filters[1].unsqueeze(0)
    model.energy_filters[1].data = filters[2].unsqueeze(0)
    model.bias.data = bias
    
    # Train model
    batch_size = 2**12
    res = fit_standard_model(train_data, model, device=device, batch_size=batch_size)
    if not res:
        optuna.TrialPruned()
    
    # Evaluate on validation set
    model.to('cpu')
    val_bps = evaluate_model(model, val_data)
    
    return val_bps  # We want to maximize bits per spike

# Create study
n_trials = 30
n_startup = n_trials // 2
study_name = f'standard_model_{cid}'
storage_file = cache / f'{study_name}.db'
storage_str = 'sqlite:///' + str(storage_file)

if storage_file.exists():
    study = optuna.load_study(study_name=study_name, storage=storage_str)
    if len(study.trials) < n_trials:
        print(f'Found {len(study.trials)} trials in study, but {n_trials} were requested. Recreating study...')
        storage_file.unlink()
    
if not storage_file.exists():
    study = optuna.create_study(
        direction='maximize',  # Maximize bits per spike
        sampler=TPESampler(seed=1002, multivariate=True, n_startup_trials=n_startup),
        study_name=study_name,
        storage=storage_str,
    )
    study.optimize(objective, n_trials=n_trials)

# Plot optimization history
plt.figure(figsize=(10, 6))
optuna.visualization.matplotlib.plot_optimization_history(study)
plt.title('Optimization History')
plt.show()

# Plot parameter importances
plt.figure(figsize=(10, 6))
optuna.visualization.matplotlib.plot_param_importances(study)
plt.title('Parameter Importances')
plt.show()

plt.style.use('default')

#%%

# McFarland Variance decomposition
trials = study.get_trials()
values = [t.value for t in trials]
order = np.argsort(values)
values = np.array(values)[order]
trials = [trials[i] for i in order]

top_percentile = 20
n_top_trials = int(len(trials) * top_percentile / 100)
print(f'Top {top_percentile}% of trials: {n_top_trials}')
top_trials = trials[-n_top_trials:]
lap_xys = np.log10(np.array([trial.params['lap_xy'] for trial in top_trials]))
min_lap_xy = np.min(lap_xys)
lap_xys -= min_lap_xy
lap_ts = np.log10(np.array([trial.params['lap_t'] for trial in top_trials]))
min_lap_t = np.min(lap_ts)
lap_ts -= min_lap_t
locals = np.log10(np.array([trial.params['local'] for trial in top_trials]))
min_local = np.min(locals)
locals -= min_local

plt.figure()
plt.scatter(lap_xys, lap_ts, c=locals)
plt.xlabel('log10(lap_xy)')
plt.ylabel('log10(lap_t)')
plt.colorbar(label='log10(local)')
plt.title(f'Top {n_top_trials} trials. \n Origin is {min_lap_xy:.2f}, {min_lap_t:.2f}, {min_local:.2f}')
plt.show()
reg_amount = np.sqrt(lap_xys**2 + lap_ts**2 + locals**2)
most_reg_trial = top_trials[np.argmax(reg_amount)]

most_reg = most_reg_trial.params
mid_reg = deepcopy(most_reg)
mid_reg['local'] /= 10
low_reg = deepcopy(most_reg)
low_reg['local'] /= 100
print(f'Most regularized trial: {most_reg}')

# %%
# Train final model with best hyperparameters

best_model_path = cache / f'best_model_{cid}.pt'
sm_best = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1, **most_reg)
if not best_model_path.exists():
    sm_best.linear_filter.data = filters[0].unsqueeze(0)
    sm_best.energy_filters[0].data = filters[1].unsqueeze(0)
    sm_best.energy_filters[1].data = filters[2].unsqueeze(0)
    sm_best.bias.data = bias
    fit_standard_model(train_data, sm_best, device=device, batch_size=batch_size)
    sm_best.to('cpu')
    torch.save(sm_best.state_dict(), best_model_path)
else:
    sm_best.load_state_dict(torch.load(best_model_path))
best_bps = evaluate_model(sm_best, val_data)
print(f"Best model BPS: {best_bps:.4f}")

fig, axs = sm_best.plot_weights()
fig.suptitle(f'Best Model Weights (BPS: {best_bps:.4f})\nLap XY: {most_reg["lap_xy"]:.2e}, Lap T: {most_reg["lap_t"]:.2e}, Local: {most_reg["local"]:.2e}')
fig.savefig(cache / f'best_weights_{cid}.png')
plt.show()

mid_model_path = cache / f'mid_model_{cid}.pt'
sm_mid = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1, **mid_reg)
if not mid_model_path.exists():
    sm_mid.linear_filter.data = filters[0].unsqueeze(0)
    sm_mid.energy_filters[0].data = filters[1].unsqueeze(0)
    sm_mid.energy_filters[1].data = filters[2].unsqueeze(0)
    sm_mid.bias.data = bias
    fit_standard_model(train_data, sm_mid, device=device, batch_size=batch_size)
    sm_mid.to('cpu')
    torch.save(sm_mid.state_dict(), mid_model_path)
else:
    sm_mid.load_state_dict(torch.load(mid_model_path))
mid_bps = evaluate_model(sm_mid, val_data)
print(f"Mid model BPS: {mid_bps:.4f}")

fig, axs = sm_mid.plot_weights()
fig.suptitle(f'Mid Model Weights (BPS: {mid_bps:.4f})\nLap XY: {mid_reg["lap_xy"]:.2e}, Lap T: {mid_reg["lap_t"]:.2e}, Local: {mid_reg["local"]:.2e}')
fig.savefig(cache / f'mid_weights_{cid}.png')
plt.show()

low_model_path = cache / f'low_model_{cid}.pt'
sm_low = StandardModel((n_lags, crop_radius*2+1, crop_radius*2+1), 1, **low_reg)
if not low_model_path.exists():
    sm_low.linear_filter.data = filters[0].unsqueeze(0)
    sm_low.energy_filters[0].data = filters[1].unsqueeze(0)
    sm_low.energy_filters[1].data = filters[2].unsqueeze(0)
    sm_low.bias.data = bias
    fit_standard_model(train_data, sm_low, device=device, batch_size=batch_size)
    sm_low.to('cpu')
    torch.save(sm_low.state_dict(), low_model_path)
else:
    sm_low.load_state_dict(torch.load(low_model_path))
low_bps = evaluate_model(sm_low, val_data)
print(f"Low model BPS: {low_bps:.4f}")

fig, axs = sm_low.plot_weights()
fig.suptitle(f'Low Model Weights (BPS: {low_bps:.4f})\nLap XY: {low_reg["lap_xy"]:.2e}, Lap T: {low_reg["lap_t"]:.2e}, Local: {low_reg["local"]:.2e}')
fig.savefig(cache / f'low_weights_{cid}.png')
plt.show()

# %%

fixrsvp = DictDataset.load(sess.sess_dir / 'shifter' / 'fixrsvp_shifted.dset')
fixrsvp['stim'] = (fixrsvp['stim'].float() - 127) / 255
# crop stim
fixrsvp['stim'] = fixrsvp['stim'][:,roi[0][0]:roi[0][1],roi[1][0]:roi[1][1]].contiguous()
fixrsvp['dfs'] = get_valid_dfs(fixrsvp, n_lags)
fixrsvp['robs'] = fixrsvp['robs'][:,[cid]]
fixrsvp_inds = fixrsvp['dfs'].squeeze().nonzero(as_tuple=True)[0]

fixrsvp_keys_lags = {
    'robs': 0,
    'stim': np.arange(n_lags),
    'dfs': 0,
    'eyepos': np.arange(n_lags),
    'psth_inds': 0,
    'trial_inds': 0,
}
fixrsvp_dset = CombinedEmbeddedDataset(fixrsvp, fixrsvp_inds, fixrsvp_keys_lags)
fixrsvp_data = fixrsvp_dset[:]

#%%

def collate_fixrsvp(data, min_bins):

    assert 'robs' in data, 'robs must be in data'
    assert 'rhat' in data, 'rhat must be in data'
    assert 'eyepos' in data, 'eyepos must be in data'
    assert 'psth_inds' in data, 'psth_inds must be in data'
    assert 'trial_inds' in data, 'trial_inds must be in data'

    unique_trials = np.unique(data['trial_inds'])
    unique_psth_inds = np.unique(data['psth_inds'])
    robs = []
    rhat = []
    eyepos = []
    min_psth_ind = np.min(unique_psth_inds)
    required_inds = np.arange(min_psth_ind, min_bins)
    for iT in unique_trials: 
        trial_mask = (data['trial_inds'] == iT).numpy()
        trial_psth_inds = (data['psth_inds'][trial_mask]).numpy()
        if not np.isin(required_inds, trial_psth_inds).all():
            print(f'Skipping trial {iT}, has {len(trial_psth_inds)} only bins')
            continue

        trial_mask[trial_mask] = np.isin(trial_psth_inds, required_inds)

        trial_robs = data['robs'][trial_mask].T
        trial_rhat = data['rhat'][trial_mask].detach().T
        trial_eyepos = data['eyepos'][trial_mask]
        robs.append(trial_robs)
        rhat.append(trial_rhat)
        eyepos.append(trial_eyepos)
    robs = np.stack(robs, axis=1)
    rhat = np.stack(rhat, axis=1)
    eyepos = np.stack(eyepos, axis=0)
    print(f'Found {robs.shape[1]} trials for fixrsvp that are at least {min_trial_dur} seconds long.')
    return required_inds, robs, rhat, eyepos 

min_trial_dur = .75 # include
min_n_bins = int(min_trial_dur * 240)
inds, robs, rhat_best, eyepos = collate_fixrsvp(sm_best(fixrsvp_data), min_n_bins)
_, robs, rhat_mid, _ = collate_fixrsvp(sm_mid(fixrsvp_data), min_n_bins)
_, robs, rhat_low, _ = collate_fixrsvp(sm_low(fixrsvp_data), min_n_bins)

#%%
t_trial = inds / 240
psth = np.mean(robs, axis=1).squeeze()
psth_ste = np.std(robs, axis=1) / np.sqrt(robs.shape[1]).squeeze()

best_psth = np.mean(rhat_best, axis=1).squeeze()
best_psth_ste = np.std(rhat_best, axis=1) / np.sqrt(rhat_best.shape[1])

mid_psth = np.mean(rhat_mid, axis=1).squeeze()
mid_psth_ste = np.std(rhat_mid, axis=1) / np.sqrt(rhat_mid.shape[1])

low_psth = np.mean(rhat_low, axis=1).squeeze()
low_psth_ste = np.std(rhat_low, axis=1) / np.sqrt(rhat_low.shape[1])

plt.figure()
plt.plot(t_trial, psth, label='Data')
plt.fill_between(t_trial, (psth - psth_ste).squeeze(), (psth + psth_ste).squeeze(), alpha=0.5)
plt.plot(t_trial, best_psth, label='Best')
plt.fill_between(t_trial, (best_psth - best_psth_ste).squeeze(), (best_psth + best_psth_ste).squeeze(), alpha=0.5)
plt.plot(t_trial, mid_psth, label='Mid')
plt.fill_between(t_trial, (mid_psth - mid_psth_ste).squeeze(), (mid_psth + mid_psth_ste).squeeze(), alpha=0.5)
plt.plot(t_trial, low_psth, label='Low')
plt.fill_between(t_trial, (low_psth - low_psth_ste).squeeze(), (low_psth + low_psth_ste).squeeze(), alpha=0.5)
plt.xlabel('Time (s)')
plt.ylabel('Firing Rate (Hz)')
plt.title('FixRSVP PSTH')
plt.legend()
plt.savefig(cache / f'fixrsvp_psth_{cid}.png')
plt.show()


#%%
def mcfarland2016(robs, eyepos, n_bins=10, plot = False):
    '''
    Partition the variance due to fixational eye movements
    Inputs:
        robs: ndarray [n_trials, n_bins] binned responses for a single unit
        eye_pos: ndarray [n_trials, n_bins, n_lags, 2] eye position
        nbins: int, number of bins for eye position distance (uses percentile)
    '''
    assert (robs.shape[0] == eyepos.shape[0]) and (robs.shape[1] == eyepos.shape[1]), \
        'robs and eyepos must have the same number of trials and bins'


    total_var = np.var(robs)
    mean_rate = np.mean(robs)

    # Estimate rate variance from cross trial covariance
    trial_outer = robs[:,None,:] * robs[None,:,:]
    upper_mask = np.triu(np.ones(trial_outer.shape[:2], dtype=bool), k=1)
    trial_outer[~upper_mask] = np.nan
    rate_var = np.nanmean(trial_outer) - mean_rate**2

    ep_dist = eyepos[:,None,:] - eyepos[None,:,:]
    ep_dist = np.hypot(ep_dist[...,0], ep_dist[...,1])
    ep_dist = np.mean(ep_dist, axis=-1)
    ep_dist[~upper_mask] = np.nan

    ep_dist_flat = ep_dist.flatten()
    ep_dist_flat = ep_dist_flat[~np.isnan(ep_dist_flat)]

    ep_dist_thresholds = np.percentile(ep_dist_flat, np.linspace(0, 100, n_bins))
    cum_ep_thresholds = ep_dist_thresholds[1:]
    cum_rate_vars = []

    for thresh in cum_ep_thresholds:
        ep_dist_mask = ep_dist < thresh
        ep_rate_var = np.mean(trial_outer[ep_dist_mask]) - mean_rate**2
        cum_rate_vars.append(ep_rate_var)
    cum_rate_vars = np.array(cum_rate_vars)

    ep_bins = []
    bin_rate_vars = []
    for t0, t1 in zip(ep_dist_thresholds[:-1], ep_dist_thresholds[1:]):
        ep_dist_mask = np.logical_and(ep_dist >= t0, ep_dist < t1)
        ep_bins.append(np.median(ep_dist[ep_dist_mask]))
        bin_rate_vars.append(np.mean(trial_outer[ep_dist_mask]) - mean_rate**2)
    ep_bins = np.array(ep_bins)
    bin_rate_vars = np.array(bin_rate_vars)

    em_corrected_var = cum_rate_vars[0]
    alpha = rate_var / em_corrected_var

    if plot:
        fig, axs = plt.subplots(2, 1, figsize=(8,8), sharex=True)
        #axs[0].axhline(total_var, color='k', linestyle='--', label='Total Variance')
        axs[0].axhline(cum_rate_vars[0], color='g', linestyle='--', label='EM-Corrected Rate Variance')
        axs[0].axhline(rate_var, color='r', linestyle='--', label='Raw Rate Variance')
        axs[0].plot(ep_bins, bin_rate_vars, 'o-', label='Binned Rate Variance')
        axs[0].plot(cum_ep_thresholds, cum_rate_vars, 'o-', label='Cumulative Rate Variance')
        axs[0].legend()
        axs[0].set_ylabel('Variance (spikes^2)')
        axs[0].set_xlabel('Eye Position Distance (degrees)')
        axs[0].set_title(f'Variance decomposition. \n$\\alpha$ = {alpha:.2f}, Total variance = {total_var:.2f}')

        axs[1].hist(ep_dist_flat, bins=50)
        axs[1].set_xlabel('Eye Position Distance (degrees)')
        axs[1].set_ylabel('Count')
        axs[1].set_title('Eye Position Distance Distribution')
        plt.show()
    out = {
        'alpha': alpha,
        'total_var': total_var,
        'rate_var': rate_var,
        'em_corrected_var': em_corrected_var,
        'ep_bins': ep_bins,
        'bin_rate_vars': bin_rate_vars,
        'cum_ep_thresholds': cum_ep_thresholds,
        'cum_rate_vars': cum_rate_vars,
    }
    return out

results_data = mcfarland2016(robs[0], eyepos, plot=True)

results_best = mcfarland2016(rhat_best[0], eyepos, plot=True)

results_mid = mcfarland2016(rhat_mid[0], eyepos, plot=True)

results_low = mcfarland2016(rhat_low[0], eyepos, plot=True)

# N = 100
# for i in range(N):
#     rsim = np.random.poisson(rhat).astype(np.float32)
#     out = mcfarland2016(rsim[0], eyepos, plot=i==0)
#     if i == 0:
#         results_sim = out
#     else:
#         for key in results_sim:
#             results_sim[key] += out[key]
# for key in results_sim:
#     results_sim[key] /= N


# %%
# Plot McFarland analysis comparison between data and model
fig, axs = plt.subplots(2, 1, figsize=(10, 10), sharex=True)

# Colors for different metrics
binned_color = 'C0'
cumulative_color = 'C1'
em_color = 'green'
raw_color = 'red'

# Line styles for data vs simulation
data_style = '-'
best_style = '--'
mid_style = '-.'
low_style = ':'

# Top plot: Raw variance values
# Binned rate variance
axs[0].plot(results_data['ep_bins'], results_data['bin_rate_vars'], 'o-', 
         color=binned_color, linestyle=data_style, label='Binned EM-RV (Data)', linewidth=3, markersize=10)
axs[0].plot(results_best['ep_bins'], results_best['bin_rate_vars'], 'o-', 
         color=binned_color, linestyle=best_style, label='Binned EM-RV (Best)', linewidth=3, markersize=10)
axs[0].plot(results_mid['ep_bins'], results_mid['bin_rate_vars'], 'o-', 
         color=binned_color, linestyle=mid_style, label='Binned EM-RV (Mid)', linewidth=3, markersize=10)
axs[0].plot(results_low['ep_bins'], results_low['bin_rate_vars'], 'o-', 
         color=binned_color, linestyle=low_style, label='Binned EM-RV (Low)', linewidth=3, markersize=10)

# Cumulative rate variance
# axs[0].plot(results_data['cum_ep_thresholds'], results_data['cum_rate_vars'], 's-', 
#          color=cumulative_color, linestyle=data_style, label='Cumulative EM-RV (Data)')
# axs[0].plot(results_best['cum_ep_thresholds'], results_best['cum_rate_vars'], 's-', 
#          color=cumulative_color, linestyle=best_style, label='Cumulative EM-RV (Best)')
# axs[0].plot(results_mid['cum_ep_thresholds'], results_mid['cum_rate_vars'], 's-', 
#          color=cumulative_color, linestyle=mid_style, label='Cumulative EM-RV (Mid)')
# axs[0].plot(results_low['cum_ep_thresholds'], results_low['cum_rate_vars'], 's-', 
#          color=cumulative_color, linestyle=low_style, label='Cumulative EM-RV (Low)')

# Horizontal lines for EM-corrected and raw rate variance
axs[0].axhline(results_data['em_corrected_var'], color=em_color, linestyle=data_style, 
            zorder=0)
axs[0].axhline(results_best['em_corrected_var'], color=em_color, linestyle=best_style, 
            zorder=0)
axs[0].axhline(results_mid['em_corrected_var'], color=em_color, linestyle=mid_style, 
            zorder=0)
axs[0].axhline(results_low['em_corrected_var'], color=em_color, linestyle=low_style, 
             zorder=0)

axs[0].axhline(results_data['rate_var'], color=raw_color, linestyle=data_style, 
            zorder=0)
axs[0].axhline(results_best['rate_var'], color=raw_color, linestyle=best_style, 
             zorder=0)
axs[0].axhline(results_mid['rate_var'], color=raw_color, linestyle=mid_style, 
             zorder=0)
axs[0].axhline(results_low['rate_var'], color=raw_color, linestyle=low_style, 
            zorder=0)

axs[0].set_ylabel('Variance (spikes²)')
axs[0].set_title(f'McFarland Analysis Comparison\nUnit: {cid}. Data α={results_data["alpha"]:.2f}, Best α={results_best["alpha"]:.2f}, Mid α={results_mid["alpha"]:.2f}, Low α={results_low["alpha"]:.2f}')
lines = [
    plt.Line2D([0], [0], color=binned_color, linestyle=data_style, label='Data'),
    plt.Line2D([0], [0], color=binned_color, linestyle=best_style, label='Most Reg'),
    plt.Line2D([0], [0], color=binned_color, linestyle=mid_style, label='Mid Reg'),
    plt.Line2D([0], [0], color=binned_color, linestyle=low_style, label='Low Reg'),
    plt.Line2D([0], [0], color=binned_color, label='Binned EM-RV'),
    plt.Line2D([0], [0], color=em_color, label='EM-RV'),
    plt.Line2D([0], [0], color=raw_color, label='Raw RV'),
]
axs[0].legend(handles=lines, loc='lower left')
axs[0].grid(True, alpha=0.5)

# Bottom plot: Normalized variance (divided by EM-corrected variance)
# Binned rate variance (normalized)
axs[1].plot(results_data['ep_bins'], 
         np.array(results_data['bin_rate_vars'])/results_data['em_corrected_var'], 'o-', 
         color=binned_color, linestyle=data_style, linewidth=3, markersize=10)
axs[1].plot(results_best['ep_bins'], 
         np.array(results_best['bin_rate_vars'])/results_best['em_corrected_var'], 'o-', 
         color=binned_color, linestyle=best_style, linewidth=3, markersize=10)
axs[1].plot(results_mid['ep_bins'], 
         np.array(results_mid['bin_rate_vars'])/results_mid['em_corrected_var'], 'o-', 
         color=binned_color, linestyle=mid_style, linewidth=3, markersize=10)
axs[1].plot(results_low['ep_bins'], 
         np.array(results_low['bin_rate_vars'])/results_low['em_corrected_var'], 'o-', 
         color=binned_color, linestyle=low_style, linewidth=3, markersize=10)

# Cumulative rate variance (normalized)
# axs[1].plot(results_data['cum_ep_thresholds'], 
#          np.array(results_data['cum_rate_vars'])/results_data['em_corrected_var'], 's-', 
#          color=cumulative_color, linestyle=data_style)
# axs[1].plot(results_best['cum_ep_thresholds'], 
#          np.array(results_best['cum_rate_vars'])/results_best['em_corrected_var'], 's-', 
#          color=cumulative_color, linestyle=best_style)
# axs[1].plot(results_mid['cum_ep_thresholds'], 
#          np.array(results_mid['cum_rate_vars'])/results_mid['em_corrected_var'], 's-', 
#          color=cumulative_color, linestyle=mid_style)
# axs[1].plot(results_low['cum_ep_thresholds'], 
#          np.array(results_low['cum_rate_vars'])/results_low['em_corrected_var'], 's-', 
#          color=cumulative_color, linestyle=low_style)

# Horizontal lines for normalized EM-corrected and raw rate variance
axs[1].axhline(1.0, color=em_color, linestyle=data_style, zorder=0)  # EM-corrected normalized to 1.0
axs[1].axhline(1.0, color=em_color, linestyle=best_style, zorder=0)
axs[1].axhline(1.0, color=em_color, linestyle=mid_style, zorder=0)
axs[1].axhline(1.0, color=em_color, linestyle=low_style, zorder=0)

axs[1].axhline(results_data['rate_var']/results_data['em_corrected_var'], 
            color=raw_color, linestyle=data_style, zorder=0)
axs[1].axhline(results_best['rate_var']/results_best['em_corrected_var'], 
            color=raw_color, linestyle=best_style, zorder=0)
axs[1].axhline(results_mid['rate_var']/results_mid['em_corrected_var'], 
            color=raw_color, linestyle=mid_style, zorder=0)
axs[1].axhline(results_low['rate_var']/results_low['em_corrected_var'], 
            color=raw_color, linestyle=low_style, zorder=0)

axs[1].set_xlabel('Eye Position Distance (degrees)')
axs[1].set_ylabel('Normalized Variance')
axs[1].grid(True, alpha=0.5)

plt.tight_layout()
plt.savefig(cache / f'mcfarland_analysis_{cid}.png')
plt.show()


# %%
