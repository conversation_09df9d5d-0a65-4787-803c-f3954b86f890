#!/usr/bin/env python
"""
Test script for the TemporalBasis module.

This script demonstrates how to use the TemporalBasis module to embed different signals
(stimulus, eye position, saccade times) onto a compact basis representation.
"""
#%%
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn.functional as F
from pathlib import Path

from DataYatesV1 import (
    enable_autoreload, get_session, ensure_tensor,
    get_complete_sessions, print_batch, set_seeds
)
from DataYatesV1.utils.data.datasets import DictDataset
from DataYatesV1.models.modules import TemporalBasis
from DataYatesV1.utils.modeling.bases import make_raised_cosine_basis

# Enable autoreload for interactive development
enable_autoreload()
set_seeds(1002)

# Set device
device = 'cuda:1' if torch.cuda.is_available() else 'cpu'

#%%
# Load session data
subject = 'Allen'
date = '2022-04-13'

sessions = get_complete_sessions()
sess = get_session(subject, date)

# Load a dataset
dset_type = 'gaborium'
dset = DictDataset.load(sess.sess_dir / 'shifter' / f'{dset_type}_shifted.dset')

# Normalize stimulus
dset['stim'] = (dset['stim'].float() - 127) / 255

#%% Get a sample batch
batch_size = 1
seq_len = 500  # Number of time steps to use
start_idx = 1000  # Starting index

# Extract sample data
stim = dset['stim'][start_idx:start_idx+seq_len].unsqueeze(0)  # Add batch dimension
eyepos = dset['eyepos'][start_idx:start_idx+seq_len].unsqueeze(0)  # Add batch dimension
dfs = dset['dpi_valid'][start_idx:start_idx+seq_len].unsqueeze(0)  # Add batch dimension
eyevel = np.gradient(eyepos*dfs[:,:,None], axis=1)
eyevel = ensure_tensor(eyevel)

print(f"Stimulus shape: {stim.shape}")
print(f"Eye position shape: {eyepos.shape}")

#%%
# Parameters for stimulus embedding (causal)
stim_basis = TemporalBasis(
    num_delta_funcs=1,
    num_cosine_funcs=6,
    history_bins=32,
    log_spacing=True,
    peak_range_ms=(5, 60),
    normalize=True,
    causal=True,
    sampling_rate=240
).to(device)

_ = stim_basis.plot_basis_functions()

#%%

# Parameters for eye position embedding (causal)
eyepos_basis = TemporalBasis(
    num_delta_funcs=0,  # Include current eye position
    num_cosine_funcs=10,
    history_bins=50,
    log_spacing=False,
    peak_range_ms=(30, 200),
    normalize=True,
    causal=False,
    sampling_rate=240
).to(device)

_ = eyepos_basis.plot_basis_functions()


#%% Apply Basis Embedding
# Move data to device
stim = stim.to(device)
eyevel = eyevel.to(device)

#%%
# import split relu
from DataYatesV1.models.modules import SplitRelu
split = SplitRelu(split_dim=2).to(device)
#%%

out = eyepos_basis(split(eyevel).permute(0,2,1).to(torch.float32))
plt.imshow(out.detach().cpu().numpy()[0], aspect='auto', interpolation='none')
ax = plt.gca().twinx()
_ = ax.plot(eyevel[0].cpu().numpy())

plt.figure()
plt.plot(out[0,:,:].T.detach().cpu())
ax = plt.gca().twinx()
_ = ax.plot(eyevel[0].cpu().numpy())
#%%

# Apply basis embedding
stim_embedded = stim_basis(stim.unsqueeze(1))

_ = plt.plot(stim_embedded[0,:,:,25,25].detach().cpu().T)
# %%
