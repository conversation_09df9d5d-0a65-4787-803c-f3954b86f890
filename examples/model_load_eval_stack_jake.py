"""
Model Evaluation Stack Script

This script loads a trained neural network model and evaluates its performance on various datasets
including Gaborium, Backimage, FixRSVP, and Gratings. It calculates and visualizes:
- Bits per spike (BPS) metrics
- Spike-triggered averages (STAs)
- Peri-event time histograms (PETHs) around saccades
- Tuning properties for gratings stimuli
- Quality metrics for neural recordings

The script generates comprehensive evaluation plots for each unit in the dataset,
combining model predictions with observed neural responses.

Author: <PERSON>
"""

#%% Import libraries
# Standard libraries
import json
from pathlib import Path
from pprint import pprint

# Data processing libraries
import numpy as np
import torch
import yaml
from tqdm import tqdm

# Visualization libraries
import matplotlib.pyplot as plt
from matplotlib.colors import Normalize
from matplotlib.gridspec import GridSpec
from matplotlib.backends.backend_pdf import PdfPages

# DataYatesV1 package imports
from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1 import (
    get_session, enable_autoreload, get_free_device,
    plot_stas, get_gaborium_sta_ste, ensure_tensor,
    CombinedEmbeddedDataset
)
from DataYatesV1.models.config_loader import load_config
from DataYatesV1.models.utils.eval import (PoissonBPSAggregator, 
                                        PETHEvalModule, STAEvalModule,
                                        FixRSVPPSTHEvalModule)
from DataYatesV1.utils.rf import calc_sta
from DataYatesV1.data.qc import plot_min_contam_prop

# Enable auto-reloading of modules for interactive development
enable_autoreload()

#%% Set up environment

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Check if CUDA is available and set device
device = get_free_device()
print(f"Using device: {device}")

#%% Load model and data
# Specify the model to evaluate
# model_name = 'v1_none_densenet_film_none_gaussianei_explosive rain_epoch_50'
model_name = 'v1_none_densenet_film_none_gaussian_epoch_52'

# Configuration paths
registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry")
save_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/eval_stack") / model_name
save_dir.mkdir(parents=True, exist_ok=True)

# Initialize model registry and load session
registry = ModelRegistry(registry_dir)




# Load model entry from registry
model_entry = registry.find_models(model_name)[0]
data_config_path = model_entry['dataset_info']
print(f'Model Registry Entry:\n')
pprint(model_entry)

# Load model configuration
model_config = load_config(model_entry['config_path'])
print(f'\n--------------------\nModel Configuration:\n')
pprint(model_config)

# Load and prepare dataset
with open(data_config_path, 'r') as f:
    dataset_config = yaml.safe_load(f)
# Add additional dataset types for evaluation
dataset_config['types'] = dataset_config['types'] + ['fixrsvp', 'gratings']
print(f'\n--------------------\nDataset Configuration:\n')
pprint(dataset_config)

model = registry.load_model(model_name, map_location=device)
model = model.to(device)
print(f'\n--------------------\nModel Loaded:\n{model}\n')

#%%
# Load session
sess = get_session(*dataset_config['session'].split('_'))

# Extract key parameters from config
n_lags = np.max(dataset_config['keys_lags']['stim']) + 1
cids = np.array(dataset_config['cids'])
n_units = len(cids)
batch_size = model_entry['metadata']['batch_size']
dt = 1/model_config['sampling_rate']  # Time bin size in seconds

# Prepare training and validation datasets
train_data, val_data, dataset_config = prepare_data(dataset_config)

#%% Helper functions for dataset manipulation


#%% Evaluate model on different datasets

def evaluate_dataset(model, dataset, indices, batch_size=256, desc="Dataset"):
    """
    Evaluate model on a dataset and calculate bits per spike.

    Parameters
    ----------
    model : torch.nn.Module
        The model to evaluate.
    dataset : CombinedEmbeddedDataset
        The dataset to evaluate on.
    indices : torch.Tensor
        The indices to use for evaluation.
    batch_size : int, optional
        Batch size for evaluation, by default 256.
    desc : str, optional
        Description for progress bar, by default "Dataset".

    Returns
    -------
    tuple
        (model predictions, bits per spike)
    """
    bps_aggregator = PoissonBPSAggregator()
    dataset = dataset.shallow_copy()
    dataset.inds = indices
    robs = []
    rhat = []

    with torch.no_grad():
        for iB in tqdm(range(0, len(dataset), batch_size), desc=desc):
            batch = dataset[iB:iB+batch_size]
            batch = {k: v.to(model.device) for k, v in batch.items()}
            batch = model(batch)
            robs.append(batch['robs'].detach())
            rhat.append(batch['rhat'].detach())
            bps_aggregator(batch)
    robs = torch.cat(robs, dim=0)
    rhat = torch.cat(rhat, dim=0)

    bps = bps_aggregator.closure().cpu().numpy()
    bps_aggregator.reset()

    return robs, rhat, bps

recalc = True
cache_file = save_dir / f'{model_name}_eval_cache.pt'
if recalc and cache_file.exists():
    cache_file.unlink()
if not cache_file.exists():
    print(f'No evaluation cache found at {cache_file}. Evaluating from scratch...')

    # Gaborium dataset
    gaborium_inds = val_data.get_dataset_inds('gaborium')
    gaborium_robs, gaborium_rhat, gaborium_bps = evaluate_dataset(
        model, train_data, gaborium_inds, batch_size, "Gaborium"
    )

    # Backimage dataset
    backimage_inds = val_data.get_dataset_inds('backimage')
    backimage_robs, backimage_rhat, backimage_bps = evaluate_dataset(
        model, train_data, backimage_inds, batch_size, "Backimage"
    )

    # FixRSVP dataset
    fixrsvp_inds = torch.concatenate([
        train_data.get_dataset_inds('fixrsvp'),
        val_data.get_dataset_inds('fixrsvp')
    ])
    fixrsvp_robs, fixrsvp_rhat, fixrsvp_bps = evaluate_dataset(
        model, train_data, fixrsvp_inds, batch_size, "FixRSVP"
    )

    # Gratings dataset
    gratings_inds = torch.concatenate([
        train_data.get_dataset_inds('gratings'),
        val_data.get_dataset_inds('gratings')
    ])
    gratings_robs, gratings_rhat, gratings_bps = evaluate_dataset(
        model, train_data, gratings_inds, batch_size, "Gratings"
    )

    # Validation set BPS
    val_bps_aggregator = PoissonBPSAggregator()
    val_bps_aggregator({'robs': gaborium_robs, 'rhat': gaborium_rhat})
    val_bps_aggregator({'robs': backimage_robs, 'rhat': backimage_rhat})
    val_bps = val_bps_aggregator.closure().cpu().numpy()
    val_bps_aggregator.reset()

    # Save evaluation results to cache
    cache = {
        'gaborium_inds': gaborium_inds,
        'gaborium_eval': (gaborium_robs, gaborium_rhat, gaborium_bps),
        'backimage_inds': backimage_inds,
        'backimage_eval': (backimage_robs, backimage_rhat, backimage_bps),
        'fixrsvp_inds': fixrsvp_inds,
        'fixrsvp_eval': (fixrsvp_robs, fixrsvp_rhat, fixrsvp_bps),
        'gratings_inds': gratings_inds,
        'gratings_eval': (gratings_robs, gratings_rhat, gratings_bps),
        'val_bps': val_bps
    }
    torch.save(cache, cache_file)
    print(f'Evaluation cache saved to {cache_file}')
else:
    print(f'Loading evaluation cache from {cache_file}')
    cache = torch.load(cache_file)
    gaborium_inds = cache['gaborium_inds']
    gaborium_robs, gaborium_rhat, gaborium_bps = cache['gaborium_eval']
    backimage_inds = cache['backimage_inds']
    backimage_robs, backimage_rhat, backimage_bps = cache['backimage_eval']
    fixrsvp_inds = cache['fixrsvp_inds']
    fixrsvp_robs, fixrsvp_rhat, fixrsvp_bps = cache['fixrsvp_eval']
    gratings_inds = cache['gratings_inds']
    gratings_robs, gratings_rhat, gratings_bps = cache['gratings_eval']
    val_bps = cache['val_bps']

#%% Visualize BPS comparisons across datasets

def plot_bps_comparison(x_bps, y_bps, x_label, y_label, title=None):
    """
    Create a scatter plot comparing bits per spike between two datasets.

    Parameters
    ----------
    x_bps : numpy.ndarray
        BPS values for x-axis.
    y_bps : numpy.ndarray
        BPS values for y-axis.
    x_label : str
        Label for x-axis.
    y_label : str
        Label for y-axis.
    title : str, optional
        Plot title, by default None.
    """
    plt.figure()
    plt.scatter(x_bps, y_bps)
    plt.plot([-1, 3], [-1, 3], color='k', linestyle='--', alpha=.5)
    plt.xlim(-1, 3)
    plt.ylim(-1, 3)
    plt.xlabel(f'{x_label} BPS')
    plt.ylabel(f'{y_label} BPS')
    if title:
        plt.title(title)
    plt.show()

# Compare Gaborium vs Backimage BPS
plot_bps_comparison(
    gaborium_bps, backimage_bps,
    'Gaborium', 'Backimage',
    'Comparison of Bits Per Spike: Gaborium vs Backimage'
)

# Compare Gaborium vs Gratings BPS
plot_bps_comparison(
    gaborium_bps, gratings_bps,
    'Gaborium', 'Gratings',
    'Comparison of Bits Per Spike: Gaborium vs Gratings'
)

# Compare Gaborium vs FixRSVP BPS
plot_bps_comparison(
    gaborium_bps, fixrsvp_bps,
    'Gaborium', 'FixRSVP',
    'Comparison of Bits Per Spike: Gaborium vs FixRSVP'
)

#%% Spike-Triggered Average (STA) Evaluation

# Initialize STA evaluation module
sta_eval = STAEvalModule(train_data, stim_indexer='[:,None,:,0,...]')

# Calculate model STAs using the Gaborium dataset
sta, ste = sta_eval.evaluate(gaborium_inds, gaborium_rhat, device=device)
sta_data, ste_data = sta_eval.evaluate(gaborium_inds, gaborium_robs, device=device)

# Get ground truth STAs from the session data
# sta_data, ste_data = get_gaborium_sta_ste(sess, n_lags, dataset_config['cids'])
# sta_data = torch.from_numpy(sta_data)
# ste_data = torch.from_numpy(ste_data)

# Visualize and compare data vs. model STAs
fig, axs = plt.subplots(1, 2, figsize=(16, 60))

# Plot data STAs
plot_stas(sta_data[:,:,None,:,:], ax=axs[0])
axs[0].set_title('Data STA')

# Plot model STAs
plot_stas(sta[:,:,None,:,:], ax=axs[1])
axs[1].set_title('Model STA (val set)')

plt.show()

#%% Saccade analysis

# Load saccade data from session
saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
saccade_times = torch.tensor([s['start_time'] for s in saccades])

# Convert saccade times to dataset indices
saccade_inds = train_data.get_inds_from_times(saccade_times)

#%% Visualize saccade eye movements

# Create a dataset with eye position data around saccades
eye_data = train_data.shallow_copy()
keys_lags = {
    'eyepos': np.arange(50, -50, -1)  # Include eye position before and after saccade
}
eye_data.set_keys_lags(keys_lags)
eye_data.inds = saccade_inds

# Get data for the first saccade
d = eye_data[0]

# Plot eye position around the saccade
plt.figure()
plt.plot(np.arange(-49, 51), d['eyepos'])
plt.title('Eye Position Around Saccade')
plt.xlabel('Time bins relative to saccade')
plt.ylabel('Eye position')
plt.axvline(x=0, color='r', linestyle='--', label='Saccade onset')
plt.legend()
plt.show()

#%% Peri-Event Time Histogram (PETH) Analysis

# Parameters for PETH analysis
n_pre, n_post = 40, 80  # Number of time bins before and after event

# Extract saccade indices for each dataset type
backimage_sacc_inds = saccade_inds[saccade_inds[:, 0] == 1]
gaborium_sacc_inds = saccade_inds[saccade_inds[:, 0] == 0]
gratings_sacc_inds = saccade_inds[saccade_inds[:, 0] == 3]

# Create PETH evaluator for Backimage dataset
backimage_saccade_eval = PETHEvalModule(
    train_data, backimage_sacc_inds,
    n_pre=n_pre, n_post=n_post,
    name='backimage_saccade'
)
backimage_peth_robs, backimage_peth_rhat = backimage_saccade_eval.evaluate(
    backimage_inds, rhat=backimage_rhat
)

# Create PETH evaluator for Gaborium dataset
gaborium_saccade_eval = PETHEvalModule(
    train_data, gaborium_sacc_inds,
    n_pre=n_pre, n_post=n_post,
    name='gaborium_saccade'
)
gaborium_peth_robs, gaborium_peth_rhat = gaborium_saccade_eval.evaluate(
    gaborium_inds, rhat=gaborium_rhat
)

# Create PETH evaluator for Gratings dataset
gratings_saccade_eval = PETHEvalModule(
    train_data, gratings_sacc_inds,
    n_pre=n_pre, n_post=n_post,
    name='gratings_saccade'
)
gratings_peth_robs, gratings_peth_rhat = gratings_saccade_eval.evaluate(
    gratings_inds, rhat=gratings_rhat
)

#%%
keys_lags = train_data.keys_lags.copy()
keys_lags['eyepos'] = 0
train_data.set_keys_lags(keys_lags)

sacc_inds = backimage_sacc_inds.clone()

event_id = 20

sac_1 = np.where((train_data.inds == sacc_inds[event_id]).all(dim=1))[0].item()
sac_2 = np.where((train_data.inds == sacc_inds[event_id+2]).all(dim=1))[0].item()

sac_ind = np.arange(sac_1, sac_2)
batch = train_data[sac_ind]

model.eval()
with torch.no_grad():
    batch = {k: v.to(device) for k, v in batch.items() if isinstance(v, torch.Tensor)}
    output = model(batch)

plt.figure(figsize=(10, 5))
plt.subplot(2,1,1)
plt.imshow(batch['robs'].detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.ylabel('Units')
plt.title('Observed Spikes')

# plot eye pos overlaid
ax = plt.gca().twinx()
ax.plot(batch['eyepos'].detach().cpu(), color='k', alpha=0.5)
ax.set_ylabel('Eye Position', color='k')
ax.tick_params(axis='y', labelcolor='k')
ax.set_ylim([-10, 10])

plt.subplot(2,1,2)
plt.imshow(output['rhat'].detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.xlabel('Time (5ms bins)')
plt.ylabel('Units')
plt.title('Predicted Spikes')

#%%
mod = model.model.modulator.encoder(batch['behavior'])
gain = model.model.modulator.scale_layer(mod).detach().cpu()
offset = model.model.modulator.shift_layer(mod).detach().cpu()

plt.figure(figsize=(10, 5))
plt.subplot(2,1,1)
plt.imshow(gain.T, aspect='auto', interpolation='none', cmap='viridis')
plt.ylabel('Conv Dim')
plt.title('Gain')

# plot eye pos overlaid
ax = plt.gca().twinx()
ax.plot(batch['eyepos'].detach().cpu(), color='r', alpha=1)
ax.set_ylabel('Eye Position', color='r')
ax.tick_params(axis='y', labelcolor='r')
ax.set_ylim([-10, 10])

plt.subplot(2,1,2)
plt.imshow(offset.T, aspect='auto', interpolation='none', cmap='viridis')
plt.xlabel('Time (5ms bins)')
plt.ylabel('Conv Dim')
plt.title('Offset')


# plot again but with gain and offset as traces
plt.figure(figsize=(10, 5))
plt.subplot(2,1,1)
_ = plt.plot(gain)
plt.ylabel('Gain')
plt.title('Gain')

# plot eye pos overlaid
ax = plt.gca().twinx()
ax.plot(batch['eyepos'].detach().cpu(), color='k', alpha=1)
ax.set_ylabel('Eye Position', color='k')
ax.tick_params(axis='y', labelcolor='k')
ax.set_ylim([-10, 10])

plt.subplot(2,1,2)
_ = plt.plot(offset)
plt.xlabel('Time (5ms bins)')
plt.ylabel('Offset')
plt.title('Offset')

# plot eye pos overlaid
ax = plt.gca().twinx()
ax.plot(batch['eyepos'].detach().cpu(), color='k', alpha=1)
ax.set_ylabel('Eye Position', color='k')
ax.tick_params(axis='y', labelcolor='k')
ax.set_ylim([-10, 10])

#%%
model.model.convnet.layers[0].components.conv.plot_weights()

#%% Visualize PETHs for a sample unit

def plot_peth_comparison(peth_robs, peth_rhat, n_pre, n_post, title, lags=None):
    """
    Plot observed vs predicted PETH for a single unit.

    Parameters
    ----------
    peth_robs : torch.Tensor
        Observed PETH values.
    peth_rhat : torch.Tensor
        Predicted PETH values.
    title : str
        Plot title.
    lags : array-like, optional
        Time lags for x-axis. If None, uses bin indices.
    """
    plt.figure()
    lags = np.arange(-n_pre, n_post)
    plt.plot(lags, peth_robs, 'b-', label='Observed')
    plt.plot(lags, peth_rhat, 'r-', label='Predicted')
    plt.xlabel('Time bin relative to event')
    plt.ylabel('Firing rate')
    plt.title(title)
    plt.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    plt.legend()
    plt.show()

# Select a sample unit to visualize
iU = 33

# Plot PETHs for each dataset
plot_peth_comparison(
    backimage_peth_robs[:, iU], backimage_peth_rhat[:, iU],
    n_pre, n_post,
    f'Backimage Perisaccadic PSTH for Unit {iU}'
)

plot_peth_comparison(
    gaborium_peth_robs[:, iU], gaborium_peth_rhat[:, iU],
    n_pre, n_post,
    f'Gaborium Perisaccadic PSTH for Unit {iU}'
)

plot_peth_comparison(
    gratings_peth_robs[:, iU], gratings_peth_rhat[:, iU],
    n_pre, n_post,
    f'Gratings Perisaccadic PSTH for Unit {iU}'
)

#%% Evaluate FixRSVP dataset

# Initialize FixRSVP evaluation module
fixrsvp_eval = FixRSVPPSTHEvalModule(train_data)

# Evaluate model on FixRSVP data
robs, rhat, psth_inds, trial_inds = fixrsvp_eval.evaluate(fixrsvp_inds, rhat=fixrsvp_rhat)

# Calculate and visualize PSTH
fixrsvp_psth, fixrsvp_psth_pred = fixrsvp_eval.calculate_psth(robs, rhat, psth_inds)
fig, ax, fixrsvp_r2 = fixrsvp_eval.plot_psth_r2(fixrsvp_psth, fixrsvp_psth_pred)
ax.set_title('FixRSVP PSTH R² by Unit')
plt.show()

#%% Load spike data and quality metrics

# Load spike data from Kilosort results
spike_times = sess.ks_results.spike_times
t_min, t_max = spike_times.min(), spike_times.max()
spike_clusters = sess.ks_results.spike_clusters
spike_amplitudes = sess.ks_results.spike_amplitudes
spike_cids = np.unique(spike_clusters)

# Load refractory period violation metrics
refractory = np.load(sess.sess_dir / 'qc' / 'refractory' / 'refractory.npz')
refractory_periods = refractory['refractory_periods']
min_contam_proportions = refractory['min_contam_props'][cids]

# Calculate contamination percentage for each unit
contam_pct = np.array([
    np.min(min_contam_proportions[iU]) * 100
    for iU in range(len(cids))
])
print(f"Contamination percentage shape: {contam_pct.shape}")

# Load amplitude truncation metrics
truncation = np.load(sess.sess_dir / 'qc' / 'amp_truncation' / 'truncation.npz')
med_missing_pct = np.array([
    np.median(truncation['mpcts'][truncation['cid'] == iC])
    for iC in cids
])
print(f"Median missing percentage shape: {med_missing_pct.shape}")

#%% Gratings analysis

# Get individual datasets for analysis
gratings_dset = [d for d in train_data.dsets if d.metadata['name'] == 'gratings'][0]
gaborium_dset = [d for d in train_data.dsets if d.metadata['name'] == 'gaborium'][0]
backimage_dset = [d for d in train_data.dsets if d.metadata['name'] == 'backimage'][0]
fixrsvp_dset = [d for d in train_data.dsets if d.metadata['name'] == 'fixrsvp'][0]

# Extract gratings data
gratings_robs = gratings_dset['robs'].numpy()
sf = gratings_dset['sf'].numpy()  # Spatial frequency
sfs = np.unique(sf)
ori = gratings_dset['ori'].numpy()  # Orientation
oris = np.unique(ori)

# Create one-hot encoding of spatial frequency and orientation combinations
sf_ori_one_hot = np.zeros((len(gratings_robs), len(sfs), len(oris)))
for i in range(len(gratings_robs)):
    sf_idx = np.where(sfs == sf[i])[0][0]
    ori_idx = np.where(oris == ori[i])[0][0]
    sf_ori_one_hot[i, sf_idx, ori_idx] = 1

# Calculate spike-triggered average for gratings
gratings_sta = calc_sta(
    sf_ori_one_hot,                              # Stimulus
    gratings_robs.astype(np.float64),            # Neural responses
    int(n_lags),                                 # Number of time lags
    dfs=gratings_dset['dfs'].numpy().squeeze(),  # Data filtering status
    reverse_correlate=False,                     # Forward correlation
    progress=True                                # Show progress bar
).numpy()

#%% Load waveform and probe data

# Load waveform data
waves_full = np.load(sess.sess_dir / 'qc' / 'waveforms' / 'waveforms.npz')
waveforms = waves_full['waveforms'][cids]
wave_samples = waves_full['samples']
wave_times = waves_full['times']

# Load probe geometry information
ephys_meta = sess.ephys_metadata
probe_geom = ephys_meta['probe_geometry_um']

# Load laminar boundary information
laminar_results = np.load(sess.sess_dir / 'laminar' / 'laminar.npz')
l4_depths = laminar_results['l4_depths']  # Layer 4 boundary depths

#%% Generate comprehensive evaluation figures for each unit

# Set up time parameters
lags = backimage_saccade_eval.lags * float(dt) * 1000  # Convert lags to milliseconds

# Create PDF for saving evaluation figures
pdf = PdfPages(save_dir / f'{model_name}_eval.pdf')
for iU in tqdm(range(n_units), desc='Plotting Evaluations'):
    cid = cids[iU]
    fig = plt.figure(figsize=(24, 16))
    gs = GridSpec(3, 4, figure=fig, width_ratios=[.5, 1, 1, 1])

    # Panel 1: Waveform visualization
    ax0 = fig.add_subplot(gs[:, 0])

    # Scaling factors for visualization
    time_mult = 5e4
    voltage_mult = .3

    # Extract waveform for this unit
    wave = waveforms[iU].squeeze().T

    # Plot waveform on each channel
    for i in range(len(wave)):
        pos = probe_geom[i]
        ax0.plot(wave_times*time_mult + pos[0], pos[1] - wave[i]*voltage_mult, 'k')

    # Set axis labels and limits
    ax0.set_xlabel('Channel position (um)')
    ax0.set_ylabel('Depth (um)')
    ax0.set_ylim(1150, -20)

    # Plot layer 4 boundaries
    ax0.plot([-90, 90], [l4_depths[0], l4_depths[0]], 'r--')
    ax0.text(0, l4_depths[0], f'L4: {l4_depths[0]:.0f}um', ha='center', va='bottom')
    ax0.plot([200-90, 200+90], [l4_depths[1], l4_depths[1]], 'r--')
    ax0.text(200, l4_depths[1], f'L4: {l4_depths[1]:.0f}um', ha='center', va='bottom')

    # Add scale bars
    # Time scale bar (1 ms)
    ax0.plot([wave_times[0]*time_mult, (wave_times[0]+1e-3)*time_mult], [1120, 1120], 'k-', linewidth=2)
    ax0.text((wave_times[0]+.5e-3)*time_mult, 1120, '1 ms', rotation=0, va='bottom', ha='center')

    # Find maximum amplitude channel and calculate depth
    max_channel = np.abs(wave).max(axis=1).argmax()
    x, y = probe_geom[max_channel]
    if x < 100:
        depth = l4_depths[0] - y
    else:
        depth = l4_depths[1] - y
    ax0.set_title(f'Unit {cid} - Waveform\nDepth: {depth:.0f}um')

    # Voltage scale bar (0.1 mV)
    ax0.plot([x-90, x-90], [y, y+100*voltage_mult], 'k-', linewidth=2)
    ax0.text(x-90, y+50*voltage_mult, '.1 mV', rotation=90, va='center', ha='right')
    ax0.set_xticks([0, 200])
    ax0.set_xlim(-140, 300)

    # Panel 2: Bits per spike comparison
    ax1 = fig.add_subplot(gs[0, 1])

    # Sort units by validation BPS
    bps_order = np.argsort(val_bps)
    bps_ind = (bps_order == iU).nonzero()[0].item()

    # Plot combined validation BPS
    ax1.scatter(np.arange(len(val_bps)), val_bps[bps_order],
                color='C0', s=30, label='Combined', zorder=9)

    # Highlight current unit
    ax1.scatter(bps_ind, val_bps[iU], color='r', zorder=10)

    # Save y-limits before adding more points
    ylim = ax1.get_ylim()

    # Add individual dataset BPS values
    ax1.scatter(np.arange(len(gaborium_bps)), gaborium_bps[bps_order],
                color='k', alpha=.5, s=7, label='Gaborium')
    ax1.scatter(np.arange(len(backimage_bps)), backimage_bps[bps_order],
                color='g', alpha=.5, s=7, label='Backimage')

    # Add reference line at zero
    ax1.axhline(0, color='k')
    ax1.set_ylim(ylim)

    # Add labels and title
    ax1.set_title(f'Validation BPS: {val_bps[iU]:.2f}, Backimage: {backimage_bps[iU]:.2f}, Gaborium: {gaborium_bps[iU]:.2f}')
    ax1.set_xlabel('Unit (sorted)')
    ax1.set_ylabel('Bits per spike')
    ax1.legend()

    # Panel 3: Inter-spike interval (ISI) analysis
    ax2 = fig.add_subplot(gs[1, 1])
    plot_min_contam_prop(
        spike_times[spike_clusters == cid],
        min_contam_proportions[iU],
        refractory_periods,
        axs=ax2
    )
    ax2.set_title(f'ISI. Min Contamination Proportion - {min_contam_proportions[iU].min()*100:.1f}%')

    # Panel 4: Spike amplitude over time
    ax3 = fig.add_subplot(gs[2, 1])

    # Plot 2D histogram of spike times vs amplitudes
    ax3.hist2d(
        spike_times[spike_clusters == cid],
        spike_amplitudes[spike_clusters == cid],
        bins=(200, 50),
        cmap='Purples'
    )
    ax3.set_xlabel('Time (s)')
    ax3.set_ylabel('Amplitude (a.u.)')
    ax3.set_title(f'Amplitude vs Time. Median missing % {med_missing_pct[iU]:.1f}%')

    # Calculate positions for trial indicators
    ylim = ax3.get_ylim()
    ypos = ylim[0] + .6 * (ylim[1] - ylim[0])
    dy = (ylim[1] - ylim[0]) * .02

    # Add trial indicators for each dataset type
    # Backimage trials
    trials, trial_inds = np.unique(backimage_dset['trial_inds'], return_inverse=True)
    for iT in range(len(trials)):
        t_trial = backimage_dset['t_bins'][trial_inds == iT]
        ax3.plot([t_trial[0], t_trial[-1]], [ypos, ypos], color='C0')

    # Gaborium trials
    trials, trial_inds = np.unique(gaborium_dset['trial_inds'], return_inverse=True)
    for iT in range(len(trials)):
        t_trial = gaborium_dset['t_bins'][trial_inds == iT]
        ax3.plot([t_trial[0], t_trial[-1]], [ypos+dy, ypos+dy], color='C1')

    # Gratings trials
    trials, trial_inds = np.unique(gratings_dset['trial_inds'], return_inverse=True)
    for iT in range(len(trials)):
        t_trial = gratings_dset['t_bins'][trial_inds == iT]
        ax3.plot([t_trial[0], t_trial[-1]], [ypos+2*dy, ypos+2*dy], color='C2')

    # FixRSVP trials
    trials, trial_inds = np.unique(fixrsvp_dset['trial_inds'], return_inverse=True)
    for iT in range(len(trials)):
        t_trial = fixrsvp_dset['t_bins'][trial_inds == iT]
        ax3.plot([t_trial[0], t_trial[-1]], [ypos+3*dy, ypos+3*dy], color='C3')

    # Add legend for trial indicators
    legend = [
        plt.Line2D([0], [0], color='C0', lw=2, label='Backimage'),
        plt.Line2D([0], [0], color='C1', lw=2, label='Gaborium'),
        plt.Line2D([0], [0], color='C2', lw=2, label='Gratings'),
        plt.Line2D([0], [0], color='C3', lw=2, label='FixRSVP'),
    ]
    ax3.legend(handles=legend)
    ax3.set_xlim(t_min, t_max)

    # Panel 5: Spike-triggered averages (STAs)
    ax4 = fig.add_subplot(gs[0, 2])

    # Stack STAs and STEs for visualization
    stas_plot = torch.stack(
        [
            sta_data[iU],                              # Data STA
            sta[iU],                                   # Model STA
            ste_data[iU] - np.median(ste_data[iU]),    # Data STE (normalized)
            ste[iU] - torch.median(ste[iU]),           # Model STE (normalized)
        ], dim=0
    )[:, :, None, :, :]

    # Plot STAs and STEs
    plot_stas(
        stas_plot,
        row_labels=['STA', 'Model STA', 'STE', 'Model STE'],
        ax=ax4
    )

    # Panel 6: Perisaccadic PSTHs
    ax5 = fig.add_subplot(gs[1, 2])

    # Add vertical line at saccade onset
    ax5.axvline(0, color='k', linestyle='--', alpha=.5)

    # Plot Backimage PETH
    ax5.plot(lags, backimage_peth_rhat[:, iU]/dt, 'C0:', label='Backimage Predicted', alpha=1)
    ax5.plot(lags, backimage_peth_robs[:, iU]/dt, 'C0', label='Backimage Observed', alpha=.8)
    ax5.axhline(torch.median(backimage_peth_robs[:, iU])/dt, color='C0', linestyle='--', alpha=.3)

    # Plot Gaborium PETH
    ax5.plot(lags, gaborium_peth_rhat[:, iU]/dt, 'C1:', label='Gaborium Predicted', alpha=1)
    ax5.plot(lags, gaborium_peth_robs[:, iU]/dt, 'C1', label='Gaborium Observed', alpha=.8)
    ax5.axhline(torch.median(gaborium_peth_robs[:, iU])/dt, color='C1', linestyle='--', alpha=.3)

    # Plot Gratings PETH
    ax5.plot(lags, gratings_peth_rhat[:, iU]/dt, 'C2:', label='Gratings Predicted', alpha=1)
    ax5.plot(lags, gratings_peth_robs[:, iU]/dt, 'C2', label='Gratings Observed', alpha=.8)
    ax5.axhline(torch.median(gratings_peth_robs[:, iU])/dt, color='C2', linestyle='--', alpha=.3)

    # Add labels and legend
    ax5.set_ylabel('Firing rate (spikes / s)')
    ax5.set_title(f'Perisaccadic PSTH for unit {iU}')
    legend = [
        plt.Line2D([0], [0], color='C0', lw=2, label='Backimage'),
        plt.Line2D([0], [0], color='C1', lw=2, label='Gaborium'),
        plt.Line2D([0], [0], color='C2', lw=2, label='Gratings'),
        plt.Line2D([0], [0], color='k', lw=2, label='Observed'),
        plt.Line2D([0], [0], color='k', linestyle=':', lw=2, label='Predicted'),
    ]
    ax5.legend(handles=legend)
    ax5.set_xlabel('Time rel. saccade onset (ms)')

    # Panel 7: FixRSVP response
    ax6 = fig.add_subplot(gs[2, 2])

    # Plot observed and predicted PSTH
    ax6.plot(fixrsvp_psth[:, iU]/dt, 'k', label='Observed', alpha=.7)
    ax6.plot(fixrsvp_psth_pred[:, iU]/dt, 'r', label='Predicted')

    # Add labels and legend
    ax6.set_title(f'FixRSVP PSTH. $R^2$ = {fixrsvp_r2[iU]:.2f}')
    ax6.set_xlabel('Time (s)')
    ax6.set_ylabel('Firing rate (spikes / s)')
    ax6.legend()

    # Panel 8: Gratings STA
    ax7 = fig.add_subplot(gs[0, 3])
    plot_stas(gratings_sta[iU][None, :, None, :, :], ax=ax7)
    ax7.set_title(f'Gratings Tuning')
    ax7.set_xlabel('Lag')

    # Find peak lag and tuning
    lag, sf_idx, ori_idx = np.unravel_index(np.argmax(gratings_sta[iU]), gratings_sta[iU].shape)

    # Panel 9: Spatial frequency and orientation tuning
    ax8 = fig.add_subplot(gs[1, 3])

    # Plot tuning heatmap
    im = ax8.imshow(gratings_sta[iU, lag, :, :] * 240, cmap='viridis')

    # Mark the peak tuning
    ax8.plot([ori_idx], [sf_idx], 'rx', markersize=10)

    # Set tick labels
    ax8.set_xticks(np.arange(len(oris)), [f'{o:.2f}' for o in oris])
    ax8.set_yticks(np.arange(len(sfs)), [f'{s:.2f}' for s in sfs])

    # Add labels and colorbar
    ax8.set_title(f'Frequency and Orientation Tuning')
    ax8.set_xlabel('Orientation (degrees)')
    ax8.set_ylabel('Spatial Frequency (cycles/degree)')
    fig.colorbar(im, ax=ax8, label='Spikes / second', fraction=0.046, pad=0.04)


    # Panel 10: Phase Tuning
    ax9 = fig.add_subplot(gs[2, 3])

    # Find frames with the peak spatial frequency and orientation
    sf_ori_idx = np.where(sf_ori_one_hot[:, sf_idx, ori_idx] > 0)[0]

    # Only keep indices that have enough frames after lag
    sf_ori_idx = sf_ori_idx[(sf_ori_idx + lag) < len(gratings_robs)]

    # Extract phase information for each frame
    phases = gratings_dset['stim_phase'][sf_ori_idx].numpy()
    _, n_y, n_x = phases.shape

    # Take center pixel as phase
    # NOTE: Could center on STA/STE peak but in practice it doesn't matter
    phases = phases[:, n_y//2, n_x//2]

    # Get spikes at the optimal lag
    spikes = gratings_robs[sf_ori_idx + lag, iU]

    # Get data filtering status (valid data points)
    filters = gratings_dset['dfs'].numpy().squeeze()[sf_ori_idx + lag]

    # Filter out invalid data points
    # -1 indicates off screen or probe, 0 indicates sampled out of ROI
    invalid = (phases <= 0) | (filters == 0)
    phases = phases[~invalid]
    spikes = spikes[~invalid]

    # Count spikes per phase bin
    n_bins = 8
    phase_bin_edges = np.linspace(0, 2*np.pi, n_bins + 1)
    phase_bin_inds = np.digitize(phases, phase_bin_edges) - 1  # bin index for each phase

    # Initialize arrays for counts and statistics
    n_phases = np.zeros(n_bins)
    n_spikes = np.zeros(n_bins)
    spikes_ste = np.zeros(n_bins)

    # Calculate statistics for each phase bin
    for i in range(n_bins):
        n_phases = np.sum(phase_bin_inds == i)
        n_spikes[i] = spikes[phase_bin_inds == i].sum()
        # Calculate standard error if there are observations
        spikes_ste[i] = spikes[phase_bin_inds == i].std() / np.sqrt(n_phases) if n_phases > 0 else 0

    # Calculate spikes per phase and convert bin edges to degrees
    spikes_per_phase = n_spikes / n_phases
    phase_bin_centers = np.rad2deg((phase_bin_edges[:-1] + phase_bin_edges[1:]) / 2)

    # Plot phase tuning
    ax9.bar(phase_bin_centers, spikes_per_phase*240, width=360/n_bins, align='center')
    ax9.errorbar(phase_bin_centers, spikes_per_phase*240,
                yerr=spikes_ste*240, fmt='ro-', ecolor='red', capsize=5)

    # Add labels and formatting
    ax9.set_xlabel('Phase (degrees)')
    ax9.set_ylabel('Spikes / second')
    ax9.set_title(f'Phase tuning at SF {sfs[sf_idx]:.2f}, Ori {oris[ori_idx]:.2f}')
    ax9.set_xticks(np.rad2deg(phase_bin_edges))

    # Apply tight layout and save figure
    fig.tight_layout()
    pdf.savefig(fig)
    plt.close(fig)

# Close the PDF file
pdf.close()

# End of script
print(f"Evaluation complete. Results saved to {model_name}_eval.pdf")
# %%
