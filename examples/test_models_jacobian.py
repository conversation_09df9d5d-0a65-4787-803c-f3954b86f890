#!/usr/bin/env python
"""
Test script for Jacobian analysis of models built with the new build module.

This script instantiates different versions of models using configuration files:
1. Basic model with DAModel frontend
2. Model with modulator network

It then tests the Jacobian computation for each model to verify compatibility
with torch.func.jacrev.
"""

#%%
import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.func import jacrev, vmap
import time
from pathlib import Path

# Import the model building and config functions
from DataYatesV1.models import build_model
from DataYatesV1.models.config_loader import load_config

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

#%%
# Check if CUDA is available
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Create a directory for saving results
results_dir = Path("results/jacobian_test")
results_dir.mkdir(parents=True, exist_ok=True)

def create_test_stimulus(batch_size=1, channels=1, frames=20, height=16, width=16):
    """Create a test stimulus for Jacobian analysis."""
    # Create a random stimulus
    stimulus = torch.randn(batch_size, channels, frames, height, width,
                          dtype=torch.float32, device=device, requires_grad=True)
    return stimulus

def create_test_behavior(batch_size=1, vars=2, lags=10):
    """Create test behavioral data (e.g., eye positions)."""
    # Create random behavioral data
    behavior = torch.randn(batch_size, vars, lags,
                          dtype=torch.float32, device=device)
    return behavior

def test_model_consistency(model, stimulus, behavior=None, n_repeats=3):
    """Test if the model produces consistent outputs for repeated calls."""
    outputs = []
    for _ in range(n_repeats):
        with torch.no_grad():
            if behavior is not None and hasattr(model, 'modulator') and model.modulator is not None:
                output = model(stimulus, behavior)
            else:
                output = model(stimulus)
            outputs.append(output.clone())

    # Check if all outputs are identical
    is_consistent = all(torch.allclose(outputs[0], output) for output in outputs[1:])

    if is_consistent:
        print("✅ Model produces consistent outputs for repeated calls")
    else:
        print("❌ Model produces different outputs for repeated calls")
        for i, output in enumerate(outputs):
            print(f"Output {i} mean: {output.mean().item()}, std: {output.std().item()}")

    return is_consistent

def compute_jacobian_for_unit(model_fn, stimulus, unit_idx=0):
    """Compute Jacobian for a specific output unit."""
    # Create a function that returns only the specified unit's output
    def unit_output_fn(x):
        return model_fn(x)[0, unit_idx]

    # Compute Jacobian
    start_time = time.time()
    jacobian = jacrev(unit_output_fn)(stimulus)
    end_time = time.time()

    print(f"Jacobian computation took {end_time - start_time:.2f} seconds")
    print(f"Jacobian shape: {jacobian.shape}")

    return jacobian

def visualize_jacobian(jacobian, save_path=None):
    """Visualize the Jacobian for a specific time point."""
    # For visualization, we'll take the middle time point
    time_point = jacobian.shape[2] // 2

    # Sum across the channel dimension if it exists
    if jacobian.dim() > 4:  # (batch, channel, time, height, width)
        jac_slice = jacobian[0, :, time_point].sum(0)
    else:  # (batch, time, height, width)
        jac_slice = jacobian[0, time_point]

    # Create figure
    fig, ax = plt.subplots(figsize=(10, 8))

    # Plot the Jacobian
    im = ax.imshow(jac_slice.cpu().detach().numpy(), cmap='coolwarm')
    ax.set_title(f'Jacobian at time point {time_point}')
    plt.colorbar(im, ax=ax)

    # Save or show
    if save_path:
        plt.savefig(save_path)
        print(f"Saved Jacobian visualization to {save_path}")
    else:
        plt.show()

    plt.close()

def test_model_with_fixed_modulator(model, stimulus, fixed_modulator):
    """Create a wrapper function with fixed modulator for Jacobian analysis."""
    # Pre-compute the modulator output to avoid running LSTM during jacrev
    with torch.no_grad():
        if model.modulator is not None and fixed_modulator is not None:
            # Extract the modulator output directly
            modulator_output = model.modulator(fixed_modulator)
        else:
            modulator_output = None

    # Create a function that bypasses the modulator computation
    def model_fn(x):
        # Frontend processing
        x_frontend = model.frontend(x)

        # ConvNet processing
        x_convnet = model.convnet(x_frontend)

        # Recurrent processing with pre-computed modulator
        if hasattr(model.recurrent, 'with_modulator') and model.recurrent.with_modulator:
            x_recurrent = model.recurrent(x_convnet, modulator=modulator_output)
        else:
            x_recurrent = model.recurrent(x_convnet)

        # Readout
        output = model.readout(x_recurrent)

        # Apply activation if it exists
        if hasattr(model, 'activation') and model.activation is not None:
            output = model.activation(output)

        return output

    return model_fn

def main():
    # Parameters
    height = 32
    width = 32
    frames = 16
    batch_size = 2

    # Create test data
    stimulus = create_test_stimulus(batch_size=batch_size, channels=1, frames=frames, height=height, width=width)
    behavior = create_test_behavior(batch_size=batch_size, vars=2, lags=frames)

    print("\n" + "="*50)
    print("Testing Model 1: Basic model with DAModel frontend")
    print("="*50)

    # Load and build basic model
    basic_config = load_config("configs/jacobian_test_basic.yaml")
    basic_model = build_model(basic_config)
    basic_model.to(device)
    basic_model.eval()  # Ensure model is in eval mode

    # Test model consistency
    test_model_consistency(basic_model, stimulus)

    # Create model function for Jacobian analysis using the same wrapper
    basic_model_fn = test_model_with_fixed_modulator(basic_model, stimulus, None)

    # Compute and visualize Jacobian
    jacobian = compute_jacobian_for_unit(basic_model_fn, stimulus)
    visualize_jacobian(jacobian, save_path=results_dir / "basic_model_jacobian.png")

    print("\n" + "="*50)
    print("Testing Model 2: Model with modulator")
    print("="*50)

    # Load and build model with modulator
    modulator_config = load_config("configs/jacobian_test_modulator.yaml")
    modulator_model = build_model(modulator_config)
    modulator_model.to(device)
    modulator_model.eval()  # Ensure model is in eval mode

    # Test model consistency
    test_model_consistency(modulator_model, stimulus, behavior)

    # Create model function with fixed modulator for Jacobian analysis
    modulator_model_fn = test_model_with_fixed_modulator(modulator_model, stimulus, behavior)

    # Compute and visualize Jacobian
    jacobian = compute_jacobian_for_unit(modulator_model_fn, stimulus)
    visualize_jacobian(jacobian, save_path=results_dir / "modulator_model_jacobian.png")

if __name__ == "__main__":
    main()

# %%
