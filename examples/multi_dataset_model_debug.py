#!/usr/bin/env python
"""
Interactive multidataset model debugging script

This script sets up the same preprocessing as model_train_multi.py but allows
step-by-step debugging of the multidataset training process to identify issues.

Usage:
    python model_debug_multi.py --train-config configs/multidataset_train_config.yaml --device auto

Then run cells interactively to debug multidataset training step by step.
"""
#%%
import torch
from torch.utils.data import DataLoader
import argparse
import sys
import time
import numpy as np
import os
from pathlib import Path
import yaml

import lightning as pl
from lightning.pytorch.loggers import WandbLogger

from DataYatesV1.models import build_model, initialize_model_components, get_name_from_config
from DataYatesV1.models.config_loader import load_config, load_multidataset_config
from DataYatesV1.models.lightning import PLCoreVisionModel, MultiDatasetPLCore
from DataYatesV1.utils.data import prepare_data, prepare_multidataset_data
from DataYatesV1.utils.general import ensure_tensor
from DataYatesV1.utils.torch import get_free_device

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

#%% Parse arguments
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Debug multidataset neural network model training')

    parser.add_argument('--train-config', type=str,
                        default='examples/configs/multidataset_train_config.yaml',
                        help='Path to multidataset training configuration file')

    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use for training. Options: "auto", "cpu", "cuda", "cuda:0", etc.')

    parser.add_argument('--batch-size', type=int, default=16,
                        help='Batch size for debugging (smaller than training)')

    return parser.parse_args()

# Parse arguments when script is run
if __name__ == "__main__" and 'ipykernel' not in sys.modules:
    args = parse_arguments()
else:
    # Default arguments for interactive use
    class Args:
        train_config = 'configs/multidataset_train_config.yaml'
        device = 'cuda:0'
        batch_size = 16
    args = Args()

print(f"Using training config: {args.train_config}")
print(f"Device: {args.device}")
print(f"Batch size: {args.batch_size}")

#%% Setup device
def setup_device(device_spec):
    """Setup device with same logic as model_train_multi.py"""
    if device_spec is None or device_spec == 'auto':
        device = get_free_device()
        print(f"Auto-selected device: {device}")
    elif isinstance(device_spec, str):
        if device_spec.lower() == 'cpu':
            device = torch.device('cpu')
        elif device_spec.lower().startswith('cuda'):
            device = torch.device(device_spec)
        else:
            raise ValueError(f"Invalid device specification: {device_spec}")
        print(f"Using specified device: {device}")
    elif isinstance(device_spec, torch.device):
        device = device_spec
        print(f"Using device: {device}")
    else:
        raise ValueError(f"Invalid device type: {type(device_spec)}")
    
    return device

device = setup_device(args.device)

#%% Load configurations
print("Loading multidataset configurations...")
model_config, dataset_configs = load_multidataset_config(args.train_config)

print(f"Model type: {model_config.get('model_type')}")
print(f"Number of datasets: {len(dataset_configs)}")

for i, dataset_config in enumerate(dataset_configs):
    print(f"Dataset {i}:")
    print(f"  Frontend: {dataset_config.get('frontend', {}).get('type', 'none')}")
    print(f"  Readout: {dataset_config.get('readout', {}).get('type', 'none')}")
    print(f"  CIDs: {len(dataset_config.get('cids', []))} units")

#%% Prepare multidataset data
print("Preparing multidataset data...")
train_datasets_dict, val_datasets_dict, updated_dataset_configs = prepare_multidataset_data(dataset_configs)

print(f"Data preparation complete!")
for i, (dataset_name, train_dset) in enumerate(train_datasets_dict.items()):
    val_dset = val_datasets_dict[dataset_name]
    config = updated_dataset_configs[i]
    print(f"  {dataset_name}: {len(train_dset)} train, {len(val_dset)} val, {len(config['cids'])} units")

#%% Create multidataset data loaders
def create_multidataset_loaders(train_datasets_dict, val_datasets_dict, batch_size=16):
    """Create DataLoader objects for multidataset debugging."""
    from lightning.pytorch.utilities import CombinedLoader
    
    # Create individual dataloaders for each dataset
    train_loaders = {}
    val_loaders = {}
    
    for dataset_name, train_dset in train_datasets_dict.items():
        train_loader = DataLoader(
            train_dset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=1,  # Reduced for debugging
            pin_memory=True
        )
        train_loaders[dataset_name] = train_loader
    
    for dataset_name, val_dset in val_datasets_dict.items():
        val_loader = DataLoader(
            val_dset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=1,  # Reduced for debugging
            pin_memory=True
        )
        val_loaders[dataset_name] = val_loader
    
    # Combine loaders using Lightning's CombinedLoader
    combined_train_loader = CombinedLoader(train_loaders, mode="max_size_cycle")
    combined_val_loader = CombinedLoader(val_loaders, mode="max_size_cycle")
    
    return combined_train_loader, combined_val_loader

train_loader, val_loader = create_multidataset_loaders(
    train_datasets_dict, val_datasets_dict, batch_size=args.batch_size
)
print(f"Created multidataset loaders with batch size: {args.batch_size}")

#%% Calculate baseline firing rates for each dataset
print("Calculating baseline firing rates for each dataset...")
baseline_rates_list = []

for dataset_idx, (dataset_name, dataset_config) in enumerate(zip(train_datasets_dict.keys(), updated_dataset_configs)):
    train_dset = train_datasets_dict[dataset_name]
    
    fr = 0
    n = 0
    for dset in train_dset.dsets:
        fr += dset.covariates['robs'].sum(0)
        n += dset.covariates['robs'].shape[0]
    baseline_rates = fr / n
    baseline_rates_list.append(baseline_rates)
    
    print(f"Dataset {dataset_idx}:")
    print(f"  Baseline rates shape: {baseline_rates.shape}")
    print(f"  Baseline rates range: [{baseline_rates.min():.6f}, {baseline_rates.max():.6f}]")
    print(f"  Baseline rates mean: {baseline_rates.mean():.6f}")
    
    # Check for any issues with baseline rates
    if torch.any(torch.isnan(baseline_rates)):
        print(f"  ❌ WARNING: NaN values found in dataset {dataset_idx} baseline rates!")
    if torch.any(baseline_rates <= 0):
        print(f"  ❌ WARNING: Zero or negative baseline rates found in dataset {dataset_idx}!")
        print(f"     Number of zero/negative rates: {(baseline_rates <= 0).sum()}")

inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta
baseline_rates_list = [inv_softplus(rates) for rates in baseline_rates_list]

#%% Create multidataset model
print("Creating multidataset model...")
model_name = get_name_from_config(model_config)
print(f"Model name: {model_name}")

# Create Lightning module
pl_model = MultiDatasetPLCore(
    model_class=build_model,
    model_config=model_config,
    dataset_configs=updated_dataset_configs,
    optimizer='AdamW',
    optim_kwargs={'lr': 1e-3, 'weight_decay': 1e-5},
    accumulate_grad_batches=1,
    dataset_info=updated_dataset_configs
)

# Initialize model components
print("Initializing model components...")
initialize_model_components(pl_model.model, init_bias=baseline_rates_list)

print(f"Multidataset model created successfully!")
print(f"Model type: {type(pl_model.model).__name__}")
print(f"Number of datasets: {pl_model.num_datasets}")
print(f"Model device: {next(pl_model.model.parameters()).device}")

# Print model structure
print("\nModel structure:")
print(f"  Shared convnet: {type(pl_model.model.convnet).__name__}")
print(f"  Shared modulator: {type(pl_model.model.modulator).__name__ if pl_model.model.modulator else 'None'}")
print(f"  Shared recurrent: {type(pl_model.model.recurrent).__name__}")

for i, (frontend, readout) in enumerate(zip(pl_model.model.frontends, pl_model.model.readouts)):
    print(f"  Dataset {i} frontend: {type(frontend).__name__}")
    print(f"  Dataset {i} readout: {type(readout).__name__} ({readout.n_units} units)")

#%% Get a single batch for debugging
print("Getting a single batch for debugging...")
train_iter = iter(train_loader)
batch_data = next(train_iter)

# Handle CombinedLoader batch format
if isinstance(batch_data, (list, tuple)):
    batch_dict = batch_data[0] if isinstance(batch_data[0], dict) else batch_data
else:
    batch_dict = batch_data

print(f"Batch dict keys: {list(batch_dict.keys())}")
for dataset_name, batch in batch_dict.items():
    print(f"\n{dataset_name}:")
    for key, value in batch.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: shape={value.shape}, dtype={value.dtype}, device={value.device}")
            print(f"    range=[{value.min():.6f}, {value.max():.6f}], mean={value.mean():.6f}")
            if torch.any(torch.isnan(value)):
                print(f"    ❌ WARNING: NaN values found in {dataset_name}.{key}!")
            if torch.any(torch.isinf(value)):
                print(f"    ❌ WARNING: Inf values found in {dataset_name}.{key}!")

#%% Move model to device
print(f"Moving model to device: {device}")
pl_model = pl_model.to(device)

# Move batch to device
for dataset_name, batch in batch_dict.items():
    batch_dict[dataset_name] = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
print("Batch moved to device")

print("\n" + "="*60)
print("SETUP COMPLETE - Ready for interactive multidataset debugging!")
print("="*60)
print("\nNext steps:")
print("1. Run forward pass: output_dict = pl_model(batch_dict)")
print("2. Check outputs for each dataset")
print("3. Compute loss: loss = pl_model.training_step(batch_dict, 0)")
print("4. Debug step by step...")
print("\nAvailable variables:")
print("- pl_model: MultiDatasetPLCore Lightning module")
print("- batch_dict: Combined batch from all datasets")
print("- model_config: Main model configuration")
print("- updated_dataset_configs: List of dataset configurations")
print("- baseline_rates_list: Baseline firing rates for each dataset")

#%% Step 1: Forward pass through multidataset model
print("\n=== STEP 1: Multidataset Forward pass ===")
pl_model.eval()  # Set to eval mode for debugging
with torch.no_grad():
    try:
        output_dict = pl_model(batch_dict)
        print(f"Forward pass successful!")

        for dataset_name, batch in output_dict.items():
            output = batch['rhat']
            print(f"\n{dataset_name}:")
            print(f"  Output shape: {output.shape}")
            print(f"  Output dtype: {output.dtype}")
            print(f"  Output device: {output.device}")
            print(f"  Output range: [{output.min():.6f}, {output.max():.6f}]")
            print(f"  Output mean: {output.mean():.6f}")
            print(f"  Output std: {output.std():.6f}")

            # Check for problematic values
            if torch.any(torch.isnan(output)):
                print(f"  ❌ WARNING: NaN values found in {dataset_name} output!")
                nan_count = torch.isnan(output).sum()
                print(f"     Number of NaN values: {nan_count}")
            else:
                print(f"  ✅ No NaN values in {dataset_name} output")

            if torch.any(torch.isinf(output)):
                print(f"  ❌ WARNING: Inf values found in {dataset_name} output!")
                inf_count = torch.isinf(output).sum()
                print(f"     Number of Inf values: {inf_count}")
            else:
                print(f"  ✅ No Inf values in {dataset_name} output")

            # Check if output is all zeros
            if torch.all(output == 0):
                print(f"  ❌ WARNING: {dataset_name} output is all zeros!")
            else:
                print(f"  ✅ {dataset_name} output has non-zero values")

    except Exception as e:
        print(f"❌ Forward pass failed with error: {e}")
        import traceback
        traceback.print_exc()

#%% Step 2: Check individual model components for each dataset
print("\n=== STEP 2: Component-wise debugging for each dataset ===")

def debug_multidataset_components(model, batch_dict):
    """Debug each component of the multidataset model separately"""
    print("Debugging multidataset model components...")

    for dataset_idx, (dataset_name, batch) in enumerate(batch_dict.items()):
        print(f"\n--- Dataset {dataset_idx} ({dataset_name}) ---")

        # Get input
        x = batch['stim']
        behavior = batch.get('behavior', None)
        print(f"Input shape: {x.shape}, range: [{x.min():.6f}, {x.max():.6f}]")
        if behavior is not None:
            print(f"Behavior shape: {behavior.shape}, range: [{behavior.min():.6f}, {behavior.max():.6f}]")

        # Dataset-specific frontend
        try:
            x_frontend = model.frontends[dataset_idx](x)
            print(f"Frontend output shape: {x_frontend.shape}, range: [{x_frontend.min():.6f}, {x_frontend.max():.6f}]")
            if torch.any(torch.isnan(x_frontend)):
                print("❌ NaN in frontend output!")
            if torch.any(torch.isinf(x_frontend)):
                print("❌ Inf in frontend output!")
        except Exception as e:
            print(f"❌ Frontend failed: {e}")
            continue

        # Shared convnet
        try:
            x_conv = model.convnet(x_frontend)
            print(f"Convnet output shape: {x_conv.shape}, range: [{x_conv.min():.6f}, {x_conv.max():.6f}]")
            if torch.any(torch.isnan(x_conv)):
                print("❌ NaN in convnet output!")
            if torch.any(torch.isinf(x_conv)):
                print("❌ Inf in convnet output!")
        except Exception as e:
            print(f"❌ Convnet failed: {e}")
            continue

        # Shared modulator (if exists and behavior provided)
        if model.modulator is not None and behavior is not None:
            try:
                x_mod = model.modulator(x_conv, behavior)
                print(f"Modulator output shape: {x_mod.shape}, range: [{x_mod.min():.6f}, {x_mod.max():.6f}]")
                if torch.any(torch.isnan(x_mod)):
                    print("❌ NaN in modulator output!")
                if torch.any(torch.isinf(x_mod)):
                    print("❌ Inf in modulator output!")
            except Exception as e:
                print(f"❌ Modulator failed: {e}")
                continue
        else:
            x_mod = x_conv
            print("No modulator or behavior data")

        # Shared recurrent
        try:
            x_rec = model.recurrent(x_mod)
            print(f"Recurrent output shape: {x_rec.shape}, range: [{x_rec.min():.6f}, {x_rec.max():.6f}]")
            if torch.any(torch.isnan(x_rec)):
                print("❌ NaN in recurrent output!")
            if torch.any(torch.isinf(x_rec)):
                print("❌ Inf in recurrent output!")
        except Exception as e:
            print(f"❌ Recurrent failed: {e}")
            continue

        # Dataset-specific readout
        try:
            x_out = model.readouts[dataset_idx](x_rec)
            print(f"Readout output shape: {x_out.shape}, range: [{x_out.min():.6f}, {x_out.max():.6f}]")
            if torch.any(torch.isnan(x_out)):
                print("❌ NaN in readout output!")
            if torch.any(torch.isinf(x_out)):
                print("❌ Inf in readout output!")
        except Exception as e:
            print(f"❌ Readout failed: {e}")
            continue

    print("Component debugging complete!")

# Run component debugging
debug_multidataset_components(pl_model.model, batch_dict)

#%% Step 3: Training step debugging
print("\n=== STEP 3: Multidataset training step debugging ===")
pl_model.train()  # Set back to training mode

try:
    # Manual training step
    print("Running multidataset training step...")
    loss = pl_model.training_step(batch_dict, 0)
    print(f"Training step successful!")
    print(f"Combined loss value: {loss}")
    print(f"Loss dtype: {loss.dtype}")

    if torch.isnan(loss):
        print("❌ COMBINED LOSS IS NaN!")
    elif torch.isinf(loss):
        print("❌ COMBINED LOSS IS Inf!")
    else:
        print("✅ Combined loss is finite")

except Exception as e:
    print(f"❌ Training step failed: {e}")
    import traceback
    traceback.print_exc()

#%% Step 4: Per-dataset loss computation debugging
print("\n=== STEP 4: Per-dataset loss computation debugging ===")

def debug_multidataset_loss_computation(pl_model, batch_dict):
    """Debug the loss computation step by step for each dataset"""
    print("Debugging multidataset loss computation...")

    # Get model outputs for all datasets
    output_dict = pl_model(batch_dict)

    total_loss = 0.0
    dataset_losses = {}

    for dataset_idx, (dataset_name, batch) in enumerate(output_dict.items()):
        print(f"\n--- Dataset {dataset_idx} ({dataset_name}) Loss Debug ---")

        output = batch['rhat']
        target = batch['robs']

        print(f"Model output shape: {output.shape}")
        print(f"Target shape: {target.shape}")
        print(f"Output range: [{output.min():.6f}, {output.max():.6f}]")
        print(f"Target range: [{target.min():.6f}, {target.max():.6f}]")

        # Check if shapes match
        if output.shape != target.shape:
            print(f"❌ Shape mismatch! Output: {output.shape}, Target: {target.shape}")
            continue

        # Check for problematic values before loss computation
        if torch.any(torch.isnan(output)):
            print("❌ NaN in model output before loss!")
        if torch.any(torch.isnan(target)):
            print("❌ NaN in target before loss!")
        if torch.any(output < 0):
            print(f"❌ Negative values in output: {(output < 0).sum()} values")
        if torch.any(target < 0):
            print(f"❌ Negative values in target: {(target < 0).sum()} values")

        # Compute loss for this dataset using the model's loss function
        try:
            loss = pl_model.loss_fn(batch)
            dataset_losses[dataset_name] = loss.detach()

            # Scale loss by number of datasets (as done in training)
            scaled_loss = loss / pl_model.num_datasets
            total_loss += scaled_loss

            print(f"Dataset loss: {loss.item():.6f}")
            print(f"Scaled loss: {scaled_loss.item():.6f}")

            if torch.isnan(loss):
                print(f"❌ {dataset_name} loss is NaN!")
            elif torch.isinf(loss):
                print(f"❌ {dataset_name} loss is Inf!")
            else:
                print(f"✅ {dataset_name} loss is finite")

        except Exception as e:
            print(f"❌ Loss computation failed for {dataset_name}: {e}")

    print(f"\nCombined loss: {total_loss.item():.6f}")
    print("Per-dataset losses:")
    for dataset_name, loss in dataset_losses.items():
        print(f"  {dataset_name}: {loss.item():.6f}")

# Run loss debugging
debug_multidataset_loss_computation(pl_model, batch_dict)

print("\n" + "="*60)
print("MULTIDATASET DEBUGGING COMPLETE!")
print("="*60)
print("\nSummary:")
print(f"- Model type: {type(pl_model.model).__name__}")
print(f"- Number of datasets: {pl_model.num_datasets}")
print(f"- Total parameters: {sum(p.numel() for p in pl_model.parameters()):,}")
print(f"- Device: {device}")
print("\nYou can now explore the model interactively!")
print("Try running individual forward passes, checking gradients, etc.")
