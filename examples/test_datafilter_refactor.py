#!/usr/bin/env python3
"""
Interactive test script for the datafilter refactor.

This script tests the new datafilter pipeline system and compares it with the old
get_valid_dfs approach to ensure compatibility.
"""

# %%
import sys
import os
import numpy as np
import torch
import yaml
from pathlib import Path

# Add the project root to the path
if __name__ == "__main__":
    # Running as script
    project_root = Path(__file__).parent.parent
else:
    # Running interactively
    project_root = Path.cwd()

sys.path.insert(0, str(project_root))

from DataYatesV1.utils.data.loading import prepare_data
from DataYatesV1.utils.data.filtering import get_valid_dfs
from DataYatesV1.utils.data.datasets import DictDataset
from DataYatesV1.utils.io import get_session

print(f"Project root: {project_root}")
print(f"Working directory: {os.getcwd()}")

# %%
# Test 1: Load the new config with datafilters
print("=" * 60)
print("TEST 1: Loading data with new datafilter pipeline")
print("=" * 60)

config_path = project_root / "examples/configs/test_data_simple_datafilters.yaml"
print(f"Loading config: {config_path}")

with open(config_path, 'r') as f:
    config = yaml.safe_load(f)

print("Config loaded successfully!")
print(f"Datafilters section: {config.get('datafilters', 'NOT FOUND')}")

# %%
# Test 2: Prepare data using new system
print("\n" + "=" * 60)
print("TEST 2: Preparing data with new system")
print("=" * 60)

try:
    train_dset, val_dset, updated_config = prepare_data(config)
    print(f"✓ Data preparation successful!")
    print(f"✓ Train dataset size: {len(train_dset)}")
    print(f"✓ Val dataset size: {len(val_dset)}")
    
    # Check if dfs field exists
    sample = train_dset[0]
    print(f"✓ Sample keys: {list(sample.keys())}")
    if 'dfs' in sample:
        print(f"✓ DFS shape: {sample['dfs'].shape}")
        print(f"✓ DFS dtype: {sample['dfs'].dtype}")
        print(f"✓ DFS range: [{sample['dfs'].min():.3f}, {sample['dfs'].max():.3f}]")
    else:
        print("✗ DFS field not found in sample!")
        
except Exception as e:
    print(f"✗ Error during data preparation: {e}")
    import traceback
    traceback.print_exc()

# %%
# Test 3: Compare with old system
print("\n" + "=" * 60)
print("TEST 3: Comparing with old get_valid_dfs system")
print("=" * 60)

try:
    # Load raw dataset manually
    sess = get_session("Allen", "2022-04-13")
    dset_path = sess.sess_dir / "shifter" / "gaborium_shifted.dset"
    raw_dset = DictDataset.load(dset_path)
    
    # Apply old method
    n_lags = 16  # from config
    old_dfs = get_valid_dfs(raw_dset, n_lags)
    
    print(f"✓ Old DFS shape: {old_dfs.shape}")
    print(f"✓ Old DFS dtype: {old_dfs.dtype}")
    print(f"✓ Old DFS range: [{old_dfs.min():.3f}, {old_dfs.max():.3f}]")
    
    # Get new DFS from prepared dataset
    # Find the gaborium dataset in the preprocessed datasets
    from DataYatesV1.utils.data.datafilters import make_datafilter_pipeline
    
    # Create the same datafilter pipeline as in config
    datafilter_ops = [{"valid_nlags": {"n_lags": 16}}]
    datafilter_pipeline = make_datafilter_pipeline(datafilter_ops)
    new_dfs = datafilter_pipeline(raw_dset)
    
    print(f"✓ New DFS shape: {new_dfs.shape}")
    print(f"✓ New DFS dtype: {new_dfs.dtype}")
    print(f"✓ New DFS range: [{new_dfs.min():.3f}, {new_dfs.max():.3f}]")
    
    # Compare
    if torch.allclose(old_dfs, new_dfs):
        print("✓ OLD AND NEW DFS ARE IDENTICAL!")
    else:
        print("✗ DFS values differ!")
        diff = torch.abs(old_dfs - new_dfs)
        print(f"  Max difference: {diff.max():.6f}")
        print(f"  Mean difference: {diff.mean():.6f}")
        print(f"  Number of differing elements: {(diff > 1e-6).sum()}")
        
except Exception as e:
    print(f"✗ Error during comparison: {e}")
    import traceback
    traceback.print_exc()

# %%
# Test 4: Test datafilter registry
print("\n" + "=" * 60)
print("TEST 4: Testing datafilter registry")
print("=" * 60)

try:
    from DataYatesV1.utils.data.datafilters import DATAFILTER_REGISTRY
    
    print("Available datafilters:")
    for name in DATAFILTER_REGISTRY.keys():
        print(f"  - {name}")
    
    # Test creating a valid_nlags filter directly
    valid_nlags_fn = DATAFILTER_REGISTRY["valid_nlags"]({"n_lags": 10})
    test_dfs = valid_nlags_fn(raw_dset)
    print(f"✓ Direct filter creation successful!")
    print(f"✓ Test DFS shape: {test_dfs.shape}")
    
except Exception as e:
    print(f"✗ Error testing registry: {e}")
    import traceback
    traceback.print_exc()

# %%
# Test 5: Test with old config (should show warning)
print("\n" + "=" * 60)
print("TEST 5: Testing with old config (should show warning)")
print("=" * 60)

try:
    # Create a simple old-style config without problematic transforms
    old_config = {
        'types': ['gaborium'],
        'keys_lags': {'robs': 0, 'stim': [0, 1, 2, 3, 4], 'dfs': 0},
        'transforms': {'stim': {'source': 'stim', 'ops': [{'pixelnorm': {}}], 'expose_as': 'stim'}},
        'train_val_split': 0.8,
        'cids': [6, 10, 15, 17, 18, 19, 20],
        'seed': 1002,
        'session': 'Allen_2022-04-13'
    }

    print("Old config datafilters section:", old_config.get('datafilters', 'NOT FOUND'))

    # This should show a warning about missing datafilters
    print("\nPreparing data (should show warning):")
    train_dset_old, val_dset_old, _ = prepare_data(old_config)
    print(f"✓ Old config still works: train={len(train_dset_old)}, val={len(val_dset_old)}")

except Exception as e:
    print(f"✗ Error with old config: {e}")
    import traceback
    traceback.print_exc()

# %%
print("\n" + "=" * 60)
print("SUMMARY")
print("=" * 60)
print("If all tests passed, the datafilter refactor is working correctly!")
print("The new system should be backward compatible and provide the same results.")
print("You can now use datafilters in your configs for more flexible data filtering.")
