#!/usr/bin/env python
"""
Example script for model loading from the registry

This script demonstrates:
1. Loading a model from a registry
2. Run model on a batch
3. Compute and visualize Jacobians to analyze instantaneous receptive fields
4. Analyze how receptive fields change dynamically based on input
"""
#%%
import torch
from torch.utils.data import DataLoader
from torch.func import jacrev, vmap
import time
import torch.cuda.amp as amp  # For mixed precision

import lightning as pl

from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import Normalize

from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1 import get_session
# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Check if CUDA is available
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Global settings
SAVE_FIGURES = True  # Set to True to save figures to disk
DISPLAY_FIGURES = True  # Set to True to display figures inline

# Create a directory for saving results (only used if SAVE_FIGURES is True)
results_dir = Path("results/jacobian_analysis")
if SAVE_FIGURES:
    results_dir.mkdir(parents=True, exist_ok=True)

# Import animation tools for dynamic visualizations
from matplotlib import animation
from IPython.display import HTML


#%%
# Import memory monitoring tools
import psutil
import gc
import time
from functools import wraps

#%%
data_config_path = "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/Allen_2022_04_13_eyevel_16_lags.yaml"
registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry")
registry = ModelRegistry(registry_dir)

sess = get_session('Allen', '2022-04-13')

# manually specify a model

# convgru
best_model =registry.load_model('v1_da_dense_convgru_gaussian_epoch_25')
# densegaussian
# best_model = registry.load_model('v1_da_dense_gaussian_epoch_09')

# # Find and load the best model from the registry
# best_model, model_entry = registry.get_best_model(
#         metric='val_loss',
#         mode='min',
#         config_match={'model_type': 'v1'},
#         dataset_match={'session': sess.name, 'types': ['gaborium', 'backimage']}
#     )

model = best_model.model.to(device)

# prepared_data = prepare_data(model_entry['dataset_info'])
import yaml
with open(data_config_path, 'r') as f:
    dataset_config = yaml.safe_load(f)

dataset_config['keys_lags']['eyepos'] = list(range(16))
train_data, val_data, dataset_config = prepare_data(dataset_config)



#%%
start = 20000
inds = np.where(val_data.inds[:,0]==1)[0]
bsize = 256

start += bsize
binds = inds[np.arange(bsize) + start]
batch = val_data[binds]

# plot the stimulus
ax = plt.subplot()
ax.imshow(batch['stim'][0][0], aspect='auto', cmap='gray')
# get axis with different x and y that overlays ontop of the gca
ax1 = ax.inset_axes([0,0,1,1])
ax1.patch.set_alpha(0)
ax1.plot(batch['eyepos'][0][:,0], batch['eyepos'][0][:,1], 'r')
ax1.set_xlim([-10, 10])
ax1.set_ylim([-10, 10])


#%% Find saccade times
import json
saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
saccade_times = torch.sort(torch.tensor([s['start_time'] for s in saccades])).values.numpy()
saccade_times = saccade_times[np.diff(saccade_times, prepend=0) > 0.1]
_ = plt.hist(np.diff(saccade_times, prepend=0), np.linspace(0, 1, 100))
plt.xlabel('Time (s)')
plt.ylabel('Count')
plt.title('Saccade ISI distribution')

#%%
dset_id = 1
inds = np.where(val_data.inds[:,0]==dset_id)[0] # indices that map to this dataset
t_bins = val_data.dsets[dset_id]['t_bins'][val_data.dset_inds[dset_id]]

sacc_ind = np.digitize(saccade_times, t_bins)
valid_saccades = (sacc_ind > 0) & (sacc_ind < len(t_bins))
# and saccade isn't within 100ms of the next saccade
valid_saccades[:-1] = valid_saccades[:-1] & (np.diff(sacc_ind) > 20)

saccade_times = saccade_times[valid_saccades]
sacc_ind = sacc_ind[valid_saccades]

saccade_indices = inds[sacc_ind - 1]

#%% check these are valid saccades
n_sample = 12
fig, axs = plt.subplots(n_sample//4, 4, figsize=(10, 2))
for ii, i in enumerate(np.random.choice(len(saccade_indices), n_sample, replace=False)):
    bind = np.arange(-10, 100) + saccade_indices[i]
    batch = val_data[bind]
    axs[ii//4, ii%4].plot(batch['eyepos'][:,0,:])
    axs[ii//4, ii%4].axvline(10, color='k', linestyle='--')

#%% plot saccade triggered average
robs = 0
window_size = 100
Nsac = len(saccade_indices)
from tqdm import tqdm
for i in tqdm(saccade_indices):
    bind = np.arange(-10, 100) + i
    robs += val_data[bind]['robs']

#%%
_ = plt.plot(robs/Nsac)

j = 1
# %%
%matplotlib inline
prewin = 10
j += 10
long_fix = np.where(np.diff(saccade_indices)>100)[0]
i = long_fix[j]
bind = np.arange(saccade_indices[i]-prewin, saccade_indices[i+1])
print(len(bind))
batch = val_data[bind]
plt.imshow(batch['stim'][50,0].cpu())
plt.show()
#%%

# #%%
# # batch = {k: v.to(device) for k, v in batch.items() if isinstance(v, torch.Tensor)}
# stim = batch['stim'].to(device)
# stim.requires_grad = True

# pred = model(stim)

#%%
stim = batch['stim'].to(device)
spikes = batch['robs'].to(device)
model.eval()
model.convnet.use_checkpointing = False 

# %%
poisson_loss = torch.nn.PoissonNLLLoss(log_input=False, full=False, reduction='none')

def pink_noise_like(shape, device=None, beta=1.0):
    """
    shape : (T, C, H, W)
    beta  : 1 → pink;   0 → white;   2 → brown
    returns a tensor with std≈1
    """
    T, C, H, W = shape
    device = device or 'cpu'

    # frequency grid (H×W)
    fy = torch.fft.fftfreq(H, d=1.0, device=device).reshape(H, 1)
    fx = torch.fft.fftfreq(W, d=1.0, device=device).reshape(1, W)
    f  = torch.sqrt(fx ** 2 + fy ** 2)
    f[0, 0] = 1.0                           # avoid division by zero
    filt = (1. / f ** (beta / 2)).unsqueeze(0).unsqueeze(0)  # (1,1,H,W)

    # draw white noise and filter each frame
    white = torch.randn(shape, device=device)
    noise_fft = torch.fft.rfft2(white, norm='ortho')          # (T,C,H,W//2+1)
    filtered  = torch.fft.irfft2(noise_fft * filt[..., : W // 2 + 1],
                                 s=(H, W), norm='ortho')

    # normalise variance ⟹ std ≈ 1
    filtered = filtered / filtered.std()

    return filtered

def lag_embed(x, S):
    # x : (T, C, H, W)
    T, C, H, W = x.shape
    x2d = x.reshape(T, -1).unsqueeze(0)               # (1, T, C·H·W)
    eye = torch.eye(S, device=x.device).flip(0)       # causal window
    weight = eye.repeat_interleave(C*H*W, dim=0)      # (C·H·W, S)
    conv = torch.nn.functional.conv1d(
        x2d.permute(0,2,1), weight.unsqueeze(1), groups=C*H*W
    )                                                 # (1, C·H·W, T-S+1)
    embed = conv.squeeze(0).T.reshape(T-S+1, C, S, H, W)
    return embed

SCALE = 1

def objective(movie, spikes, λ=1e-2):
    embed = lag_embed(movie, 16)              # (T-S+1,...)
    r      = model(embed)                    # forward model
    ll     = poisson_loss( SCALE* r, spikes[16-1:])   
    smooth = λ * torch.mean((movie[:, :, :, 1:] - movie[:, :, :, :-1])**2)
    return ll + smooth


# movie = torch.randn_like(stim[:,[0],:,:])
movie = pink_noise_like(stim[:,[0],:,:].shape, device=device)
movie.requires_grad = True

opt   = torch.optim.Adam([movie], lr=1e-2)

max_iter   = 2000
tol        = 1e-4          # stop when absolute loss change < tol
print_every= 5            # reporting cadence

prev_loss  = None
with torch.enable_grad():
    for it in range(max_iter):
        opt.zero_grad()
        loss = objective(movie, spikes).sum()          # already scalar
        loss.backward()
        opt.step()

        # -------- convergence / reporting --------
        cur = loss.item()
        if prev_loss is not None:
            delta = prev_loss - cur              # improvement (positive ⇒ better)
            if it % print_every == 0:
                print(f"{it:4d}  loss={cur:.6f}  Δ={delta:+.6e}")
            if abs(delta) < tol:
                print(f"Converged at iter {it} (Δ={delta:+.3e} < {tol})")
                break
        prev_loss = cur


# %%

plt.imshow(movie.detach().cpu().numpy()[60,0])
# %%