#!/usr/bin/env python
"""
Interactive model debugging script

This script sets up the same preprocessing as model_train.py but allows
step-by-step debugging of the training process to identify issues like NaN losses.

Usage:
    python model_debug.py --config configs/v1_cones_dense_gaussian_standard_mish.yaml --device auto

Then run cells interactively to debug training step by step.
"""
#%%
import torch
from torch.utils.data import DataLoader
import argparse
import sys
import time
import numpy as np
import os
from pathlib import Path
import yaml

import lightning as pl
from lightning.pytorch.loggers import WandbLogger

from DataYatesV1.models import build_model, initialize_model_components, get_name_from_config
from DataYatesV1.models.config_loader import load_config
from DataYatesV1.models.lightning import PLCoreVisionModel
from DataYatesV1.utils.data import prepare_data
from DataYatesV1.utils.general import ensure_tensor
from DataYatesV1.utils.torch import get_free_device

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

#%% Parse arguments
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Debug neural network model training')

    parser.add_argument('--config', type=str,
                        default='configs/test_model_stimembed.yaml',
                        help='Path to model configuration file')

    parser.add_argument('--dataset-config', type=str,
                        default='configs/test_data_stimembed.yaml',
                        help='Path to dataset configuration file')

    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use for training. Options: "auto", "cpu", "cuda", "cuda:0", etc.')

    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for debugging (smaller than training)')

    return parser.parse_args()

# Parse arguments when script is run
if __name__ == "__main__" and 'ipykernel' not in sys.modules:
    args = parse_arguments()
else:
    # Default arguments for interactive use
    class Args:
        config = 'configs/test_model_stimembed.yaml'
        dataset_config = 'configs/test_data_stimembed.yaml'
        device = 'cuda:0'
        batch_size = 32
    args = Args()

print(f"Using config: {args.config}")
print(f"Using dataset config: {args.dataset_config}")
print(f"Device: {args.device}")
print(f"Batch size: {args.batch_size}")

#%% Setup device
def setup_device(device_spec):
    """Setup device with same logic as model_train.py"""
    if device_spec is None or device_spec == 'auto':
        device = get_free_device()
        print(f"Auto-selected device: {device}")
    elif isinstance(device_spec, str):
        if device_spec.lower() == 'cpu':
            device = torch.device('cpu')
        elif device_spec.lower().startswith('cuda'):
            device = torch.device(device_spec)
        else:
            raise ValueError(f"Invalid device specification: {device_spec}")
        print(f"Using specified device: {device}")
    elif isinstance(device_spec, torch.device):
        device = device_spec
        print(f"Using device: {device}")
    else:
        raise ValueError(f"Invalid device type: {type(device_spec)}")
    
    return device

device = setup_device(args.device)

#%% Load configurations
config_path = Path(args.config)
dataset_config_path = Path(args.dataset_config)

print("Loading configurations...")
with open(dataset_config_path, 'r') as f:
    dataset_config = yaml.safe_load(f)

config = load_config(config_path)

#%% Prepare data
print("Preparing data...")
train_dset, val_dset, dataset_config = prepare_data(dataset_config)

# Update config with number of units
config['readout']['params']['n_units'] = len(dataset_config['cids'])

print(f"Training dataset size: {len(train_dset)}")
print(f"Validation dataset size: {len(val_dset)}")
print(f"Number of output units: {len(dataset_config['cids'])}")

#%% Create data loaders
def create_dataloaders(train_dset, val_dset, batch_size=32):
    """Create DataLoader objects for debugging."""
    train_loader = DataLoader(
        train_dset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=2,  # Reduced for debugging
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=2,  # Reduced for debugging
        pin_memory=True
    )

    return train_loader, val_loader

train_loader, val_loader = create_dataloaders(train_dset, val_dset, batch_size=args.batch_size)
print(f"Created data loaders with batch size: {args.batch_size}")

#%% Calculate baseline firing rates
print("Calculating baseline firing rates...")
fr = 0
n = 0
for dset in train_loader.dataset.dsets:
    fr += dset.covariates['robs'].sum(0)
    n += dset.covariates['robs'].shape[0]
baseline_rates = fr / n
print(f"Baseline firing rates shape: {baseline_rates.shape}")
print(f"Baseline rates range: [{baseline_rates.min():.6f}, {baseline_rates.max():.6f}]")
print(f"Baseline rates mean: {baseline_rates.mean():.6f}")

# Check for any issues with baseline rates
if torch.any(torch.isnan(baseline_rates)):
    print("WARNING: NaN values found in baseline rates!")
if torch.any(baseline_rates <= 0):
    print("WARNING: Zero or negative baseline rates found!")
    print(f"Number of zero/negative rates: {(baseline_rates <= 0).sum()}")

inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta

#%% Create model
print("Creating model...")
model_name = get_name_from_config(config)
print(f"Model name: {model_name}")

# Create Lightning module
pl_model = PLCoreVisionModel(
    model_class=build_model,  # Pass the factory function
    model_config=config,      # Pass the configuration
    optimizer='AdamW',
    optim_kwargs={'lr': 5e-4, 'weight_decay': 1e-5},
    accumulate_grad_batches=1,
    dataset_info=dataset_config  # Pass dataset information
)

# Initialize model components
print("Initializing model components...")
initialize_model_components(pl_model.model, init_bias=inv_softplus(baseline_rates))

print(f"Model created successfully!")
print(f"Model device: {next(pl_model.model.parameters()).device}")

#%% Get a single batch for debugging
print("Getting a single batch for debugging...")
train_iter = iter(train_loader)
batch = next(train_iter)

print(f"Batch keys: {batch.keys()}")
for key, value in batch.items():
    if isinstance(value, torch.Tensor):
        print(f"  {key}: shape={value.shape}, dtype={value.dtype}, device={value.device}")
        print(f"    range=[{value.min():.6f}, {value.max():.6f}], mean={value.mean():.6f}")
        if torch.any(torch.isnan(value)):
            print(f"    WARNING: NaN values found in {key}!")
        if torch.any(torch.isinf(value)):
            print(f"    WARNING: Inf values found in {key}!")

#%% Move model to device
print(f"Moving model to device: {device}")
pl_model = pl_model.to(device)

# Move batch to device
batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
print("Batch moved to device")

print("\n" + "="*50)
print("SETUP COMPLETE - Ready for interactive debugging!")
print("="*50)
print("\nNext steps:")
print("1. Run forward pass: output = pl_model.model(batch)")
print("2. Check output for NaN/Inf values")
print("3. Compute loss: loss = pl_model.training_step(batch, 0)")
print("4. Debug step by step...")

#%% Step 1: Forward pass through model
print("\n=== STEP 1: Forward pass ===")
pl_model.eval()  # Set to eval mode for debugging
with torch.no_grad():
    try:
        batch = pl_model(batch)
        output = batch['rhat']
        print(f"Forward pass successful!")
        print(f"Output shape: {output.shape}")
        print(f"Output dtype: {output.dtype}")
        print(f"Output device: {output.device}")
        print(f"Output range: [{output.min():.6f}, {output.max():.6f}]")
        print(f"Output mean: {output.mean():.6f}")
        print(f"Output std: {output.std():.6f}")

        # Check for problematic values
        if torch.any(torch.isnan(output)):
            print("❌ WARNING: NaN values found in output!")
            nan_count = torch.isnan(output).sum()
            print(f"   Number of NaN values: {nan_count}")
        else:
            print("✅ No NaN values in output")

        if torch.any(torch.isinf(output)):
            print("❌ WARNING: Inf values found in output!")
            inf_count = torch.isinf(output).sum()
            print(f"   Number of Inf values: {inf_count}")
        else:
            print("✅ No Inf values in output")

        # Check if output is all zeros
        if torch.all(output == 0):
            print("❌ WARNING: Output is all zeros!")
        else:
            print("✅ Output has non-zero values")

    except Exception as e:
        print(f"❌ Forward pass failed with error: {e}")
        import traceback
        traceback.print_exc()

#%% Step 2: Check individual model components
print("\n=== STEP 2: Component-wise debugging ===")

def debug_model_components(model, batch):
    """Debug each component of the model separately"""
    print("Debugging model components...")

    # Get input
    x = batch['stim']
    print(f"Input shape: {x.shape}, range: [{x.min():.6f}, {x.max():.6f}]")

    # Frontend
    if hasattr(model, 'frontend'):
        try:
            x_frontend = model.frontend(x)
            print(f"Frontend output shape: {x_frontend.shape}, range: [{x_frontend.min():.6f}, {x_frontend.max():.6f}]")
            if torch.any(torch.isnan(x_frontend)):
                print("❌ NaN in frontend output!")
            if torch.any(torch.isinf(x_frontend)):
                print("❌ Inf in frontend output!")
        except Exception as e:
            print(f"❌ Frontend failed: {e}")
            return
    else:
        x_frontend = x
        print("No frontend found, using raw input")

    # Convnet
    if hasattr(model, 'convnet'):
        try:
            x_conv = model.convnet(x_frontend)
            print(f"Convnet output shape: {x_conv.shape}, range: [{x_conv.min():.6f}, {x_conv.max():.6f}]")
            if torch.any(torch.isnan(x_conv)):
                print("❌ NaN in convnet output!")
            if torch.any(torch.isinf(x_conv)):
                print("❌ Inf in convnet output!")
        except Exception as e:
            print(f"❌ Convnet failed: {e}")
            return
    else:
        x_conv = x_frontend
        print("No convnet found")

    # Recurrent (if exists)
    if hasattr(model, 'recurrent') and model.recurrent is not None:
        try:
            x_rec = model.recurrent(x_conv)
            print(f"Recurrent output shape: {x_rec.shape}, range: [{x_rec.min():.6f}, {x_rec.max():.6f}]")
            if torch.any(torch.isnan(x_rec)):
                print("❌ NaN in recurrent output!")
            if torch.any(torch.isinf(x_rec)):
                print("❌ Inf in recurrent output!")
        except Exception as e:
            print(f"❌ Recurrent failed: {e}")
            return
    else:
        x_rec = x_conv
        print("No recurrent layer found")

    # Readout
    if hasattr(model, 'readout'):
        try:
            x_out = model.readout(x_rec)
            print(f"Readout output shape: {x_out.shape}, range: [{x_out.min():.6f}, {x_out.max():.6f}]")
            if torch.any(torch.isnan(x_out)):
                print("❌ NaN in readout output!")
            if torch.any(torch.isinf(x_out)):
                print("❌ Inf in readout output!")
        except Exception as e:
            print(f"❌ Readout failed: {e}")
            return
    else:
        print("No readout found")

    print("Component debugging complete!")

# Run component debugging
debug_model_components(pl_model.model, batch)

#%% Step 3: Training step debugging
print("\n=== STEP 3: Training step debugging ===")
pl_model.train()  # Set back to training mode

try:
    # Manual training step
    print("Running training step...")
    loss = pl_model.training_step(batch, 0)
    print(f"Training step successful!")
    print(f"Loss value: {loss}")
    print(f"Loss dtype: {loss.dtype}")

    if torch.isnan(loss):
        print("❌ LOSS IS NaN!")
    elif torch.isinf(loss):
        print("❌ LOSS IS Inf!")
    else:
        print("✅ Loss is finite")

except Exception as e:
    print(f"❌ Training step failed: {e}")
    import traceback
    traceback.print_exc()

#%% Step 4: Loss computation debugging
print("\n=== STEP 4: Loss computation debugging ===")

def debug_loss_computation(pl_model, batch):
    """Debug the loss computation step by step"""
    print("Debugging loss computation...")

    # Get model output
    output = pl_model.model(batch)
    target = batch['robs']

    print(f"Model output shape: {output.shape}")
    print(f"Target shape: {target.shape}")
    print(f"Output range: [{output.min():.6f}, {output.max():.6f}]")
    print(f"Target range: [{target.min():.6f}, {target.max():.6f}]")

    # Check if shapes match
    if output.shape != target.shape:
        print(f"❌ Shape mismatch! Output: {output.shape}, Target: {target.shape}")
        return

    # Check for problematic values before loss computation
    if torch.any(torch.isnan(output)):
        print("❌ NaN in model output before loss!")
    if torch.any(torch.isnan(target)):
        print("❌ NaN in target before loss!")
    if torch.any(output < 0):
        print(f"❌ Negative values in output: {(output < 0).sum()} values")
    if torch.any(target < 0):
        print(f"❌ Negative values in target: {(target < 0).sum()} values")

    # Compute Poisson NLL manually
    try:
        # Poisson NLL: output - target * log(output)
        log_output = torch.log(output + 1e-8)  # Add small epsilon
        poisson_nll = output - target * log_output
        loss = poisson_nll.mean()

        print(f"Manual Poisson NLL: {loss}")
        if torch.isnan(loss):
            print("❌ Manual loss computation gives NaN!")
            # Find where NaN occurs
            nan_mask = torch.isnan(poisson_nll)
            if torch.any(nan_mask):
                print(f"NaN locations in loss tensor: {nan_mask.sum()} positions")
        else:
            print("✅ Manual loss computation successful")

    except Exception as e:
        print(f"❌ Manual loss computation failed: {e}")

# Run loss debugging
debug_loss_computation(pl_model, batch)

print("\n" + "="*50)
print("DEBUGGING COMPLETE!")
print("="*50)
