#!/usr/bin/env python
"""
Example script for model loading from the registry

This script demonstrates:
1. Loading a model from a registry
2. Run model on a batch
3. Compute and visualize Jacobians to analyze instantaneous receptive fields
4. Analyze how receptive fields change dynamically based on input
"""
#%%
import torch
from torch.utils.data import DataLoader
from torch.func import jacrev, vmap
import time
import torch.cuda.amp as amp  # For mixed precision

import lightning as pl

from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import Normalize

from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1 import get_session
# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Check if CUDA is available
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Global settings
SAVE_FIGURES = True  # Set to True to save figures to disk
DISPLAY_FIGURES = True  # Set to True to display figures inline

# Create a directory for saving results (only used if SAVE_FIGURES is True)
results_dir = Path("results/jacobian_analysis")
if SAVE_FIGURES:
    results_dir.mkdir(parents=True, exist_ok=True)

# Import animation tools for dynamic visualizations
from matplotlib import animation
from IPython.display import HTML


#%%
# Import memory monitoring tools
import psutil
import gc
import time
from functools import wraps

#%%
data_config_path = "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/Allen_2022_04_13_eyevel_16_lags.yaml"
registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry")
registry = ModelRegistry(registry_dir)

sess = get_session('Allen', '2022-04-13')

# manually specify a model

# convgru
best_model =registry.load_model('v1_da_dense_convgru_gaussian_epoch_25')
# densegaussian
# best_model = registry.load_model('v1_da_dense_gaussian_epoch_09')

# # Find and load the best model from the registry
# best_model, model_entry = registry.get_best_model(
#         metric='val_loss',
#         mode='min',
#         config_match={'model_type': 'v1'},
#         dataset_match={'session': sess.name, 'types': ['gaborium', 'backimage']}
#     )

best_model = best_model.to(device)

# prepared_data = prepare_data(model_entry['dataset_info'])
import yaml
with open(data_config_path, 'r') as f:
    dataset_config = yaml.safe_load(f)

dataset_config['keys_lags']['eyepos'] = list(range(16))
train_data, val_data, dataset_config = prepare_data(dataset_config)



#%%
start = 20000
#%%
inds = np.where(val_data.inds[:,0]==1)[0]
bsize = 256

start += bsize
binds = inds[np.arange(bsize) + start]
batch = val_data[binds]

# plot the stimulus
ax = plt.subplot()
ax.imshow(batch['stim'][0][0], aspect='auto', cmap='gray')
# get axis with different x and y that overlays ontop of the gca
ax1 = ax.inset_axes([0,0,1,1])
ax1.patch.set_alpha(0)
ax1.plot(batch['eyepos'][0][:,0], batch['eyepos'][0][:,1], 'r')
ax1.set_xlim([-10, 10])
ax1.set_ylim([-10, 10])


#%% Find saccade times
import json
saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
saccade_times = torch.sort(torch.tensor([s['start_time'] for s in saccades])).values.numpy()
saccade_times = saccade_times[np.diff(saccade_times, prepend=0) > 0.1]
_ = plt.hist(np.diff(saccade_times, prepend=0), np.linspace(0, 1, 100))
plt.xlabel('Time (s)')
plt.ylabel('Count')
plt.title('Saccade ISI distribution')

#%%
dset_id = 1
inds = np.where(val_data.inds[:,0]==dset_id)[0] # indices that map to this dataset
t_bins = val_data.dsets[dset_id]['t_bins'][val_data.dset_inds[dset_id]]

sacc_ind = np.digitize(saccade_times, t_bins)
valid_saccades = (sacc_ind > 0) & (sacc_ind < len(t_bins))
# and saccade isn't within 100ms of the next saccade
valid_saccades[:-1] = valid_saccades[:-1] & (np.diff(sacc_ind) > 20)

saccade_times = saccade_times[valid_saccades]
sacc_ind = sacc_ind[valid_saccades]

saccade_indices = inds[sacc_ind - 1]

#%% check these are valid saccades
n_sample = 12
fig, axs = plt.subplots(n_sample//4, 4, figsize=(10, 2))
for ii, i in enumerate(np.random.choice(len(saccade_indices), n_sample, replace=False)):
    bind = np.arange(-10, 100) + saccade_indices[i]
    batch = val_data[bind]
    axs[ii//4, ii%4].plot(batch['eyepos'][:,0,:])
    axs[ii//4, ii%4].axvline(10, color='k', linestyle='--')

#%% plot saccade triggered average
robs = 0
window_size = 100
Nsac = len(saccade_indices)
from tqdm import tqdm
for i in tqdm(saccade_indices):
    bind = np.arange(-10, 100) + i
    robs += val_data[bind]['robs']

#%%
_ = plt.plot(robs/Nsac)
# %%
prewin = 10
i = 0 
bind = np.arange(saccade_indices[i]-prewin, saccade_indices[i+1])
batch = val_data[bind]
batch = {k: v.to(device) for k, v in batch.items() if isinstance(v, torch.Tensor)}
batch['stim'].requires_grad = True
batch = best_model(batch)

spike_count = batch['robs'].sum(0).detach().cpu()

cid = np.where(spike_count > 20)[0][0]
irfs = []
for t in tqdm(range(len(bind))):
    if batch["robs"][t, cid] > 0 and len(irfs) < 10000:
        J = torch.autograd.grad(batch['rhat'][t,cid], batch['stim'], retain_graph=True)[0][t].detach().cpu()
        irfs.append(J)


#%%
plt.subplot(2,1,1)
_ = plt.imshow(batch['robs'].detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.ylabel('Units')

plt.subplot(2,1,2)
_ = plt.imshow(batch['rhat'].detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.xlabel('Time (5ms bins)')
plt.ylabel('Units')

plt.figure()
plt.plot(batch['robs'][:, cid].detach().cpu(), 'k')
plt.plot(batch['rhat'][:, cid].detach().cpu(), 'r')



# %%
J = torch.stack(irfs, dim=0)

plt.plot(J.abs().mean((0, 2, 3)))

# make a movie of the irfs
lag = np.argmax(J.abs().mean((0, 2, 3))).item()
fig, axs = plt.subplots(1,2, figsize=(10, 5))

for ax in axs:
    ax.axis('off')

ims = []
spikes = np.where(batch['robs'][:, cid].cpu() > 0)[0]
for t in range(len(bind)):
    if t in spikes:
        i = np.where(spikes == t)[0][0]
        irf = J[i][lag]
    else:
        irf = np.zeros_like(J[0][lag])

    stim_im = axs[0].imshow(batch['stim'][t][0].detach().cpu(), cmap='gray')
    rf_im = axs[1].imshow(irf, cmap='coolwarm', vmin=J.min(), vmax=J.max())
    ims.append([stim_im, rf_im])

ani = animation.ArtistAnimation(fig, ims, interval=50, blit=True, repeat_delay=1000)
# ani.save("stim_irf.gif", writer="pillow", fps=5, dpi=100)

HTML(ani.to_jshtml())
# %% With COM tracking
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import animation
from IPython.display import HTML
from scipy.ndimage import gaussian_filter

t0, t1 = spikes.min(), spikes.max()
frame_times = np.arange(t0, t1 + 1)            # full timeline

# ---------------- robust CoM (energy-weighted) ----------------
def center_of_mass_energy(arr, smooth_sigma=2.5):
    # if np.all(irf==0):
    #     return np.nan, np.nan
    e = np.square(arr)
    if smooth_sigma:
        e = gaussian_filter(e, smooth_sigma)
    tot = e.sum()
    if tot == 0:
        return np.nan, np.nan
    y, x = np.indices(arr.shape)
    return (x * e).sum() / tot, (y * e).sum() / tot   # (col, row)

# map time → IRF index for quick lookup
idx_of_time = {t: i for i, t in enumerate(spikes)}

# pre-compute CoM for every frame (NaNs where no IRF)
com_x, com_y = [], []
for t in frame_times:
    if t in idx_of_time:
        i = idx_of_time[t]
        cx, cy = center_of_mass_energy(J[i, lag])
    else:
        cx, cy = np.nan, np.nan
    com_x.append(cx)
    com_y.append(cy)

# ---------------- figure & artists ----------------
fig, ax = plt.subplots(figsize=(5, 5))
ax.axis("off")

overlay_cmap = plt.cm.get_cmap("coolwarm").copy()
overlay_cmap.set_bad(alpha=0)                     # masked → transparent

# initialise background + overlay with first frame
stim_im = ax.imshow(batch["stim"][t0, 0].detach().cpu(),
                    cmap="gray")
irf_im  = ax.imshow(np.zeros_like(J[0, lag]),      # start empty
                    cmap=overlay_cmap,
                    vmin=J.min(), vmax=J.max(),
                    alpha=0.8)

trail, = ax.plot([], [], lw=1, color="yellow", animated=True)
dot,   = ax.plot([], [], "o", color="yellow", ms=4, animated=True)

hist_x, hist_y = [], []

def update(fi):
    """fi is the frame index into frame_times, not the spike index"""
    t = frame_times[fi]

    # stimulus (detach to drop grad graph)
    stim_im.set_data(batch["stim"][t, lag].detach().cpu())

    # IRF overlay (only when t is a spike)
    if t in idx_of_time:
        i   = idx_of_time[t]
        irf = np.ma.masked_equal(J[i, lag], 0)
    else:
        irf = np.zeros_like(J[0, lag])             # fully transparent
    irf_im.set_data(irf)

    # # grow trajectory
    if not np.isnan(com_x[fi]):
        hist_x.append(com_x[fi])
        hist_y.append(com_y[fi])
    trail.set_data(hist_x, hist_y)
    dot.set_data(com_x[fi], com_y[fi])

    return stim_im, irf_im, trail, dot

ani = animation.FuncAnimation(fig, update,
                              frames=len(frame_times),
                              interval=50, blit=True,
                              repeat_delay=1000)

HTML(ani.to_jshtml())          # notebook preview

# # save to GIF (Pillow writer needs only the ‘pillow’ package)
# ani.save("stim_irf_tracker.gif", writer="pillow", fps=15, dpi=100)
# print(f"saved stim_irf_tracker_{cid}.gif")


