#!/usr/bin/env python
"""
Example script for model loading from the registry

This script demonstrates:
1. Loading a model from a registry
3. Run model on a batch
"""
#%%
import torch
from torch.utils.data import DataLoader

import lightning as pl

from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1 import get_session
# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Check if CUDA is available
device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

#%%

def create_dataloaders(train_dset, val_dset, batch_size=256):
    """Create DataLoader objects for training."""
    train_loader = DataLoader(
        train_dset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    return train_loader, val_loader

#%%
data_config_path = "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/Allen_2022_04_13_eyevel_16_lags.yaml"
registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry")
registry = ModelRegistry(registry_dir)

sess = get_session('Allen', '2022-04-13')

# manually specify a model

# convgru
best_model =registry.load_model('v1_da_dense_convgru_gaussian_epoch_25', map_location='cpu')
# densegaussian
# best_model = registry.load_model('v1_da_dense_gaussian_epoch_09', map_location='cpu')

# # Find and load the best model from the registry
# best_model, model_entry = registry.get_best_model(
#         metric='val_loss',
#         mode='min',
#         config_match={'model_type': 'v1'},
#         dataset_match={'session': sess.name, 'types': ['gaborium', 'backimage']}
#     )

# best_model = best_model.to(device)
device = 'cpu'
# prepared_data = prepare_data(model_entry['dataset_info'])
prepared_data = prepare_data(data_config_path)
train_loader, val_loader = create_dataloaders(*prepared_data[:2])

cycler = iter(val_loader)
#%%
batch = next(cycler)
batch = {k: v.to(device) for k, v in batch.items() if isinstance(v, torch.Tensor)}

# Test the loaded model
best_model.eval()
with torch.no_grad():
    output = best_model(batch)


# %%
plt.subplot(2,1,1)
_ = plt.imshow(batch['robs'].detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.ylabel('Units')

plt.subplot(2,1,2)
_ = plt.imshow(output['rhat'].detach().cpu().T, aspect='auto', interpolation='none', cmap='gray_r')
plt.xlabel('Time (5ms bins)')
plt.ylabel('Units')
# %% plot conv weights

for layer in best_model.model.convnet.layers:
    layer.components.conv.plot_weights()
# %% Plot readout looks borked. TODO: fix it
best_model.model.readout.plot_weights()
# %%
cc = 86#89

def smooth(x, n=5):
    return torch.conv1d(x[None, None], torch.ones(1, 1, n)/n, padding=n//2)[0,0]

plt.plot(smooth(batch['robs'][:,cc].detach().cpu()))
plt.plot(batch['rhat'][:,cc].detach().cpu(), 'r')
# %%
