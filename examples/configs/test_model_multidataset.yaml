# V1 multidataset model configuration
# This model uses adapter frontends (per dataset) -> shared DenseNet -> shared recurrent -> readouts (per dataset)
model_type: v1multi

# Model dimensions
sampling_rate: 240
initial_input_channels: 6 # must match num cosines in stim embed

# Note: Frontend configurations are specified in individual dataset configs
# All frontends must be of type 'adapter' for multidataset training

# Shared convnet configuration
# High-capacity X3DNet with proper regularization
convnet:
  type: x3d
  params:
    dim: 3
    channels: [64, 128, 256]  # Large channels
    depth: [2, 4, 6]            # Deep stages  
    exp_ratio: 4                   # 4x expansion
    dropout: 0.2                   # Regular dropout
    stochastic_depth: 0.15         # Skip blocks randomly
    norm_type: grn                 # Efficient normalization
    act_type: silu                 # Efficient activation
    stride_stages: [1, 1, 1]       # Temporal strides per stage
    checkpointing: true             # Gradient checkpointing for memory
    lite_lk: false                  # Use large kernels periodically
    lk_every: 2                     # Large kernel every N stages (if lite_lk=true)

# Shared modulator configuration
modulator:
  type: film
  params:
    behavior_dim: 40
    feature_dim: 32  # Number of convnet output channels
    encoder_params:
      type: mlp
      dims: [128, 128]  # Hidden layers + output size
      activation: gelu
      bias: true
      residual: true
      dropout: 0.1
      last_layer_activation: true

# Shared recurrent configuration
recurrent:
  type: none
  params: {}

# Note: Readout configurations are specified in individual dataset configs
# Each dataset will have its own readout with n_units = len(cids)
