# V1 multidataset model configuration
# This model uses adapter frontends (per dataset) -> shared DenseNet -> shared recurrent -> readouts (per dataset)
model_type: v1multi

# Model dimensions
sampling_rate: 240
initial_input_channels: 6 # must match num cosines in stim embed

# Note: Frontend configurations are specified in individual dataset configs
# All frontends must be of type 'adapter' for multidataset training

# Shared convnet configuration
# High-capacity X3DNet with proper regularization
convnet:
  type: x3d
  params:
    dim: 3
    channels: [32, 64, 128]  # Reduced channels for debugging
    depth: [2, 2, 3]            # Reduced depth for debugging
    exp_ratio: 2                   # Reduced expansion for debugging
    dropout: 0.1                   # Reduced dropout
    stochastic_depth: 0.0          # Disabled for debugging
    norm_type: batch               # Stable normalization (changed from grn to fix NaN)
    act_type: silu                 # Efficient activation
    stride_stages: [1, 1, 1]       # Temporal strides per stage
    checkpointing: true             # Gradient checkpointing for memory
    lite_lk: false                  # Use large kernels periodically
    lk_every: 2                     # Large kernel every N stages (if lite_lk=true)

# Shared modulator configuration
modulator:
  type: film
  params:
    behavior_dim: 40
    feature_dim: 128  # Number of convnet output channels (matches convnet final stage)
    encoder_params:
      type: mlp
      dims: [128, 128]  # Hidden layers + output size
      activation: gelu
      bias: true
      residual: true
      dropout: 0.1
      last_layer_activation: true

# Shared recurrent configuration
recurrent:
  type: none
  params: {}

# Note: Readout configurations are specified in individual dataset configs
# Each dataset will have its own readout with n_units = len(cids)
