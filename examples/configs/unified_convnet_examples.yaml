# Examples showing the new unified convnet configuration approach
# All three convnet types (DenseNet, ResNet, VanillaCNN) now use the same clean syntax

# Example 1: DenseNet with unified format
densenet_unified:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: densenet
    params:
      channels: [8, 16, 12]  # Each layer outputs these many channels
      dim: 3
      checkpointing: true
      block_config:
        conv_params:
          type: depthwise
          kernel_size: [3, 5, 5]
          padding: [1, 2, 2]
        norm_type: rms
        act_type: mish
        pool_params: {}
  recurrent:
    type: convgru
    params:
      hidden_dim: 64

# Example 2: ResNet with unified format
resnet_unified:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: resnet
    params:
      channels: [16, 32, 64]  # Each layer outputs these many channels
      dim: 3
      checkpointing: true
      block_config:
        conv_params:
          type: standard
          kernel_size: [3, 3, 3]
          padding: [1, 1, 1]
        norm_type: batch
        act_type: relu
        pool_params: {}
  recurrent:
    type: convgru
    params:
      hidden_dim: 64

# Example 3: VanillaCNN with unified format
vanilla_cnn_unified:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: vanilla
    params:
      channels: [24, 48, 32]  # Each layer outputs these many channels
      dim: 3
      checkpointing: false
      block_config:
        conv_params:
          type: standard
          kernel_size: [3, 3, 3]
          padding: [1, 1, 1]
          stride: [1, 2, 2]  # Spatial downsampling
        norm_type: layer
        act_type: gelu
        pool_params:
          type: max
          kernel_size: [1, 2, 2]
          stride: [1, 2, 2]
  recurrent:
    type: convgru
    params:
      hidden_dim: 64

# Example 4: Performance-optimized ResNet
resnet_performance:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: resnet
    params:
      channels: [64, 128, 256]  # Larger channels for performance
      dim: 3
      checkpointing: true
      block_config:
        conv_params:
          type: depthwise
          kernel_size: [3, 5, 5]
          padding: [1, 2, 2]
        norm_type: rms
        act_type: silu
        pool_params:
          type: max
          kernel_size: [1, 2, 2]
          stride: [1, 2, 2]
  recurrent:
    type: convgru
    params:
      hidden_dim: 256

# Example 5: Lightweight DenseNet
densenet_lightweight:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: densenet
    params:
      channels: [4, 8, 6]  # Small channels for lightweight model
      dim: 3
      checkpointing: false
      block_config:
        conv_params:
          type: depthwise
          kernel_size: [3, 3, 3]
          padding: [1, 1, 1]
        norm_type: batch
        act_type: relu
        pool_params: {}
  recurrent:
    type: convgru
    params:
      hidden_dim: 32

# Example 6: X3DNet with unified format
x3dnet_unified:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: x3d
    params:
      channels: [32, 64, 128]  # Unified format: output channels per stage
      depth: [2, 3, 2]         # X3D-specific: blocks per stage
      dim: 3
      t_kernel: 5              # Temporal kernel size
      s_kernel: 3              # Spatial kernel size
      exp_ratio: 4             # MLP expansion ratio
      norm_type: grn           # Group RMS Norm (recommended for X3D)
      act_type: silu           # SiLU activation (recommended for X3D)
      stride_stages: [1, 2, 2] # Temporal strides per stage
      checkpointing: true
  recurrent:
    type: convgru
    params:
      hidden_dim: 128

# Example 7: Simple X3DNet with minimal config
x3dnet_simple:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: x3d
    params:
      channels: [24, 48, 96]   # Only channels specified, depth defaults to [1, 1, 1]
      dim: 3
      t_kernel: 3              # Smaller temporal kernel
      s_kernel: 3
      exp_ratio: 2             # Smaller expansion ratio for efficiency
      norm_type: grn
      act_type: silu
      checkpointing: false
  recurrent:
    type: convgru
    params:
      hidden_dim: 96

# Example 8: X3DNet with lite large kernel feature
x3dnet_lite_lk:
  model_type: v1
  frontend:
    type: da
    params:
      alpha: 0.01
      beta: 0.00008
  convnet:
    type: x3d
    params:
      channels: [32, 64, 128, 256]
      depth: [2, 3, 4, 2]
      dim: 3
      t_kernel: 5
      s_kernel: 3
      exp_ratio: 4
      norm_type: grn
      act_type: silu
      stride_stages: [1, 2, 2, 2]
      lite_lk: true             # Enable lite large kernel
      lk_every: 2               # Large kernel every 2 stages
      checkpointing: true
  recurrent:
    type: convgru
    params:
      hidden_dim: 256
