# V1 model configuration demonstrating the new unified convnet configuration approach
# This shows how DenseNet, ResNet, and VanillaCNN can all use the same clean syntax
model_type: v1

# Model dimensions
height: 32
width: 32
sampling_rate: 240
initial_input_channels: 6 # must match num cosines in stim embed

# Frontend configuration
frontend:
  type: da
  params:
    alpha: 0.01
    beta: 0.00008
    gamma: 0.5
    tau_y_ms: 5.0
    tau_z_ms: 60.0
    n_y: 5.0
    n_z: 2.0
    filter_length: 16
    learnable_params: true

# Convnet configuration using new unified format
convnet:
  type: densenet  # Can be: densenet, resnet, vanilla
  params:
    channels: [16, 32, 24]  # Explicit channel counts for each layer
    dim: 3
    checkpointing: true
    block_config:
      conv_params:
        type: depthwise
        kernel_size: [3, 5, 5]
        padding: [1, 2, 2]
      norm_type: rms
      act_type: mish
      pool_params: {}

# Recurrent configuration
recurrent:
  type: convgru
  params:
    hidden_dim: 64
    kernel_size: [3, 3, 3]
    num_layers: 1
    bias: true
    dropout: 0.0

# Modulator configuration
modulator:
  type: film
  params:
    behavior_dim: 3
    feature_dim: 64
    encoder_params:
      type: mlp
      dims: [192, 256, 192]
      activation: gelu
      bias: true
      residual: true
      dropout: 0.0
      last_layer_activation: false

# Readout configuration
readout:
  type: gaussian
  params:
    n_units: 500
    bias: true
    initial_std: 5.0

# Training configuration
training:
  batch_size: 32
  learning_rate: 0.001
  weight_decay: 0.0001
  max_epochs: 100
  patience: 10
  validation_fraction: 0.2
