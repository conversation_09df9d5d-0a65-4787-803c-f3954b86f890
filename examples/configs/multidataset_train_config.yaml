# Multidataset training configuration
# This config specifies the model and multiple datasets for training

training_type: v1multi

# Path to the main model configuration (shared components)
model_config: "test_model_multidataset.yaml"

# List of dataset configurations
datasets:
  - path: "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/Allen_2022-02-16.yaml"
  - path: "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/Allen_2022-04-13.yaml"
  - path: "/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/Allen_2022-04-13.yaml"


# Training-specific parameters
training:
  accumulate_grad_batches: 1
  max_epochs: 100
  batch_size: 32
  early_stopping_patience: 10
  
# Optimizer configuration
optimizer:
  type: AdamW
  lr: 5e-4
  weight_decay: 1e-5

# Scheduler configuration (optional)
scheduler:
  type: cosine
  warmup_epochs: 5
