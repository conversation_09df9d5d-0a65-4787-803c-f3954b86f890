# DataYatesV1/utils/data/prepare.py  (new or replace old prepare_data)
#%%

from pathlib import Path
import yaml
from DataYatesV1.utils.data import prepare_data
#%%
dataset_config_path = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/Allen_2022_04_13_eyevel_16_lags.yaml")
# Load dataset configuration
with open(dataset_config_path, 'r') as f:
    dataset_config = yaml.safe_load(f)

train_dset, val_dset, dataset_config = prepare_data(dataset_config)

#%%