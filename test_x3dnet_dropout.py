#!/usr/bin/env python3
"""
Test script for X3DNet dropout implementation.
Tests both regular dropout and stochastic depth.
"""

import torch
import torch.nn as nn
from DataYatesV1.models.modules.convnet import X3DNet
from DataYatesV1.models.config_builders import build_x3dnet_config

def test_x3dnet_without_dropout():
    """Test X3DNet without any dropout."""
    print("Testing X3DNet without dropout...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[32, 64],
        depth=[2, 2],
        dim=3,
        t_kernel=3,
        s_kernel=3,
        exp_ratio=2,
        dropout=0.0,
        stochastic_depth=0.0
    )
    
    model = X3DNet(config)
    model.eval()  # Set to eval mode
    
    x = torch.randn(2, 6, 8, 32, 32)
    y1 = model(x)
    y2 = model(x)
    
    # Should be identical in eval mode
    assert torch.allclose(y1, y2), "Outputs should be identical in eval mode"
    print(f"  ✓ No dropout: {x.shape} -> {y1.shape}")

def test_x3dnet_with_regular_dropout():
    """Test X3DNet with regular dropout."""
    print("\nTesting X3DNet with regular dropout...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[32, 64],
        depth=[2, 2],
        dim=3,
        t_kernel=3,
        s_kernel=3,
        exp_ratio=2,
        dropout=0.2,  # 20% dropout
        stochastic_depth=0.0
    )
    
    model = X3DNet(config)
    
    # Test in training mode
    model.train()
    x = torch.randn(2, 6, 8, 32, 32)
    y1 = model(x)
    y2 = model(x)
    
    # Should be different in training mode due to dropout
    assert not torch.allclose(y1, y2), "Outputs should be different in training mode with dropout"
    print(f"  ✓ Training mode with dropout: outputs differ as expected")
    
    # Test in eval mode
    model.eval()
    y3 = model(x)
    y4 = model(x)
    
    # Should be identical in eval mode
    assert torch.allclose(y3, y4), "Outputs should be identical in eval mode"
    print(f"  ✓ Eval mode: {x.shape} -> {y3.shape}")

def test_x3dnet_with_stochastic_depth():
    """Test X3DNet with stochastic depth."""
    print("\nTesting X3DNet with stochastic depth...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[32, 64],
        depth=[3, 3],  # More blocks to see stochastic depth effect
        dim=3,
        t_kernel=3,
        s_kernel=3,
        exp_ratio=2,
        dropout=0.0,
        stochastic_depth=0.3  # 30% chance to skip blocks
    )
    
    model = X3DNet(config)
    
    # Test in training mode
    model.train()
    x = torch.randn(2, 6, 8, 32, 32)
    
    # Run multiple times to see stochastic behavior
    outputs = []
    for i in range(5):
        y = model(x)
        outputs.append(y)
    
    # Check that at least some outputs are different (stochastic depth working)
    all_same = all(torch.allclose(outputs[0], out) for out in outputs[1:])
    assert not all_same, "Outputs should vary due to stochastic depth"
    print(f"  ✓ Training mode with stochastic depth: outputs vary as expected")
    
    # Test in eval mode
    model.eval()
    y1 = model(x)
    y2 = model(x)
    
    # Should be identical in eval mode (no stochastic depth)
    assert torch.allclose(y1, y2), "Outputs should be identical in eval mode"
    print(f"  ✓ Eval mode: {x.shape} -> {y1.shape}")

def test_x3dnet_with_both_dropouts():
    """Test X3DNet with both regular dropout and stochastic depth."""
    print("\nTesting X3DNet with both dropout types...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[32, 64, 96],
        depth=[2, 3, 2],
        dim=3,
        t_kernel=3,
        s_kernel=3,
        exp_ratio=4,
        dropout=0.15,           # Regular dropout
        stochastic_depth=0.2    # Stochastic depth
    )
    
    model = X3DNet(config)
    
    # Test in training mode
    model.train()
    x = torch.randn(2, 6, 8, 32, 32)
    
    # Run multiple times
    outputs = []
    for i in range(3):
        y = model(x)
        outputs.append(y)
    
    # Should be different due to both dropout types
    all_same = all(torch.allclose(outputs[0], out, atol=1e-6) for out in outputs[1:])
    assert not all_same, "Outputs should vary due to combined dropout effects"
    print(f"  ✓ Training mode with both dropouts: outputs vary as expected")
    
    # Test in eval mode
    model.eval()
    y1 = model(x)
    y2 = model(x)
    
    # Should be identical in eval mode
    assert torch.allclose(y1, y2), "Outputs should be identical in eval mode"
    print(f"  ✓ Eval mode: {x.shape} -> {y1.shape}")

def test_dropout_parameters_in_config():
    """Test that dropout parameters are correctly stored in config."""
    print("\nTesting dropout parameters in config...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[32, 64],
        dropout=0.25,
        stochastic_depth=0.15
    )
    
    assert config['dropout'] == 0.25, f"Expected dropout=0.25, got {config['dropout']}"
    assert config['stochastic_depth'] == 0.15, f"Expected stochastic_depth=0.15, got {config['stochastic_depth']}"
    print("  ✓ Dropout parameters correctly stored in config")

def test_large_x3dnet_with_dropout():
    """Test a larger X3DNet that would benefit from dropout."""
    print("\nTesting large X3DNet with dropout...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[64, 128, 256, 512],  # Large model
        depth=[3, 4, 6, 3],            # Deep model
        dim=3,
        t_kernel=5,
        s_kernel=3,
        exp_ratio=4,                   # 4x expansion
        dropout=0.2,                   # Recommended for large models
        stochastic_depth=0.15,         # Recommended for deep models
        norm_type='grn',
        act_type='silu'
    )
    
    model = X3DNet(config)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"  Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Test forward pass
    model.train()
    x = torch.randn(1, 6, 16, 51, 51)  # Realistic input size
    y = model(x)
    
    print(f"  ✓ Large model: {x.shape} -> {y.shape}")
    print(f"  ✓ Memory efficient: {total_params/1e6:.1f}M parameters")

if __name__ == "__main__":
    print("Testing X3DNet dropout implementation...\n")
    
    test_x3dnet_without_dropout()
    test_x3dnet_with_regular_dropout()
    test_x3dnet_with_stochastic_depth()
    test_x3dnet_with_both_dropouts()
    test_dropout_parameters_in_config()
    test_large_x3dnet_with_dropout()
    
    print("\n🎉 All X3DNet dropout tests passed!")
    print("\n💡 Recommendations for large X3DNet models:")
    print("   - Use dropout=0.1-0.3 for regularization")
    print("   - Use stochastic_depth=0.1-0.2 for deep models")
    print("   - Higher dropout for larger models/datasets")
    print("   - Start with lower values and increase if overfitting")
