#!/usr/bin/env python3
"""
Test script for the unified config builders.
Tests both legacy and new unified config builders for all convnet types.
"""

import torch
from DataYatesV1.models.config_builders import (
    build_densenet_config, build_resnet_config,
    build_densenet_unified_config, build_resnet_unified_config, 
    build_vanilla_cnn_unified_config, build_unified_convnet_config
)
from DataYatesV1.models.modules.convnet import DenseNet, ResNet, VanillaCNN

def test_legacy_config_builders():
    """Test the legacy config builders."""
    print("Testing legacy config builders...")
    
    # Test legacy DenseNet config
    densenet_config = build_densenet_config(
        initial_channels=6,
        growth_rate=8,
        num_blocks=3,
        dim=3,
        use_checkpointing=True
    )
    
    model = DenseNet(densenet_config)
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Legacy DenseNet: {x.shape} -> {y.shape}")
    
    # Test legacy ResNet config
    resnet_config = build_resnet_config(
        in_channels=6,
        out_channels=32,
        dim=3,
        use_checkpointing=True
    )
    
    model = ResNet(resnet_config)
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Legacy ResNet: {x.shape} -> {y.shape}")
    print("  ✓ Legacy config builders work!")

def test_unified_config_builders():
    """Test the new unified config builders."""
    print("\nTesting unified config builders...")
    
    # Test unified DenseNet config
    densenet_config = build_densenet_unified_config(
        initial_channels=6,
        channels=[8, 16, 12],
        dim=3,
        conv_type='depthwise',
        kernel_size=(3, 5, 5),
        padding=(1, 2, 2),
        norm_type='rms',
        act_type='mish'
    )
    
    model = DenseNet(densenet_config)
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Unified DenseNet: {x.shape} -> {y.shape}")
    
    # Test unified ResNet config
    resnet_config = build_resnet_unified_config(
        initial_channels=6,
        channels=[16, 32, 24],
        dim=3,
        conv_type='standard',
        kernel_size=(3, 3, 3),
        padding=(1, 1, 1),
        norm_type='batch',
        act_type='relu'
    )
    
    model = ResNet(resnet_config)
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Unified ResNet: {x.shape} -> {y.shape}")
    
    # Test unified VanillaCNN config
    vanilla_config = build_vanilla_cnn_unified_config(
        initial_channels=6,
        channels=[24, 48, 32],
        dim=3,
        conv_type='standard',
        kernel_size=(3, 3, 3),
        padding=(1, 1, 1),
        norm_type='layer',
        act_type='gelu'
    )
    
    model = VanillaCNN(vanilla_config)
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Unified VanillaCNN: {x.shape} -> {y.shape}")
    print("  ✓ Unified config builders work!")

def test_generic_unified_builder():
    """Test the generic unified config builder."""
    print("\nTesting generic unified config builder...")
    
    # Test building different convnet types with the same function
    convnet_types = ['densenet', 'resnet', 'vanilla']
    
    for convnet_type in convnet_types:
        config = build_unified_convnet_config(
            convnet_type=convnet_type,
            initial_channels=6,
            channels=[16, 32, 24],
            dim=3,
            conv_type='standard',
            kernel_size=(3, 3, 3),
            padding=(1, 1, 1),
            norm_type='batch',
            act_type='relu'
        )
        
        # Create the appropriate model
        if convnet_type == 'densenet':
            model = DenseNet(config)
        elif convnet_type == 'resnet':
            model = ResNet(config)
        elif convnet_type == 'vanilla':
            model = VanillaCNN(config)
        
        x = torch.randn(2, 6, 8, 32, 32)
        y = model(x)
        print(f"  Generic {convnet_type}: {x.shape} -> {y.shape}")
    
    print("  ✓ Generic unified config builder works!")

def test_config_equivalence():
    """Test that unified configs produce equivalent results to manual configs."""
    print("\nTesting config equivalence...")
    
    # Create a manual unified config
    manual_config = {
        'model_type': 'densenet',
        'dim': 3,
        'initial_channels': 6,
        'channels': [8, 16, 12],
        'checkpointing': False,
        'block_config': {
            'conv_params': {
                'type': 'standard',
                'kernel_size': (3, 3, 3),
                'padding': (1, 1, 1)
            },
            'norm_type': 'batch',
            'act_type': 'relu',
            'pool_params': {}
        }
    }
    
    # Create equivalent config using builder
    builder_config = build_densenet_unified_config(
        initial_channels=6,
        channels=[8, 16, 12],
        dim=3,
        conv_type='standard',
        kernel_size=(3, 3, 3),
        padding=(1, 1, 1),
        norm_type='batch',
        act_type='relu',
        use_checkpointing=False
    )
    
    # Test both configs produce same architecture
    model1 = DenseNet(manual_config)
    model2 = DenseNet(builder_config)
    
    x = torch.randn(2, 6, 8, 32, 32)
    y1 = model1(x)
    y2 = model2(x)
    
    print(f"  Manual config: {x.shape} -> {y1.shape}")
    print(f"  Builder config: {x.shape} -> {y2.shape}")
    print(f"  Output shapes match: {y1.shape == y2.shape}")
    print("  ✓ Config equivalence verified!")

def test_advanced_features():
    """Test advanced features like custom pooling and different conv types."""
    print("\nTesting advanced features...")
    
    # Test with pooling
    config_with_pooling = build_resnet_unified_config(
        initial_channels=6,
        channels=[32, 64],
        dim=3,
        conv_type='depthwise',
        kernel_size=(3, 5, 5),
        padding=(1, 2, 2),
        norm_type='rms',
        act_type='silu',
        pool_params={
            'type': 'max',
            'kernel_size': (1, 2, 2),
            'stride': (1, 2, 2)
        }
    )
    
    model = ResNet(config_with_pooling)
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  ResNet with pooling: {x.shape} -> {y.shape}")
    
    # Test with different stride
    config_with_stride = build_vanilla_cnn_unified_config(
        initial_channels=6,
        channels=[24, 48],
        dim=3,
        conv_type='standard',
        kernel_size=(3, 3, 3),
        padding=(1, 1, 1),
        stride=(1, 2, 2),  # Spatial downsampling
        norm_type='layer',
        act_type='gelu'
    )
    
    model = VanillaCNN(config_with_stride)
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  VanillaCNN with stride: {x.shape} -> {y.shape}")
    print("  ✓ Advanced features work!")

if __name__ == "__main__":
    print("Testing unified convnet config builders...\n")
    
    test_legacy_config_builders()
    test_unified_config_builders()
    test_generic_unified_builder()
    test_config_equivalence()
    test_advanced_features()
    
    print("\n🎉 All config builder tests passed! The unified approach works perfectly.")
