
from pathlib import Path
import shutil

import numpy as np
import pandas as pd
from ..utils.io import KilosortResults

def save_binary_recording(seg, cache_dir, recalc=False):
    '''
        Save a given spikeinterface extractor to a binary format. If the cache_dir exists,
        then will attempt to load from there. If the extractor cannot be loaded, then the extractor is saved.
        Saving a preprocessed recording reduces computation time when running the sorter, especially if
        running multiple sorters.

        Parameters:
        ------------
        seg: spikeinterface extractor
            The extractor to save
        cache_dir: str or Path
        recalc: bool
            If True, will delete the cache_dir and rerun the sorter

        Returns:
        -----------
        seg_saved: spikeinterface extractor
            The loaded output
    '''
    from spikeinterface.core import load_extractor

    if recalc and cache_dir.exists():
        shutil.rmtree(cache_dir)

    if isinstance(cache_dir, str):
        cache_dir = Path(cache_dir)

    if cache_dir.exists():
        try:
            seg_load = load_extractor(cache_dir)
        except Exception as e:
            print(f'Failed to load extractor: {e}')
            shutil.rmtree(cache_dir)
    
    if not cache_dir.exists():
        seg.save(folder=cache_dir)

    return load_extractor(cache_dir)

def sort_ks4(seg, cache_dir, sorter_params = {}, recalc=False):
    '''
        Sort a given spikeinterface extractor using kilosort4. If the cache_dir exists,
        then will attempt to loaded from there. If the sorting cannot be loaded, then kilsort4 is run.

        Parameters:
        ------------
        seg: spikeinterface extractor
            The extractor to sort
        cache_dir: str or Path
        sorter_params: dict
            Parameters to pass to the sorter
        recalc: bool
            If True, will delete the cache_dir and rerun the sorter

        Returns:
        -----------
        ks4_sorting: spikeinterface sorting extractor
            The sorted output
    '''
    from spikeinterface.sorters import run_sorter, get_default_sorter_params

    if isinstance(cache_dir, str):
        cache_dir = Path(cache_dir)

    if recalc and cache_dir.exists():
        shutil.rmtree(cache_dir)

    ks4_sorting = None
    if cache_dir.exists():
        try:
            ks4_sorting = KilosortResults(cache_dir / 'sorter_output')
        except Exception as e:
            print(f'Failed to load kilosort4 sorting: {e}')
            shutil.rmtree(cache_dir)

    if not cache_dir.exists():
        # Run kilosort4 locally
        sorter_params = get_default_sorter_params('kilosort4')
        sorter_params['do_correction'] = False
        sorter_params['save_extra_vars'] = True
        sorter_params = dict(sorter_params, **sorter_params)
        try:
            _ = run_sorter("kilosort4", seg, folder=str(cache_dir), verbose=True, remove_existing_folder=True, **sorter_params)
        except Exception as e:
            print(f'Error running kilosort4: {e}')
        ks4_sorting = KilosortResults(cache_dir / 'sorter_output')

    return ks4_sorting




