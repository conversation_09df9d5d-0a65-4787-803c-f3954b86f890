from pathlib import Path
import numpy as np
import json
from tqdm import tqdm
from loguru import logger
import matplotlib.pyplot as plt
from matplotlib import gridspec
from scipy.ndimage import convolve
from scipy.stats import pearsonr
class OpenEphysContinuousFile:
    def __init__(self, file_path):
        if isinstance(file_path, str):
            file_path = Path(file_path)
        self.file_path = file_path
        assert file_path.exists(), f'{file_path} does not exist'
        assert file_path.suffix == '.continuous', f'{file_path} is not a .continuous file'
        self.header_bytes = 1024
        self.record_bytes = 2070
        self.sdt = np.dtype('int16').newbyteorder('>')
        self.mmap = np.memmap(self.file_path, dtype='uint8', mode='r')

        assert (self.file_path.stat().st_size - self.header_bytes) % self.record_bytes == 0, f'{self.file_path} is not a valid .continuous file'
        self.n_records = int((self.file_path.stat().st_size - self.header_bytes) // self.record_bytes)

    def get_record(self, record_idx):
        record_start = self.header_bytes + record_idx * self.record_bytes
        record_end = record_start + self.record_bytes
        data = self.mmap[record_start:record_end]
        timestamp = np.frombuffer(data[:8], dtype='uint64')[0]
        sample_count = np.frombuffer(data[8:10], dtype='uint16')[0]
        recording_index = np.frombuffer(data[10:12], dtype='uint16')[0]
        samples = np.frombuffer(data[12:2060], dtype=self.sdt)
        record_marker = data[2060:]
        if not np.all(record_marker == np.array([0, 1, 2, 3, 4, 5, 6, 7, 8, 255], dtype='uint8')):
            print(f'Warning: record {record_idx} does not end with the expected marker')
        return {
            'timestamp': timestamp,
            'sample_count': sample_count,
            'recording_index': recording_index,
            'samples': samples,
            'record_marker': record_marker
        }

    def get_record_signal(self, record_idx):
        i_start =  self.header_bytes + record_idx * self.record_bytes
        n_samples = int(np.frombuffer(self.mmap[i_start+8:i_start+10], dtype='uint16')[0])

        i_end = i_start + 12 + n_samples * self.sdt.itemsize

        samples = np.frombuffer(self.mmap[i_start+12:i_end], dtype=self.sdt)
        return samples

    def get_timestamps(self):
        timestamps = np.zeros(self.n_records, dtype='uint64')
        for i in range(self.n_records):
            i_start = self.header_bytes + i * self.record_bytes
            timestamps[i] = np.frombuffer(self.mmap[i_start:i_start+8], dtype='uint64')[0]
        return timestamps

    def get_sample_counts(self):
        sample_counts = np.zeros(self.n_records, dtype='uint16')
        for i in range(self.n_records):
            i_start = self.header_bytes + i * self.record_bytes
            sample_counts[i] = np.frombuffer(self.mmap[i_start+8:i_start+10], dtype='uint16')[0]
        return sample_counts

    def get_recording_indices(self):
        recording_indices = np.zeros(self.n_records, dtype='uint16')
        for i in range(self.n_records):
            i_start = self.header_bytes + i * self.record_bytes
            recording_indices[i] = np.frombuffer(self.mmap[i_start+10:i_start+12], dtype='uint16')[0]
        return recording_indices
    
    def get_record_marker(self):
        record_markers = np.zeros((self.n_records, 10), dtype='uint8')
        for i in range(self.n_records):
            i_start = self.header_bytes + i * self.record_bytes
            record_markers[i] = self.mmap[i_start+2060:i_start+2070]
        return record_markers
    def get_header(self):
        header_bytes = self.mmap[:self.header_bytes]
        header_string = header_bytes.tobytes().decode('utf-8')
        return header_string
    def get_sample_rate(self):
        header_string = self.get_header()
        for line in header_string.split('\n'):
            if 'sampleRate' in line:
                return float(line.split('=')[-1][:-1])
        return None
    def get_uV_per_bit(self):
        header_string = self.get_header()
        for line in header_string.split('\n'):
            if 'bitVolts' in line:
                return float(line.split('=')[-1][:-1])
        return None

def get_electrode_by_name(name):
    # Channel maps generated in matlab by running:
    # e = hardware.electrodeFactory(key){1}
    # e = e{1}
    # hs = hardware.combineHeadstages(e.headstages)
    # ch_map = hs.channelMap(e.channelMap)

    electrode_factory_channel_maps = {
            'Nandy': np.array([56,31,57,29,55,27,58,25,54,24,59,26,53,23,60,22,52,28,61,21,51,20,62,30,50,19,63,18,49,32,64,17,2,41,4,40,6,42,8,39,9,43,7,38,10,44,11,37,5,45,12,36,13,46,3,35,14,47,15,34,1,48,16,33]),
            'Nandyflip': np.array([40,31,41,29,39,27,42,25,38,24,43,26,37,23,44,22,36,28,45,21,35,20,46,30,34,19,47,18,33,32,48,17,2,57,4,56,6,58,8,55,9,59,7,54,10,60,11,53,5,61,12,52,13,62,3,51,14,63,15,50,1,64,16,49]),
            'NandyFrontFlip': np.array([8,63,9,61,7,59,10,57,6,56,11,58,5,55,12,54,4,60,13,53,3,52,14,62,2,51,15,50,1,64,16,49,34,25,36,24,38,26,40,23,41,27,39,22,42,28,43,21,37,29,44,20,45,30,35,19,46,31,47,18,33,32,48,17]),
            'Nandy64': np.array([18,4,17,8,20,12,19,16,22,15,21,14,24,13,23,11,26,10,25,9,28,7,27,6,30,5,29,3,32,2,31,1,62,48,58,47,54,46,50,45,49,44,52,43,51,42,53,41,56,40,55,39,57,38,60,37,59,36,61,35,64,34,63,33]),
            'Nandy64_flip': np.array([50,36,49,40,52,44,51,48,54,47,53,46,56,45,55,43,58,42,57,41,60,39,59,38,62,37,61,35,64,34,63,33,30,16,26,15,22,14,18,13,17,12,20,11,19,10,21,9,24,8,23,7,25,6,28,5,27,4,29,3,32,2,31,1])
    }

    electrode_pitch = 35 # um
    shank_pitch = 200 # um
    electrode_geometry = np.zeros((64, 2))
    electrode_geometry[:32, 0] = 0
    electrode_geometry[32:, 0] = shank_pitch
    electrode_geometry[:32, 1] = np.arange(32) * electrode_pitch
    electrode_geometry[32:, 1] = np.arange(32) * electrode_pitch

    shank_indices = [np.arange(32), np.arange(32, 64)]

    return electrode_factory_channel_maps[name], electrode_geometry, shank_indices


def determine_electrode(dset_dir, n_segs=1000, outlier_thresh = 7, n_neighbors = 7,
                        cache_dir=None, recalc=False, show=True, seed=1002):
    '''
    Determine the electrode by calculating the correlation between all channels.
    The electrode with the highest correlation between neighboring channels is selected.

    Parameters
    ----------
    dset_dir: Path
        The directory containing the OpenEphys continuous files.
    n_segs: int
        The number of segments to use for calculating the correlation.
    outlier_thresh: float
        The threshold for detecting dead channels. Channels are considered
        dead if the median correlation is below the median - outlier_thresh * MAD.
    n_neighbors: int
        The number of neighboring channels to use for refining the dead channel detection.
    cache_dir: Path
        The directory to use for caching the results.
    recalc: bool
        If True, will recalculate the correlation even if the cache exists.
    show: bool
        If True, will show the figure with the correlation results.
    seed: int
        The random seed to use for selecting the segments.

    Returns
    -------
    electrode: str
        The name of the electrode with the highest correlation.
    '''

    np.random.seed(seed)

    if isinstance(dset_dir, str):
        dset_dir = Path(dset_dir)
    assert dset_dir.exists(), f'{dset_dir} does not exist'

    if cache_dir is not None:
        cache_dir = Path(cache_dir)
        cache_dir.mkdir(parents=True, exist_ok=True)
        if (cache_dir / 'electrode.json').exists() and not recalc:
            # Load the cached results
            try:
                electrode = json.load(open(cache_dir / 'electrode.json', 'r'))
                return electrode
            except Exception as e:
                logger.warning(f'Failed to load cached electrode: {e}')

    f_chs= list(dset_dir.glob('*CH*.continuous'))
    channels = {
        f.stem.split('_')[-1]: OpenEphysContinuousFile(f) for f in f_chs
    }
    n_channels = len(f_chs)

    # check record consistency
    n_records = np.array([f.n_records for f in channels.values()])
    assert np.all(n_records == n_records[0]), 'All .continuous files should have the same number of records'
    n_records = n_records[0]

    n_recordings = np.array([len(np.unique(f.get_recording_indices())) for f in channels.values()])
    if not np.all(n_recordings == 1):
        logger.warning('At least one file has more than one recording index, concatenating recordings')

    # check sampling rate consistency
    sampling_rates = np.array([f.get_sample_rate() for f in channels.values()])
    assert np.all(sampling_rates == sampling_rates[0]), 'All .continuous files should have the same sampling rate'

    # check uV_per_bit consistency
    uV_per_bit = np.array([f.get_uV_per_bit() for f in channels.values()])
    assert np.all(uV_per_bit == uV_per_bit[0]), 'All .continuous files should have the same gain'
    uV_per_bit = uV_per_bit[0]

    # Pick random records for all electrodes
    if n_records < n_segs:
        n_segs = n_records
    record_ids = np.random.choice(n_records, n_segs, replace=False)
    channel_names = [f'CH{i+1}' for i in range(n_channels)]
    records = []
    for iR in record_ids:
        r = np.stack(
            [channels[name].get_record_signal(iR) for name in channel_names], axis=1
        )
        records.append(r)

    records = np.concatenate(records, axis=0)

    # calculate all pairwise correlation coefficients
    corrs = np.ones((n_channels, n_channels))
    n_iter = n_channels * (n_channels - 1) // 2
    with tqdm(total=n_iter, desc='Calculating correlations') as pbar:
        for i in range(n_channels):
            for j in range(i+1, n_channels):
                corrs[i, j] = pearsonr(records[:, i], records[:, j])[0]
                corrs[j, i] = corrs[i, j]
                pbar.update(1)

    def get_outliers(vals, thresh):
        med = np.median(vals)
        mad = np.median(np.abs(vals - med))
        return np.where(vals < med - thresh * mad)[0], med - thresh * mad

    # Detect dead channels
    med_corrs = np.median(corrs, axis=1)
    dead_channels, first_thresh = get_outliers(med_corrs, outlier_thresh)

    # calculate the correlation roughness
    electrodes = ['Nandy','Nandyflip','NandyFrontFlip','Nandy64','Nandy64_flip']
    kernel = np.array([[0, 1, 0],[1,-4,1],[0, 1, 0]])
    n_maps = len(electrodes)

    corr_roughness = []
    for i, electrode in enumerate(electrodes):
        channel_map,_,_ = get_electrode_by_name(electrode)
        channel_map = np.array(channel_map) - 1
        dead_electrodes = np.isin(channel_map, dead_channels)
        channel_map = channel_map[~dead_electrodes]
        sorted_corrs = corrs[channel_map, :][:, channel_map]
        filt_corrs = convolve(sorted_corrs, kernel, mode='reflect')
        corr_roughness.append(np.mean(np.abs(filt_corrs)))

    map_id = np.argmin(corr_roughness)
    electrode = electrodes[map_id]

    # get the electrode geometry
    # and shank positions
    channel_map, electrode_geometry, shank_position = get_electrode_by_name(electrode)

    channel_map = np.array(channel_map) - 1
    # Refine the dead channels using neighboring correlation
    sorted_corrs = corrs[channel_map, :][:, channel_map]
    near_corrs = np.zeros(len(channel_map))
    for i in range(len(channel_map)):
        electrode_id = np.where(channel_map == i)[0][0]
        electrode_pos = electrode_geometry[electrode_id]
        electrode_distances = np.linalg.norm(electrode_geometry - electrode_pos, axis=1)
        near_electrodes = np.argsort(electrode_distances)[1:n_neighbors+1]
        near_channels = channel_map[near_electrodes]
        mask = np.zeros(len(channel_map), dtype=bool)
        mask[near_channels] = True
        near_corrs[i] = np.median(corrs[i, mask])

    final_dead_channels, second_thresh = get_outliers(near_corrs, outlier_thresh)

    dead_electrodes = np.where(np.isin(channel_map, final_dead_channels))[0]
    electrode = {
        'name': electrode,
        'channel_map': (channel_map + 1).tolist(),
        'geometry': electrode_geometry.tolist(),
        'shank_position': [s.tolist() for s in shank_position],
        'dead_channels': (final_dead_channels+1).tolist(),
        'dead_electrodes' : dead_electrodes.tolist(),
        'channel_neighbor_correlation': near_corrs.tolist(),
        'n_neighbors': n_neighbors,
    }

    # Plot summary figure
    gs = gridspec.GridSpec(6, n_maps)
    fig = plt.figure(figsize=(n_maps * 4, 24))
    for i in range(n_maps):
        channel_map, _, _ = get_electrode_by_name(electrodes[i])
        channel_map = np.array(channel_map) - 1
        sorted_corrs = corrs[channel_map, :][:, channel_map]
        ax = plt.subplot(gs[0, i])
        im = ax.imshow(sorted_corrs, aspect='auto')
        ax.set_title(electrodes[i])
        ax.set_xticks([0, n_channels-1])
        if i == 0:
            ax.set_ylabel('Channel')
            ax.set_yticks([0, n_channels-1])
        else:
            ax.set_yticks([])
        if i == n_maps // 2:
            ax.set_xlabel('Channel')
        if i == n_maps - 1:
            fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

        ax = plt.subplot(gs[2, i])
        ax.set_title(electrodes[i])
        good_electrodes = ~np.isin(channel_map, dead_channels)
        im = ax.imshow(sorted_corrs[good_electrodes, :][:, good_electrodes], aspect='auto')
        ax.set_xticks([0, np.sum(good_electrodes)-1])
        if i == 0:
            ax.set_ylabel('Good Channel')
            ax.set_yticks([0, np.sum(good_electrodes)-1])
        else:
            ax.set_yticks([])
        if i == n_maps // 2:
            ax.set_xlabel('Good Channel')
        if i == n_maps - 1:
            fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

        ax = plt.subplot(gs[5, i])
        ax.set_title(electrodes[i])
        good_electrodes = ~np.isin(channel_map, final_dead_channels)
        ax.imshow(sorted_corrs[good_electrodes, :][:, good_electrodes], aspect='auto')
        ax.set_xticks([0, np.sum(good_electrodes)-1])
        if i == 0:
            ax.set_ylabel('Final Channel')
            ax.set_yticks([0, np.sum(good_electrodes)-1])
        else:
            ax.set_yticks([])
        if i == n_maps // 2:
            ax.set_xlabel('Final Channel')
        if i == n_maps - 1:
            fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

    ax = plt.subplot(gs[1,:])
    ax.axhline(first_thresh, color='r', linestyle='--')
    ax.scatter(range(n_channels), med_corrs, c='k')
    ax.scatter(dead_channels, med_corrs[dead_channels], c='r', s=100)
    ax.set_title('Median correlation')
    ax.set_xlabel('Channel')
    ax.set_ylabel('Correlation')

    ax = plt.subplot(gs[3,:])
    ax.bar(electrodes, corr_roughness)
    ax.scatter(map_id, corr_roughness[map_id], c='r', s=100, marker='x')
    ax.set_title('Channel Map Detection')
    ax.set_ylabel('Mean absolute correlation roughness')
    ax.set_xlabel('Electrode')

    ax = plt.subplot(gs[4,:])
    ax.axhline(second_thresh, color='r', linestyle='--')
    ax.scatter(range(len(channel_map)), near_corrs, c='k')
    ax.scatter(final_dead_channels, near_corrs[final_dead_channels], c='r', s=100)
    ax.set_title('Median correlation with neighbors')
    ax.set_xlabel('Channel')
    fig.tight_layout()

    if cache_dir is not None:
        json.dump(electrode, open(cache_dir / 'electrode.json', 'w'))
        fig.savefig(cache_dir / 'dead_channel_detection.png', dpi=300)

    if not show:
        plt.close(fig)
    else:
        plt.show()

    return electrode


def import_openephys_continuous(input_directory, output_directory, electrode_name, recalc=False, progress=False):
    '''
    Import OpenEphys continuous files into a single .dat file for each shank.
    The .dat files will contain the raw data from the continuous files, concatenated in time.
    Currently assumes that there are two shanks.
    '''
    from spikeinterface.core import read_binary
    from probeinterface import Probe, combine_probes
    logger.info(f'Importing OpenEphys continuous files from {input_directory} to {output_directory}')
    input_directory = Path(input_directory)
    assert input_directory.exists(), f'Input directory {input_directory} does not exist'
    output_directory = Path(output_directory)
    output_directory.mkdir(exist_ok=True, parents=True)

    # Check if we need to recalculate
    f_dat = output_directory / 'recording.dat'
    f_meta = output_directory / 'ephys_metadata.json'
    if not recalc and f_dat.exists() and f_meta.exists():
        f_dat_size = f_dat.stat().st_size
        metadata = json.load(open(f_meta, 'r'))
        n_samples = metadata['n_samples']
        n_channels = metadata['n_channels']
        dtype = metadata['dtype']
        expected_size = n_samples * n_channels * np.dtype(dtype).itemsize
        if f_dat_size == expected_size:
            if metadata['probe_name'] == electrode_name:
                logger.info('Files already exist, returnning early')
                seg_out = read_binary(f_dat,
                                      num_channels=n_channels, dtype=dtype, 
                                      sampling_frequency=metadata['sample_rate'], is_filtered=False, 
                                      t_starts=[0])

                return seg_out
            else:
                logger.info(f'recording.dat exists but probe name does not match metadata ({metadata["probe_name"]} vs {electrode_name}). Reimporting.')
        else:
            logger.info(f'recording.dat exists but size does not match metadata ({f_dat_size} vs {expected_size}). Reimporting.')

    channel_map, probe_geometry, shank_inds = get_electrode_by_name(electrode_name)

    f_chs= list(input_directory.glob('*CH*.continuous'))
    channels = {
        f.stem.split('_')[-1]: OpenEphysContinuousFile(f) for f in f_chs
    }
    n_channels = len(f_chs)
    assert n_channels == len(channel_map), f'Number of channels ({n_channels}) does not match number of channels in electrode ({len(channel_map)})'

    # check record consistency
    n_records = np.array([f.n_records for f in channels.values()])
    assert np.all(n_records == n_records[0]), 'All .continuous files should have the same number of records'
    n_records = n_records[0]
    
    n_recordings = np.array([len(np.unique(f.get_recording_indices())) for f in channels.values()])
    if not np.all(n_recordings == 1):
        logger.warning('At least one file has more than one recording index, concatenating recordings')

    # check sampling rate consistency
    sampling_rates = np.array([f.get_sample_rate() for f in channels.values()])
    assert np.all(sampling_rates == sampling_rates[0]), 'All .continuous files should have the same sampling rate'
    sample_rate = sampling_rates[0]

    # check uV_per_bit consistency
    uV_per_bit = np.array([f.get_uV_per_bit() for f in channels.values()])
    assert np.all(uV_per_bit == uV_per_bit[0]), 'All .continuous files should have the same gain'
    uV_per_bit = uV_per_bit[0]

    # check for data skips
    chan = list(channels.values())[0]
    timestamps = chan.get_timestamps()
    sample_counts = chan.get_sample_counts()
    n_samples = np.sum(sample_counts)
    # Find the index of the record at the start of each contiguous block of records
    block_start_records = np.concatenate( 
        [[0], np.where(np.diff(timestamps) != sample_counts[:-1])[0]+1])
    # Find the index of the sample at the start of each contiguous block of records
    record_start_inds = np.concatenate([[0], np.cumsum(sample_counts)]).astype(np.int64)
    block_start_inds = record_start_inds[block_start_records]
    block_n_samples = np.diff(np.concatenate([block_start_inds, [n_samples]])).astype(np.int64)
    # Find the starting timestamp of each contiguous block of records
    block_start_samples = timestamps[block_start_records]
    block_start_times = block_start_samples / sample_rate
    logger.info(f'Found {len(block_start_records)} recording block(s)')

    metadata = {
        'sample_rate': float(sample_rate),
        'uV_per_bit': float(uV_per_bit),
        'n_channels': int(n_channels),
        'n_samples': int(np.sum(sample_counts)),
        'dtype': 'int16',
        'probe_name': electrode_name,
        'probe_geometry_um': probe_geometry.tolist(),
        'shank_inds': [s.tolist() for s in shank_inds],
        'block_start_times': block_start_times.tolist(),
        'block_n_samples': block_n_samples.tolist(),
    }
    
    json.dump(metadata, open(f_meta, 'w'))

    with open(f_dat, 'wb') as f:
        iter = range(n_records)
        if progress:
            iter = tqdm(iter, desc='Exporting .dat files')
        for iR in iter:
            n_samples = int(sample_counts[iR])
            arr_out = np.zeros(n_channels * n_samples, dtype='int16')
            for iC in range(n_channels):
                ch_id = channel_map[iC]
                channel = channels[f'CH{ch_id}']
                signal = channel.get_record_signal(iR)
                arr_out[iC::n_channels] = signal
            f.write(arr_out.tobytes())

    logger.info('Done')


    # load the probe geometry
    probe_geometry = np.array(metadata['probe_geometry_um'])
    shank_inds = [np.array(s) for s in metadata['shank_inds']]
    shanks = []
    channel_inds = np.concatenate(shank_inds, axis=0)
    n_shanks = len(shank_inds)
    for iS in range(n_shanks):
        s_inds = np.array(shank_inds[iS])
        shank = Probe(ndim=2, si_units='um')
        shank.set_contacts(positions=probe_geometry[s_inds], shapes='circle', shape_params={'radius': 6})
        shank.set_device_channel_indices(s_inds)
        shanks.append(shank)

    probe = combine_probes(shanks)
    probe.set_device_channel_indices(channel_inds)

    seg_out = read_binary(f_dat,
                          num_channels=n_channels, dtype='int16', 
                          sampling_frequency=sample_rate, is_filtered=False, 
                          t_starts=[0]).set_probe(probe)
    return seg_out

