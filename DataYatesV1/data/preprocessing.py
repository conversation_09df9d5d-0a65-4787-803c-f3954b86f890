#%%
import numpy as np
from loguru import logger
from tqdm import tqdm
import matplotlib.pyplot as plt
from pathlib import Path
from scipy.signal import medfilt
import os
import json

def get_default_job_kwargs():
    n_cpus = os.cpu_count()
    n_cpus = n_cpus if n_cpus is not None else 1
    n_jobs = max(1, n_cpus - 1) 
    job_kwargs = dict(n_jobs=n_jobs, 
                      chunk_duration='2s', 
                      progress_bar=True,)
    return job_kwargs

def correct_motion(seg, cache_dir, detect_peak_args={}, localize_peak_args={}, ks_motion_args={}, dc_motion_args={}, med_motion_args={}, job_kwargs={}, recalc=False, method='med', median_filter_size=1):
    import medicine
    from spikeinterface.sortingcomponents.peak_detection import detect_peaks
    from spikeinterface.sortingcomponents.peak_localization import localize_peaks
    from spikeinterface.sortingcomponents.motion import estimate_motion, motion_utils, interpolate_motion
    from spikeinterface.preprocessing import astype
    
    print('Starting motion correction...')

    if isinstance(cache_dir, str):
        cache_dir = Path(cache_dir)
    cache_dir.mkdir(parents=True, exist_ok=True)

    job_kwargs = dict(get_default_job_kwargs(), **job_kwargs)

    ###
    # Detect peaks
    ###

    default_detect_peak_args = dict(
        method = 'locally_exclusive', 
        radius_um = 100, 
        detect_threshold=7
    )
    detect_peak_args = dict(default_detect_peak_args, **detect_peak_args)

    f_peaks = cache_dir / 'peaks.npy'
    if not f_peaks.exists() or recalc:
        peaks = detect_peaks(seg, **detect_peak_args, **job_kwargs)
        np.save(cache_dir / 'peaks.npy', peaks)
    else:
        peaks = np.load(f_peaks)

    ###
    # Localize peaks
    ###

    default_localize_peak_args = dict(method = 'monopolar_triangulation')
    localize_peak_args = dict(default_localize_peak_args, **localize_peak_args)

    f_peak_locations = cache_dir / 'peak_locations.npy'
    if not f_peak_locations.exists() or recalc:
        peak_locations = localize_peaks(seg, peaks, **localize_peak_args, **job_kwargs)
        np.save(cache_dir / 'peak_locations.npy', peak_locations)
    else:
        peak_locations = np.load(f_peak_locations)

    ###
    # Kilosort motion
    ###

    print('Estimating Kilosort-like motion...')

    default_ks_motion_args = dict(method = 'iterative_template', direction = 'y', bin_s = 2.0, num_shifts_block = 5)
    ks_motion_args = dict(default_ks_motion_args, **ks_motion_args)
    ks_motion_args['method'] = 'iterative_template'

    ks_motion_dir = cache_dir / 'ks-motion'
    ks_motion_dir.mkdir(parents=True, exist_ok=True)
    if not (ks_motion_dir / "motion.npy").exists() or recalc:
        ks_motion = estimate_motion(
            recording = seg, 
            peaks = peaks,
            peak_locations = peak_locations,
            **ks_motion_args 
        )
        ks_displacement = ks_motion.displacement[0]
        if median_filter_size > 1:
            ks_displacement = medfilt(ks_displacement, kernel_size=(median_filter_size, 1))

        np.save(ks_motion_dir / "motion.npy", ks_displacement)
        np.save(ks_motion_dir / "time_bins.npy", ks_motion.temporal_bins_s[0])
        np.save(ks_motion_dir / "depth_bins.npy", ks_motion.spatial_bins_um)

    # load kilosort motion
    ks_motion = motion_utils.Motion(
        displacement=np.load(ks_motion_dir / "motion.npy"),
        temporal_bins_s=np.load(ks_motion_dir / "time_bins.npy"),
        spatial_bins_um=np.load(ks_motion_dir / "depth_bins.npy"),
    )
    
    ###
    # Varol2021 decentralized motion
    ###

    print('Estimating decentralized motion...')

    default_dc_motion_args = dict(method = 'decentralized', direction = 'y', bin_s = 2.0)
    dc_motion_args = dict(default_dc_motion_args, **dc_motion_args)

    decentralized_motion_dir = cache_dir / 'decentralized-motion'
    decentralized_motion_dir.mkdir(parents=True, exist_ok=True)
    if not (decentralized_motion_dir / "motion.npy").exists() or recalc:
        dc_motion = estimate_motion(
            recording = seg, 
            peaks = peaks,
            peak_locations = peak_locations,
            **dc_motion_args
        )
        dc_displacement = dc_motion.displacement[0]
        if median_filter_size > 1:
            dc_displacement = medfilt(dc_displacement, kernel_size=(median_filter_size, 1))
        np.save(decentralized_motion_dir / "motion.npy", dc_displacement)
        np.save(decentralized_motion_dir / "time_bins.npy", dc_motion.temporal_bins_s[0])
        np.save(decentralized_motion_dir / "depth_bins.npy", dc_motion.spatial_bins_um)

    # load decentralized motion
    dc_motion = motion_utils.Motion(
        displacement=np.load(decentralized_motion_dir / "motion.npy"),
        temporal_bins_s=np.load(decentralized_motion_dir / "time_bins.npy"),
        spatial_bins_um=np.load(decentralized_motion_dir / "depth_bins.npy"),
    )

    ###
    # MEDiCINe motion
    ###

    print('Estimating MEDiCINe motion...')

    default_med_motion_args = dict(time_bin_size = 2.0, num_depth_bins = 2)
    med_motion_args = dict(default_med_motion_args, **med_motion_args)

    # Create directory to store MEDiCINe outputs for this recording
    medicine_output_dir = cache_dir / 'medicine'
    medicine_output_dir.mkdir(parents=True, exist_ok=True)
    if not (medicine_output_dir / "motion.npy").exists() or recalc:
        medicine.run_medicine(
            peak_amplitudes=peaks['amplitude'],
            peak_depths=peak_locations['y'],
            peak_times=peaks['sample_index'] / seg.get_sampling_frequency() + seg.get_time_info()['t_start'],
            output_dir=medicine_output_dir,
            **med_motion_args
        )

        # Load MEDiCINe outputs
        med_motion = np.load(medicine_output_dir / "motion.npy")
        med_time_bins = np.load(medicine_output_dir / "time_bins.npy")
        med_depth_bins = np.load(medicine_output_dir / "depth_bins.npy")
        n_append = 5
        dt = med_time_bins[1] - med_time_bins[0]
        med_time_bins = np.concatenate(
                [med_time_bins, med_time_bins[-1] + np.arange(1, n_append + 1) * dt]
        )
        med_motion = np.concatenate(
            [med_motion, np.ones((n_append, med_motion.shape[1])) * med_motion[-1]]
        )
        if median_filter_size > 1:
            med_motion = medfilt(med_motion, kernel_size=(median_filter_size, 1))
        np.save(medicine_output_dir / "motion.npy", med_motion)
        np.save(medicine_output_dir / "time_bins.npy", med_time_bins)
        np.save(medicine_output_dir / "depth_bins.npy", med_depth_bins)

    # Load MEDiCINe outputs
    med_motion = motion_utils.Motion( 
        displacement=np.load(medicine_output_dir / "motion.npy"),
        temporal_bins_s=np.load(medicine_output_dir / "time_bins.npy"),
        spatial_bins_um=np.load(medicine_output_dir / "depth_bins.npy"),
    )

    # Interpolate motion using MEDiCINe
    motion = med_motion
    if method == 'ks':
        motion = ks_motion
    if method == 'dc':
        motion = dc_motion

    seg_sort = astype(interpolate_motion(astype(seg, "float"), motion, border_mode='force_zeros'), "int16")

    print('Finished motion correction')
    return seg_sort

def plot_motion_output(seg, cache_dir, save_dir=None, plot_stride=30, uV_per_bit=.195, recalc=False):
    from medicine.plotting import _correct_motion_on_peaks, plot_motion_correction
    from spikeinterface.sortingcomponents.motion import motion_utils
    
    if isinstance(cache_dir, str):
        cache_dir = Path(cache_dir)
    if save_dir is not None and isinstance(save_dir, str):
        save_dir = Path(save_dir)
    if save_dir is None:
        save_dir = cache_dir

    save_files = [
        'depth_raster.png',
        'motion_comparison.png',
        'amplitude_depth_comparison.png',
        'kilosort_motion_correction.png',
        'decentralized_motion_correction.png',
        'medicine_motion_correction.png',
    ]
    if all([(save_dir / f).exists() for f in save_files]) and not recalc:
        print('All plots already exist, returning...')
        return

    peaks = np.load(cache_dir / 'peaks.npy')
    peak_locations = np.load(cache_dir / 'peak_locations.npy')
    ks_motion = motion_utils.Motion(
        displacement=np.load(cache_dir / "ks-motion/motion.npy"),
        temporal_bins_s=np.load(cache_dir / "ks-motion/time_bins.npy"),
        spatial_bins_um=np.load(cache_dir / "ks-motion/depth_bins.npy"),
    )
    dc_motion = motion_utils.Motion(
        displacement=np.load(cache_dir / "decentralized-motion/motion.npy"),
        temporal_bins_s=np.load(cache_dir / "decentralized-motion/time_bins.npy"),
        spatial_bins_um=np.load(cache_dir / "decentralized-motion/depth_bins.npy"),
    )
    med_motion = motion_utils.Motion(
        displacement=np.load(cache_dir / "medicine/motion.npy"),
        temporal_bins_s=np.load(cache_dir / "medicine/time_bins.npy"),
        spatial_bins_um=np.load(cache_dir / "medicine/depth_bins.npy"),
    )

    spike_samples = peaks['sample_index']
    spike_times = spike_samples / seg.get_sampling_frequency() + seg.get_time_info()['t_start']
    spike_depths = peak_locations['y']
    spike_amps = peaks['amplitude'] * uV_per_bit

    # Subsample
    peak_samples = spike_samples[::plot_stride]
    peak_times = spike_times[::plot_stride]
    peak_depths = spike_depths[::plot_stride]
    peak_amplitudes = spike_amps[::plot_stride]

    # Normalize amplitudes by CDF to have uniform distribution
    amp_argsort = np.argsort(np.argsort(peak_amplitudes))
    peak_amplitudes = amp_argsort / len(peak_amplitudes)

    #
    # Plot depth raster
    #
    # Function for plotting neural activity
    def _plot_neural_activity(ax, times, depths, colors):
        plot = ax.scatter(times, depths, s=1, c=colors, alpha=.75)
        ax.set_xlabel("time (s)", fontsize=12)
        ax.set_ylabel("depth from probe tip (um)", fontsize=12)
        return plot

    # Scatterplot peaks
    cmap = plt.get_cmap("winter")
    colors = cmap(peak_amplitudes)
    fig, axs = plt.subplots(1, 1, figsize=(7, 5))
    plot = _plot_neural_activity(axs, peak_times, peak_depths, colors)
    fig.colorbar(plot, ax=axs)
    fig.savefig(save_dir / 'depth_raster.png')

    #
    # Plot motion estiamte comparison
    #

    depth = ks_motion.spatial_bins_um[0]
    times = ks_motion.temporal_bins_s[0]
    n_depths = 5

    probe = seg.get_probe()
    d_min = np.min(probe.contact_positions[:, 1])
    d_max = np.max(probe.contact_positions[:, 1])
    depths = np.linspace(d_min, d_max, n_depths)
    fig, axs = plt.subplots(5, 1, figsize=(10, 8), sharex=True) 
    ks_motion_depths = np.zeros((len(times), n_depths))
    dc_motion_depths = np.zeros((len(times), n_depths))
    med_motion_depths = np.zeros((len(times), n_depths))

    for i, depth in enumerate(depths):

        dist = (d_max - depth)
        ks_motion_interp = ks_motion.get_displacement_at_time_and_depth(times, np.ones(len(times)) * dist)
        ks_motion_depths[:,n_depths-i-1] = ks_motion_interp
        dc_motion_interp = dc_motion.get_displacement_at_time_and_depth(times, np.ones(len(times)) * dist)
        dc_motion_depths[:,n_depths-i-1] = dc_motion_interp
        med_motion_interp = med_motion.get_displacement_at_time_and_depth(times, np.ones(len(times)) * dist)
        med_motion_depths[:,n_depths-i-1] = med_motion_interp

        axs[i].plot(times, ks_motion_interp, label='Kilosort', alpha=.5)
        axs[i].plot(times, dc_motion_interp, label='Decentralized', alpha=.5)
        axs[i].plot(times, med_motion_interp, label='MEDiCINe', alpha=.5)
        if i == n_depths // 2: 
            axs[i].set_ylabel('Motion (um)')
        if i == n_depths - 1:
            axs[i].set_xlabel('Time (s)')
        axs[i].set_title(f'Motion estimates (depth = {depth} um)')
        if i == 0:
            axs[i].legend()
    plt.tight_layout()
    plt.savefig(save_dir / 'motion_comparison.png')

    #
    # Plot amplitude-depth comparison
    #
    # Get colors and create figure
    cmap = plt.get_cmap('winter')
    colors = cmap(peak_amplitudes)
    fig, axes = plt.subplots(1, 3, figsize=(15, 10), sharex=True, sharey=True)
    
    peak_depth_ks = _correct_motion_on_peaks(
        peak_times,
        peak_depths,
        ks_motion_depths,
        times,
        depths
    )

    _ = _plot_neural_activity(axes[0], peak_times, peak_depth_ks, colors)
    axes[0].set_title("Kilosort")

    peak_depth_dc = _correct_motion_on_peaks(
        peak_times,
        peak_depths,
        dc_motion_depths,
        times,
        depths
    )
    _ = _plot_neural_activity(axes[1], peak_times, peak_depth_dc, colors)
    axes[1].set_title("Decentralized")

    peak_depth_med = _correct_motion_on_peaks(
        peak_times,
        peak_depths,
        med_motion_depths,
        times,
        depths
    )
    plot = _plot_neural_activity(axes[2], peak_times, peak_depth_med, colors)
    axes[2].set_title("MEDiCINe")
    fig.colorbar(plot, ax=axes[2]) 
    plt.tight_layout()
    plt.savefig(save_dir / 'amplitude_depth_comparison.png')

    #
    #   Plot individual motion correction
    #

    # Kilosort
    f_ks = plot_motion_correction(
        spike_times,
        spike_depths,
        spike_amps,
        times,
        depths,
        ks_motion_depths,
    )
    f_ks.suptitle('Kilosort')
    f_ks.savefig(save_dir / 'kilosort_motion_correction.png')

    # Decentralized
    f_dc = plot_motion_correction(
        spike_times,
        spike_depths,
        spike_amps,
        times,
        depths,
        dc_motion_depths,
    )
    f_dc.suptitle('Decentralized')    
    f_dc.savefig(save_dir / 'decentralized_motion_correction.png')

    # MEDiCINe
    f_med = plot_motion_correction(
        spike_times,
        spike_depths,
        spike_amps,
        times,
        depths,
        med_motion_depths,
    )
    f_med.suptitle('MEDiCINe')
    f_med.savefig(save_dir / 'medicine_motion_correction.png')
    
    plt.close('all')

def preprocess_shank(f_binary, n_channels, probe, cache_dir, dead_channels=None, sample_rate=30000, recalc=False):
    from spikeinterface.core import read_binary
    from spikeinterface.preprocessing import highpass_filter, common_reference, interpolate_bad_channels
    
    '''
    Preprocess a single shank of Mitchell V1 data by highpass filtering, common referencing, and motion correction.

    Parameters
    ----------
    f_binary : Path
        Path to the raw binary data file.
    n_channels : int
        The number of channels in the recording.
    probe : Probe
        The probe object.
    cache_dir : Path
        The directory to save the motion correction cache.
    recalc : bool
        If True, will recalculate motion correction.
        If False, will load from cache if available.

    Returns
    -------
    seg_motion : RecordingExtractor
        A motion corrected recording extractor.
    '''
    seg = read_binary(f_binary, num_channels=n_channels, dtype='int16', sampling_frequency=sample_rate, is_filtered=False, t_starts=[0])
    seg_probe = seg.set_probe(probe)

    if dead_channels is not None:
        # Interpolate dead channels
        channel_ids = [c for c in dead_channels if c in seg_probe.get_channel_ids()]
        seg_probe = interpolate_bad_channels(seg_probe, bad_channel_ids=channel_ids)

    seg_hp = highpass_filter(seg_probe, freq_min=300, direction='forward-backward')
    seg_pre = common_reference(seg_hp, reference='global')

    seg_motion = correct_motion(seg_pre, cache_dir = cache_dir, recalc=recalc,
                            detect_peak_args={'detect_threshold': 7}, localize_peak_args={},
                            ks_motion_args={}, dc_motion_args={}, med_motion_args={}, method='med',
                            median_filter_size=11)

    plot_motion_output(seg_motion, cache_dir=cache_dir, recalc=recalc)

    return seg_motion

def merge_segments_to_dat(segments, f_dat, batch_size=30000, recalc=False, progress=False):
    '''
    Merge a list of spikeinterface segments to a single .dat file, concatenating along the channel axis.

    Parameters
    ----------
    segments : list
        A list of RecordingExtractor objects.
    f_dat : Path
        The path to save the merged .dat file.
    batch_size : int
        The number of samples to read at a time.
    recalc : bool
        If True, will recalculate the .dat file.
    progress : bool
        If True, will display a progress bar.
    '''
    n_samples = np.array([s.get_num_frames() for s in segments])
    assert np.all(n_samples == n_samples[0]), 'All segments must have the same number of samples.'

    dtypes = np.array([s.get_dtype() for s in segments])
    assert np.all(dtypes == dtypes[0]), 'All segments must have the same dtype.'
    
    n_samples = segments[0].get_num_frames()
    if f_dat.exists() and not recalc:
        expected_n_bytes = np.sum([s.get_total_memory_size() for s in segments])
        n_bytes = f_dat.stat().st_size
        if n_bytes == expected_n_bytes:
            logger.info(f'{f_dat} already exists, returning early...')
            return
        else:
            logger.info(f'{f_dat} exists but size does not match ({n_bytes/10**9:.2f} GB vs {expected_n_bytes/10**9:.2f} GB). remerging...')
            raise NotImplementedError

    with open(f_dat, 'wb') as f:
        iter = range(0, n_samples, batch_size)
        if progress:
            iter = tqdm(iter, desc='Merging segments to .dat file')
        for i in iter:
            traces = []
            for seg in segments:
                traces.append(seg.get_traces(start_frame=i, end_frame=i+batch_size)) 
            arr_out = np.concatenate(traces, axis=1).flatten()
            f.write(arr_out.tobytes())

def preprocess_recording(recording_directory, dead_channels=None, recalc=False):
    from probeinterface import Probe, combine_probes
    from spikeinterface.core import read_binary
    
    f_meta = recording_directory / 'ephys_metadata.json'
    metadata = json.load(open(f_meta, 'r'))

    sample_rate = metadata['sample_rate']
    n_channels = metadata['n_channels']

    # load the probe geometry
    probe_geometry = np.array(metadata['probe_geometry_um'])
    shank_inds = [np.array(s) for s in metadata['shank_inds']]
    shanks = []
    channel_inds = np.concatenate(shank_inds, axis=0)
    n_shanks = len(shank_inds)
    for iS in range(n_shanks):
        s_inds = np.array(shank_inds[iS])
        shank = Probe(ndim=2, si_units='um')
        shank.set_contacts(positions=probe_geometry[s_inds], shapes='circle', shape_params={'radius': 6})
        shank.set_device_channel_indices(s_inds)
        shanks.append(shank)

    probe = combine_probes(shanks)
    probe.set_device_channel_indices(channel_inds)

    # preprocess the shanks independently
    # we do this because the shanks may drift differentially
    f_rec = recording_directory / 'recording.dat'
    segments = []
    for iS in range(n_shanks):
        logger.info(f'Processing shank {iS+1} of {n_shanks}')
        shank = shanks[iS]
        cache_dir = recording_directory / f'shank{iS+1}-motion'
        seg = preprocess_shank(f_rec, n_channels, shank, cache_dir, dead_channels=dead_channels,
                               sample_rate=sample_rate, recalc=recalc)
        segments.append(seg)

    # remerge the binaries
    f_out = recording_directory / 'preprocessed.dat'
    from DataYatesV1.data.preprocessing import merge_segments_to_dat
    merge_segments_to_dat(segments, f_out, recalc=recalc, progress=True)

    seg_out = read_binary(f_out, num_channels=64, 
                            dtype='int16', sampling_frequency=sample_rate, 
                            is_filtered=True, t_starts=[0]
                           ).set_probe(probe)

    return seg_out



# %%
