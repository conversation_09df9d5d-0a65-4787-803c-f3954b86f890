
import json
import numpy as np
from loguru import logger
from scipy.signal import butter, filtfilt, iirnotch
from tqdm import tqdm
from unittest.mock import Mock
from pathlib import Path

def lowpass_filter(data, freq, fs, order=2, axis=0):
    nyq = 0.5 * fs
    normal_cutoff = freq / nyq
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    y = filtfilt(b, a, data, axis=axis)
    return y

def bandpass_filter(data, low_freq, high_freq, fs, order=2, axis=0):
    nyq = 0.5 * fs
    low = low_freq / nyq
    high = high_freq / nyq
    b, a = butter(order, [low, high], btype='band', analog=False)
    y = filtfilt(b, a, data, axis=axis)
    return y

def notch_filter(data, freq, fs, width=1, axis=0):
    nyq = 0.5 * fs
    f0 = freq / nyq
    Q = freq / width
    b, a = iirnotch(f0, Q)
    y = filtfilt(b, a, data, axis=axis)
    return y


def export_lfp(recording_dir, lfp_dir,
               lp_freq=250, lp_ord=2, fs_out=1000, 
               notch_60hz=False, notch_width=3,
               batch_duration=1, batch_padding=0.02,
               recalc=False, progress=False):
    '''
    Export the LFP from the raw data. Note: This function does not currently correct for probe motion.

    Parameters
    ----------
    recording_dir : Path
        Path to a folder containing the raw data.
    lfp_dir : Path
        Path to a folder to save the LFP data.
    lp_freq : int, optional
        Low-pass filter cutoff frequency. The default is 250.
    lp_ord : int, optional
        Low-pass filter order. The default is 2.
        The actual order is 2*order, since the filter is forward and backward.
    fs_out : int, optional
        Output sampling frequency. The default is 1000.
    notch_60hz : bool, optional
        Apply a 60 Hz notch filter. The default is False.
    recalc : bool, optional
        If True, the LFP is recalculated. The default is False.
        If False, the LFP is recalculated only if the output file does not exist.
    progress : bool, optional
        Show progress bar. The default is False.

    '''
    from spikeinterface.core import read_binary

    if isinstance(recording_dir, str):
        recording_dir = Path(recording_dir)
    
    if isinstance(lfp_dir, str):
        lfp_dir = Path(lfp_dir)
    lfp_dir.mkdir(parents=True, exist_ok=True)

    f_dat = recording_dir / 'recording.dat'
    f_ephys_meta = recording_dir / 'ephys_metadata.json'
    assert f_dat.exists() and f_ephys_meta.exists(), 'recording.dat and ephys_metadata.json must exist in the recording directory.'

    ephys_metadata = json.load(open(f_ephys_meta, 'r'))
    sample_rate = ephys_metadata['sample_rate']
    n_channels = ephys_metadata['n_channels']
    uV_per_bit = ephys_metadata['uV_per_bit']

    decimate = int(sample_rate / fs_out)
    fs_out = sample_rate / decimate

    batch_samples = int(batch_duration * sample_rate // decimate * decimate)
    batch_padding_samples = int(batch_padding * sample_rate)

    block_n_samples = np.array(ephys_metadata['block_n_samples']).astype(int)
    n_blocks = len(block_n_samples)
    block_n_batches = np.ceil(block_n_samples / batch_samples).astype(int)
    block_edge_samples = np.concatenate([[0], np.cumsum(block_n_samples)])

    lfp_block_n_samples = block_n_samples // decimate + 1
    n_samples_out = int(lfp_block_n_samples.sum())

    metadata = {
        'sample_rate': fs_out,
        'decimate': decimate,
        'lp_type': 'butter',
        'lp_freq': lp_freq,
        'lp_ord': lp_ord*2,
        'batch_duration': batch_duration,
        'batch_padding': batch_padding,
        'notch_60hz': notch_60hz,
        'notch_width': notch_width,
        'n_channels': n_channels,
        'n_samples': n_samples_out,
        'dtype': 'float32',
        'units': 'uV',
        'block_start_times': ephys_metadata['block_start_times'],
        'block_n_samples': lfp_block_n_samples.tolist(),
        'probe_geometry_um': ephys_metadata['probe_geometry_um'],
        'probe_name': ephys_metadata['probe_name'],
        'shank_inds': ephys_metadata['shank_inds']
    }

    # Check if we need to recalculate
    f_meta = lfp_dir / 'lfp_metadata.json'
    f_binary = lfp_dir / 'lfp.dat'
    if f_binary.exists() and f_meta.exists() and not recalc:
        lfp_metadata = json.load(open(f_meta, 'r'))
        checks = [metadata[k] == lfp_metadata[k] for k in metadata.keys() if not isinstance(metadata[k], list)]
        if all(checks):
            bin_size = f_binary.stat().st_size
            expected_size = n_samples_out * n_channels * np.dtype(metadata['dtype']).itemsize
            if bin_size == expected_size:
                logger.info('LFP already either exists with given parameters.')
                return
            else:
                logger.info(f'LFP exists but size does not match metadata ({bin_size/10**6:.2f} MB vs {expected_size/10**6:.2f} MB). Recalculating.')

    json.dump(metadata, open(f_meta, 'w'))

    seg = read_binary(f_dat, num_channels=n_channels, dtype=ephys_metadata['dtype'], sampling_frequency=sample_rate, is_filtered=False, t_starts=[0])

    with open(f_binary, 'wb') as f:
        pbar = tqdm(total=np.sum(block_n_batches), desc='Exporting LFP') if progress else Mock()
        for iB in range(n_blocks):
            block_start = block_edge_samples[iB]
            block_end = block_edge_samples[iB+1]
            for i in range(block_start, block_end, batch_samples):
                i0 = i-batch_padding_samples
                n_pre = 0 if i0 > 0 else -i0
                i0 = 0 if i0 < 0 else i0

                i1 = i + batch_samples 
                i1 = block_end if i1 >= block_end else i1
                trace_len = i1 - i
                i1 += batch_padding_samples
                n_post = 0 if i1 <= block_end else i1 - block_end
                i1 = block_end if i1 > block_end else i1
                 
                traces = seg.get_traces(start_frame=i0, end_frame=i1).astype(np.float32) * uV_per_bit
                traces = np.pad(traces, 
                                  ((n_pre, n_post), (0,0)), 
                                  'constant')

                assert len(traces) == trace_len + 2 * batch_padding_samples, f'{len(traces)} != {trace_len + 2 * batch_padding_samples}'

                traces = lowpass_filter(traces, lp_freq, sample_rate,
                                        order=lp_ord, axis=0)
                traces = notch_filter(traces, 60, sample_rate, width=notch_width, axis=0) if notch_60hz else traces

                traces = traces[batch_padding_samples:-batch_padding_samples]
                assert len(traces) == trace_len
                traces = traces[::decimate]
                traces = traces.flatten().astype(np.float32)
                f.write(traces.tobytes())
                pbar.update(1)


def export_apband(recording_dir, apband_dir,
                  hp_freq=300, lp_freq=3000, bp_ord=2, fs_out=1000, 
                  rectify=True, smooth_freq=200, 
                  notch_60hz=True, notch_width=3,
                  batch_duration=1, batch_padding=0.1,
                  recalc=False, progress=False):
    '''
    Export the action potential (AP) band from raw data.

    Parameters
    ----------
    recording_dir : Path
        Path to a folder containing the raw data.
    hp_freq : int, optional
        High-pass filter cutoff frequency. The default is 300 Hz.
    lp_freq : int, optional
        Low-pass filter cutoff frequency. The default is 3000 Hz.
    bp_ord : int, optional
        Band-pass filter order. The default is 2 (4th-order Butterworth).
    fs_out : int, optional
        Output sampling frequency for MUA. Default is 1000 Hz.
    rectify : bool, optional
        If True, rectifies the signal and applies smoothing to estimate MUA. Default is False.
    smooth_freq : int, optional
        Cutoff frequency for smoothing (if rectify=True). Default is 200 Hz.
    notch_60hz : bool, optional
        Apply a 60 Hz notch filter. Default is False.
    recalc : bool, optional
        If True, AP band is recalculated. Default is False.
    progress : bool, optional
        Show progress bar. Default is False.
    '''
    from spikeinterface.core import read_binary

    if isinstance(recording_dir, str):
        recording_dir = Path(recording_dir)
    if isinstance(apband_dir, str):
        apband_dir = Path(apband_dir)
    apband_dir.mkdir(parents=True, exist_ok=True)

    f_dat = recording_dir / 'recording.dat'
    f_ephys_meta = recording_dir / 'ephys_metadata.json'
    assert f_dat.exists() and f_ephys_meta.exists(), 'recording.dat and ephys_metadata.json must exist in the recording directory.'

    ephys_metadata = json.load(open(f_ephys_meta, 'r'))
    sample_rate = ephys_metadata['sample_rate']
    n_channels = ephys_metadata['n_channels']
    n_samples = ephys_metadata['n_samples']
    uV_per_bit = ephys_metadata['uV_per_bit']

    decimate = int(sample_rate / fs_out)
    fs_out = sample_rate / decimate
    n_samples_out = n_samples // decimate + 1

    batch_samples = int(batch_duration * sample_rate // decimate * decimate)
    batch_padding_samples = int(batch_padding * sample_rate)

    metadata = {
        'sample_rate': fs_out,
        'decimate': decimate,
        'bp_type': 'butter',
        'hp_freq': hp_freq,
        'lp_freq': lp_freq,
        'bp_ord': bp_ord * 2,
        'batch_duration': batch_duration,
        'batch_padding': batch_padding,
        'notch_60hz': notch_60hz,
        'notch_width': notch_width,
        'rectify': rectify,
        'smooth_freq': smooth_freq if rectify else None,
        'n_channels': n_channels,
        'n_samples': n_samples_out,
        'dtype': 'float32',
        'units': 'uV'
    }

    # Check if we need to recalculate
    f_meta = apband_dir / 'apband_metadata.json'
    f_binary = apband_dir / 'apband.dat'
    if f_binary.exists() and f_meta.exists() and not recalc:
        ap_metadata = json.load(open(f_meta, 'r'))
        checks = [metadata[k] == ap_metadata[k] for k in metadata.keys() if not isinstance(metadata[k], list)]
        if all(checks):
            bin_size = f_binary.stat().st_size
            expected_size = n_samples_out * n_channels * np.dtype(metadata['dtype']).itemsize
            if bin_size == expected_size:
                logger.info('AP band already exists with given parameters.')
                return
            else:
                logger.info(f'AP band exists but size does not match metadata ({bin_size/10**6:.2f} MB vs {expected_size/10**6:.2f} MB). Recalculating.')

    json.dump(metadata, open(f_meta, 'w'))

    seg = read_binary(f_dat, num_channels=n_channels, dtype=ephys_metadata['dtype'], sampling_frequency=sample_rate, is_filtered=False, t_starts=[0])

    with open(f_binary, 'wb') as f:
        iter = range(0, n_samples, batch_samples)
        if progress:
            iter = tqdm(iter, desc='Exporting AP band')
        for i in iter:
            i0 = i - batch_padding_samples
            n_pre = 0 if i0 > 0 else -i0
            i0 = 0 if i0 < 0 else i0

            i1 = i + batch_samples
            i1 = n_samples if i1 >= n_samples else i1
            trace_len = i1 - i
            i1 += batch_padding_samples
            n_post = 0 if i1 <= n_samples else i1 - n_samples
            i1 = n_samples if i1 > n_samples else i1

            traces = seg.get_traces(start_frame=i0, end_frame=i1).astype(np.float32) * uV_per_bit
            traces = np.pad(traces, ((n_pre, n_post), (0, 0)), 'constant')

            assert len(traces) == trace_len + 2 * batch_padding_samples, f'{len(traces)} != {trace_len + 2 * batch_padding_samples}'

            traces = bandpass_filter(traces, hp_freq, lp_freq, sample_rate, order=bp_ord, axis=0)
            traces = notch_filter(traces, 60, sample_rate, width=notch_width, axis=0) if notch_60hz else traces

            if rectify:
                traces = np.abs(traces)  # Rectify signal
                traces = lowpass_filter(traces, smooth_freq, sample_rate, order=2, axis=0)  # Smooth

            traces = traces[batch_padding_samples:-batch_padding_samples]
            assert len(traces) == trace_len
            traces = traces[::decimate]
            traces = traces.flatten().astype(np.float32)
            f.write(traces.tobytes())



