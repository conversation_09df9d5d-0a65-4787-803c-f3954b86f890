"""
Neural network modules for DataYatesV1.

This package contains modular components for building neural network models.
"""

from .frontend import DAModel, TemporalBasis, AffineAdapter
from .convnet import VanillaCNN, ResNet, DenseNet, BaseConvNet
from .recurrent import ConvLSTM, ConvGRU
from .modulator import ConcatModulator, FiLMModulator, MODULATORS
from .readout import DynamicGaussianReadout, DynamicGaussianReadoutEI
from .common import SplitRelu, chomp

# For backward compatibility
from .conv_blocks import ConvBlock

# Model architectures
from .models import ModularV1Model, MultiDatasetV1Model
