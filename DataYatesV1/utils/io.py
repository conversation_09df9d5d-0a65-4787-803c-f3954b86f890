import numpy as np
import os
from pathlib import Path
import pandas as pd
from mat73 import loadmat
import json
from functools import cached_property
from .general import convert_samples_to_time, get_clock_functions

def load_lfp(ephys_path):
    '''
    Load LFP data from an ephys path
    INPUTS:
    - ephys_path: path to ephys data
    OUTPUTS:
    - lfp [Nsamples x Nchannels]: lfp data as memmap
    - lfp_meta: metadata for lfp data
    
    lfp, lfp_meta = load_lfp(ephys_path)

    jly 2025-02-07 wrote it
    '''
    # find file in ephys_path with lfp.dat
    lfp_file_path = os.path.join(ephys_path, 'lfp', 'lfp.dat')
    lfp_meta_path = os.path.join(ephys_path, 'lfp', 'lfp_metadata.json')

    # read json file for metadata
    import json
    with open(lfp_meta_path, 'r') as f:
        lfp_meta = json.load(f)

    # read lfp data
    lfp = np.memmap(lfp_file_path, dtype=lfp_meta['dtype'], mode='r', shape=(lfp_meta['n_samples'],lfp_meta['n_channels']))

    return lfp, lfp_meta

class KilosortResults:
    def __init__(self, directory):
        if isinstance(directory, str):
            directory = Path(directory)
        assert isinstance(directory, Path), 'directory must be a string or Path object'
        assert directory.exists(), f'{directory} does not exist'
        assert directory.is_dir(), f'{directory} is not a directory'
        self.directory = directory

        # Move directory to sorter_output if it is a kilosort4 output directory
        if (directory / 'sorter_output').exists():
            directory = directory / 'sorter_output'

        self.spike_times_file = directory / 'spike_times.npy'
        assert self.spike_times_file.exists(), f'{self.spike_times_file} does not exist'
        self._spike_times = None # TODO: I don't like that spike "times" are actually samples 

        self.spike_amplitudes_file = directory / 'amplitudes.npy'
        assert self.spike_amplitudes_file.exists(), f'{self.spike_amplitudes_file} does not exist'
        self._spike_amplitudes = None

        self.st_file = directory / 'full_st.npy'
        if not self.st_file.exists():
            print(f'Warning: {self.st_file} does not exist. Use Kilosort4 with save_extra_vars=True to generate.')
        self.kept_spikes_file = directory / 'kept_spikes.npy'
        if not self.kept_spikes_file.exists():
            print(f'Warning: {self.kept_spikes_file} does not exist. Use Kilosort4 with save_extra_vars=True to generate.')

        self.spike_clusters_file = directory / 'spike_clusters.npy'
        assert self.spike_clusters_file.exists(), f'{self.spike_clusters_file} does not exist'

        self.spike_templates_file = directory / 'spike_templates.npy'
        assert self.spike_templates_file.exists(), f'{self.spike_templates_file} does not exist'

        self.cluster_labels_file = directory / 'cluster_KSLabel.tsv'
        assert self.cluster_labels_file.exists(), f'{self.cluster_labels_file} does not exist'

        # check if ephys_metadata.json exists two levels up
        ephys_metadata_file = directory.parent.parent / 'ephys_metadata.json'
        if ephys_metadata_file.exists():
            import json
            with open(ephys_metadata_file, 'r') as f:
                self.ephys_metadata = json.load(f)

        # check if ../spikeinterface_log.json exists
        spikeinterface_log_file = directory.parent / 'spikeinterface_log.json'
        if spikeinterface_log_file.exists():
            import json
            with open(spikeinterface_log_file, 'r') as f:
                self.spikeinterface_log = json.load(f)
        
    @cached_property
    def spike_times(self):
        '''
        This now properly returns times if that info is available
        '''
        spike_times = np.load(self.spike_times_file)
        if hasattr(self, 'ephys_metadata'):
            return convert_samples_to_time(spike_times, self.ephys_metadata['sample_rate'], self.ephys_metadata['block_start_times'], self.ephys_metadata['block_n_samples'])
        else:
            print('Warning: ephys_metadata not found. Returning samples instead of times.')

        return spike_times
    
    @cached_property
    def spike_samples(self):
        return np.load(self.spike_times_file)
    
    @cached_property
    def spike_amplitudes(self):
        return self.st[:,2]

    @cached_property
    def st(self): 
        st = np.load(self.st_file)
        spikes = np.load(self.kept_spikes_file)
        return st[spikes]
    
    @cached_property
    def spike_clusters(self):
        return np.load(self.spike_clusters_file)

    @cached_property
    def spike_templates(self):
        return np.load(self.spike_templates_file)

    @cached_property
    def cluster_labels(self):
        return pd.read_csv(self.cluster_labels_file, sep='\t')

class YatesV1Session:
    """
    A class for wrapping an Mitchell lab ephys session
    """
    def __init__(self, name, mat_dir = None, proc_dir = None):
        '''
        Parameters
        ----------
        name : str
            The name of the session (Subject_YYYY-MM-DD)
        mat_dir : str, optional
            The directory containing the .mat files for all sessions
        proc_dir : str, optional
            The directory containing the processed data for all sessions
        '''

        if mat_dir is None:
            mat_dir = Path('/mnt/ssd/YatesMarmoV1/mat')
        if proc_dir is None:
            proc_dir = Path('/mnt/ssd/YatesMarmoV1/processed')

        self.name = name

        self.f_mat = mat_dir / f'{name}_struct.mat'
        assert self.f_mat.exists(), f'{self.f_mat} does not exist'
        self.sess_dir = proc_dir / name
        assert self.sess_dir.exists(), f'{self.sess_dir} does not exist'
    
    def __repr__(self):
        return f'YatesV1Session({self.name})'
    
    @cached_property
    def exp(self):
        '''
        Returns the experiment structure for the session (.mat file)
        '''
        return loadmat(self.f_mat)

    @cached_property
    def clock_functions(self):
        '''
        Returns the clock functions for the session
        '''
        return get_clock_functions(self.exp)

    @property
    def ptb2ephys(self):
        ptb2ephys, _ = self.clock_functions
        return ptb2ephys

    @property
    def vpx2ephys(self):
        _, vpx2ephys = self.clock_functions
        return vpx2ephys

    @cached_property
    def ks_results(self):
        '''
        Returns the KilosortResults object for the session
        '''
        return KilosortResults(self.sess_dir / 'ks4')

    @cached_property
    def dpi(self):
        '''
        Returns the DPI data for the session
        '''
        return pd.read_csv(self.sess_dir / 'dpi' / 'ddpi.csv')
    
    @cached_property
    def lfp_metadata(self):
        '''
        Returns the LFP metadata for the session
        '''
        return json.load(open(self.sess_dir / 'lfp' / 'lfp_metadata.json', 'r'))

    @property
    def lfp(self):
        '''
        Returns the LFP data for the session
        '''
        meta = self.lfp_metadata
        lfp_file_path = self.sess_dir / 'lfp' / 'lfp.dat'
        return np.memmap(lfp_file_path, dtype=meta['dtype'], mode='r', shape=(meta['n_samples'],meta['n_channels']))

    @cached_property
    def apband_metadata(self):
        '''
        Returns the AP band metadata for the session
        '''
        return json.load(open(self.sess_dir / 'apband' / 'apband_metadata.json', 'r'))

    @property
    def apband(self):
        '''
        Returns the AP band data for the session
        '''
        meta = self.apband_metadata
        apband_file_path = self.sess_dir / 'apband' / 'apband.dat'
        return np.memmap(apband_file_path, dtype=meta['dtype'], mode='r', shape=(meta['n_samples'],meta['n_channels']))

    @cached_property
    def ephys_metadata(self):
        '''
        Returns the ephys metadata for the session
        '''
        return json.load(open(self.sess_dir / 'ephys_metadata.json', 'r'))
    
    @property
    def processed_recording(self):
        '''
        Returns the processed ephys data for the session
        '''
        meta = self.ephys_metadata
        ephys_file_path = self.sess_dir / 'preprocessed.dat'
        return np.memmap(ephys_file_path, dtype=meta['dtype'], mode='r', shape=(meta['n_samples'],meta['n_channels']))

    @property
    def recording(self):
        '''
        Returns the ephys data for the session
        '''
        meta = self.ephys_metadata
        ephys_file_path = self.sess_dir / 'recording.dat'
        return np.memmap(ephys_file_path, dtype=meta['dtype'], mode='r', shape=(meta['n_samples'],meta['n_channels']))
    
    def get_si_processed_recording(self):
        '''
        Returns the processed ephys data for the session as a spikeinterface recording object
        '''

        assert (self.sess_dir / 'preprocessed.dat').exists(), 'preprocessed.dat does not exist'

        sample_rate = self.ephys_metadata['sample_rate']
        n_channels = self.ephys_metadata['n_channels']
        dtype = self.ephys_metadata['dtype']

        # load the probe geometry
        from probeinterface import Probe, combine_probes
        probe_geometry = np.array(self.ephys_metadata['probe_geometry_um'])
        shank_inds = [np.array(s) for s in self.ephys_metadata['shank_inds']]
        shanks = []
        channel_inds = np.concatenate(shank_inds, axis=0)
        n_shanks = len(shank_inds)
        for iS in range(n_shanks):
            s_inds = np.array(shank_inds[iS])
            shank = Probe(ndim=2, si_units='um')
            shank.set_contacts(positions=probe_geometry[s_inds], shapes='circle', shape_params={'radius': 6})
            shank.set_device_channel_indices(s_inds)
            shanks.append(shank)

        probe = combine_probes(shanks)
        probe.set_device_channel_indices(channel_inds)

        # preprocess the shanks independently
        # we do this because the shanks may drift differentially
        from spikeinterface.core import read_binary
        seg_out = read_binary(self.sess_dir/'preprocessed.dat', num_channels=64, 
                                dtype='int16', sampling_frequency=sample_rate, 
                                is_filtered=True, t_starts=[0]
                            ).set_probe(probe)

        return seg_out
    
    
def get_session(subject, date, mat_dir=None, proc_dir=None) -> YatesV1Session:
    '''
    Get a YatesV1Session object for a given subject and date

    Parameters
    ----------
    subject : str
        The subject name (e.g. 'Logan' or 'Allen')
    date : str
        The date of the session in 'YYYY-MM-DD' format or datetime object
    mat_dir : str, optional
        The directory containing the .mat files for all sessions
    proc_dir : str, optional
        The directory containing the processed data for all sessions

    Returns
    -------
    YatesV1Session
        A YatesV1Session object for the specified subject and date. 
        Returns None if the session does not exist.
    '''
    if mat_dir is None:
        mat_dir = Path('/mnt/ssd/YatesMarmoV1/mat')
    if proc_dir is None:
        proc_dir = Path('/mnt/ssd/YatesMarmoV1/processed')

    subject = str(subject)  # Ensure subject is a string
    subject = subject.capitalize()
    if subject not in ['Logan', 'Allen']:
        print('Invalid subject name. Must be one of: Logan, Allen.')
        return None

    # Format the session name
    if isinstance(date, str):
        session_name = f'{subject}_{date}'
    else:
        session_name = f'{subject}_{date.strftime("%Y-%m-%d")}'
    # Check if the session exists in the mat directory
    f_mat = mat_dir / f'{session_name}_struct.mat'
    if not f_mat.exists():
        print(f'Session {session_name} does not exist in {mat_dir}.')
        return None

    # Check if the processed directory exists
    sess_dir = proc_dir / session_name
    if not sess_dir.exists():
        print(f'Session {session_name} does not exist in {proc_dir}.')
        return None
    # If it exists, return the YatesV1Session object
    try:
        session = YatesV1Session(session_name, mat_dir=mat_dir, proc_dir=proc_dir)
        return session
    except Exception as e:
        print(f'Error creating YatesV1Session for {session_name}: {e}')
        return None
    
def get_complete_sessions(mat_dir=None, proc_dir=None):
    '''
    Get a list of all sessions that have a mat file, processed data, and DPI data

    Parameters
    ----------
    mat_dir : str, optional
        The directory containing the .mat files for all sessions
    proc_dir : str, optional
        The directory containing the processed data for all sessions

    Returns
    -------
    sessions : list
        A list of YatesV1Session objects
    '''
    if mat_dir is None:
        mat_dir = Path('/mnt/ssd/YatesMarmoV1/mat')
    if proc_dir is None:
        proc_dir = Path('/mnt/ssd/YatesMarmoV1/processed')
    
    sessions = []
    mats = list(mat_dir.glob('*_struct.mat'))
    mats.sort()
    for f in mats:
        name = f.stem.replace('_struct', '')
        if (proc_dir / name).exists() \
            and (proc_dir / name / 'ks4').exists() \
            and (proc_dir / name / 'dpi' / 'ddpi.csv').exists():
            sessions.append(YatesV1Session(name, mat_dir=mat_dir, proc_dir=proc_dir))
    return sessions


