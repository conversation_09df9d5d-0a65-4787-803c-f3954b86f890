
import matplotlib.pyplot as plt
import torch
import numpy as np

def plot_stas(sta, row_labels:list = None, col_labels:list = None, share_scale=False, ax=None):
    """
    Plots STAs across lags.

    Parameters
    ----------
    sta : np.ndarray
        STA with shape (n_rows, n_lags, n_channels, n_y, n_x) or (n_lags, n_channels, n_y, n_x)
    """
    if isinstance(sta, torch.Tensor):
        sta = sta.detach().cpu().numpy()

    if sta.ndim == 4:
        sta = sta[np.newaxis, ...]

    n_rows, n_lags, n_c, n_y, n_x= sta.shape
    if row_labels is not None:
        assert len(row_labels) == n_rows, 'Number of row labels must match number of rows in sta'
    
    scale = 1 / (np.max(np.abs(sta)) * 2)
    aspect = n_x / n_y
    imshow_kwargs = dict(aspect='equal')
    if n_c == 1:
        # imshow_kwargs['cmap'] = 'gray'
        imshow_kwargs['cmap'] = 'coolwarm'
        imshow_kwargs['vmin'] = 0
        imshow_kwargs['vmax'] = 1

    # Plot sta
    if ax is None:
        fig = plt.figure(figsize=(n_lags*aspect, n_rows))
        ax = fig.subplots(1, 1)
    else:
        fig = ax.figure
    for iR in range(n_rows):
        if not share_scale:
            scale = 1 / (np.max(np.abs(sta[iR])) * 2)

        for iL in range(n_lags):
            x0, x1 = iL*aspect, (iL+1)*aspect
            y0, y1 = -iR-1, -iR
            ax.imshow(sta[iR,iL].transpose(1,2,0) * scale + .5, 
                       extent=[x0, x1, y0, y1], 
                       **imshow_kwargs)
            ax.plot([x0, x1, x1, x0, x0], [y1, y1, y0, y0, y1], 'k-')
        
        ax.set_ylim([-n_rows-.1, .1])
        ax.set_xlim([-.1, n_lags*aspect+.1])
        # turn off 
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)
        ax.set_aspect('equal')

        ax.set_yticks([-iR-.5 for iR in range(n_rows)])
        if row_labels is None: 
            row_labels = [f'{iR}' for iR in range(n_rows)]
        ax.set_yticklabels(row_labels)
        
        ax.set_xticks([(iL+.5)*aspect for iL in range(n_lags)])
        if col_labels is None:
            col_labels = [f'{iL}' for iL in range(n_lags)]
        ax.set_xticklabels(col_labels)
    return fig, ax


def plot_waveforms(wfs, chmap, cids):
    '''plot_waveforms plots the waveforms of the specified cell ids
    
    Parameters
    ----------
    wfs : dict
        Dictionary with the waveforms (output from loading the qc file)
        wf['waveforms'] : ndarray
            Array with the waveforms [nunits x ntimesample x nchannels]
    chmap : ndarray
        Array with the channel map
    cids : list or ndarray
    
    '''
    plt.figure(figsize=(8, 4))
    # set the figure background to gray
    plt.gca().set_facecolor('gray')
    offset = chmap[:,1] # np.max(chmap[:,1])*(chmap[:,0]==200) + 

    NT = wfs['waveforms'].shape[1]
    time = (chmap[:,0][:,None]/200*(NT+1) + np.arange(NT)).T
    cmap = plt.get_cmap('hsv', wfs['waveforms'].shape[0])
    for cc in cids: #range(wfs['waveforms'].shape[0]):
        plt.plot(time+cc*5*NT, (offset[:,None]+wfs['waveforms'][cc, :, :].T).T, '-', color=cmap(cc)) 