"""
Data filtering utilities for neural data analysis.

This module provides a registry-based system for data filters that can be
applied to neural datasets. Filters output boolean masks and can be chained
together in pipelines where they are combined with AND operations.

The module supports:
- Registry-based filter functions
- Pipeline composition of multiple filters with AND combination
- Configurable filters with parameters
- Broadcasting support for combining different shaped masks
"""

import torch
import numpy as np
from typing import Callable, Dict, List, Any
from .datasets import DictDataset

# ──────────────────────────────────────────────────────────────────────────────
# 1.  Datafilter registry
# ──────────────────────────────────────────────────────────────────────────────
class DataFilterFn(Callable[[DictDataset], torch.Tensor]): ...
DATAFILTER_REGISTRY: Dict[str, Callable[[Dict[str, Any]], DataFilterFn]] = {}

def _register(name):
    def wrap(fn):
        DATAFILTER_REGISTRY[name] = fn
        return fn
    return wrap

@_register("valid_nlags")
def _make_valid_nlags(cfg):
    """
    Create a datafilter that validates frames based on trial boundaries and temporal continuity.
    
    This is equivalent to the original get_valid_dfs function.
    
    Parameters
    ----------
    cfg : dict
        Configuration dictionary with 'n_lags' parameter
        
    Returns
    -------
    DataFilterFn
        Function that takes a dataset and returns boolean mask
    """
    n_lags = cfg if isinstance(cfg, int) else cfg.get("n_lags", 1)
    
    def valid_nlags(dset: DictDataset) -> torch.Tensor:
        """
        Generate a binary mask for valid data frames based on trial boundaries and DPI validity.
        
        This function creates a mask that identifies valid frames for analysis by:
        1. Identifying trial boundaries
        2. Excluding the first frame of each trial
        3. Ensuring DPI (eye tracking) data is valid
        4. Ensuring temporal continuity for the specified number of lags
        
        Parameters
        ----------
        dset : DictDataset
            Dataset containing trial indices and DPI validity information
            
        Returns
        -------
        torch.Tensor
            Binary mask tensor of shape [n_frames, 1] where 1 indicates valid frames
        """
        dpi_valid = dset['dpi_valid']
        new_trials = torch.diff(dset['trial_inds'], prepend=torch.tensor([-1])) != 0
        dfs = ~new_trials
        dfs &= (dpi_valid > 0)

        for _ in range(n_lags-1):
            dfs &= torch.roll(dfs, 1)

        # Convert to float for compatibility with original get_valid_dfs
        dfs = dfs.float()
        dfs = dfs[:, None]
        return dfs
    
    return valid_nlags

# ──────────────────────────────────────────────────────────────────────────────
# 2.  Build a composite datafilter pipeline
# ──────────────────────────────────────────────────────────────────────────────
def make_datafilter_pipeline(op_list: List[Dict[str, Any]]) -> DataFilterFn:
    """
    Build a composite datafilter pipeline that combines multiple filters with AND operations.
    
    Parameters
    ----------
    op_list : List[Dict[str, Any]]
        List of operation dictionaries, each containing a single key-value pair
        where the key is the filter name and value is the configuration
        
    Returns
    -------
    DataFilterFn
        Function that takes a dataset and returns combined boolean mask
    """
    fns: List[DataFilterFn] = []
    for op_dict in op_list:
        name, cfg = next(iter(op_dict.items()))
        if name not in DATAFILTER_REGISTRY:
            raise ValueError(f"Unknown datafilter '{name}'")
        fns.append(DATAFILTER_REGISTRY[name](cfg))

    def pipeline(dset: DictDataset) -> torch.Tensor:
        if not fns:
            # If no filters specified, return all True mask
            n_frames = len(dset)
            return torch.ones((n_frames, 1), dtype=torch.bool)
        
        # Apply first filter
        result = fns[0](dset)
        
        # Combine subsequent filters with AND operation
        for fn in fns[1:]:
            mask = fn(dset)
            # Broadcasting will handle different shapes (Tx1 with TxN)
            result = result & mask
            
        return result
    
    return pipeline
