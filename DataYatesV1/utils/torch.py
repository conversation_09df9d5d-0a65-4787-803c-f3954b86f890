import torch
import numpy as np
import os

def get_free_device(device=None, usage_threshold=0.1):
    if torch.cuda.is_available():
        # get memory usage for each device
        memory_usage_percent = [torch.cuda.memory_allocated(i) / torch.cuda.get_device_properties(i).total_memory for i in range(torch.cuda.device_count())]

        # get device with most free memory
        if device is None: 
            free_device = np.argmin(memory_usage_percent)
            if memory_usage_percent[free_device] > usage_threshold:
                print(f'Warning: No device with less than {usage_threshold*100:g}% memory usage found. All GPUs may be in use.')
        else:
            free_device = int(device)
            if memory_usage_percent[free_device] > usage_threshold:
                print(f'Warning: Device {free_device} has {memory_usage_percent[free_device]*100:.2f}% memory usage. This may cause the job to OOM.')
        print(f'Using device {free_device} with {memory_usage_percent[free_device]*100:.2f}% memory usage')
        return torch.device(f'cuda:{free_device}')
    else:
        print('Using CPU')
        return torch.device('cpu')

def get_memory_footprint(t):
    '''
    Get the memory footprint of a tensor, sparse_coo_tensor, or numpy array
    '''
    if isinstance(t, np.ndarray):
        return t.nbytes
    if isinstance(t, torch.Tensor):
        return t.element_size() * t.nelement()
    if isinstance(t, torch.sparse_coo_tensor):
        return t.element_size() * t.values().nelement() + t.element_size() * t.indices().nelement()
    
    return 0

def get_memory_footprints_str(t):
    '''
    Get the memory footprint of a tensor, sparse_coo_tensor, or numpy array as a string with human readable units
    '''
    size = get_memory_footprint(t)
    suffixes = ['B', 'KB', 'MB', 'GB', 'TB']
    i = 0
    while size > 1024 and i < len(suffixes):
        size = size / 1024
        i += 1
    return f'{size:.2f} {suffixes[i]}'

def set_seeds(seed:int):
    '''
    Set seeds for reproducibility
    '''
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_batch(batch, space=0):
    '''
    Recurisvely prints the shape and dtype of each tensor in a batch. A batch can be a dictionary, list, or tuple.
    '''
    if isinstance(batch, dict):
        for key, value in batch.items():
            if isinstance(value, np.ndarray):
                print(' '*space + f'{key} ({get_memory_footprints_str(value)}): {value.shape} -> {value.dtype}')
            elif isinstance(value, torch.Tensor):
                print(' '*space + f'{key} ({value.device} {get_memory_footprints_str(value)}): {value.shape} -> {value.dtype}')
            elif isinstance(value, (dict, tuple)):
                print(' '*space + f'{key}:')
                print_batch(value, space=space+2)
            else:
                print(' '*space + f'{key}: {value}')
    elif isinstance(batch, tuple) or isinstance(batch, list):
        for i, value in enumerate(batch):
            print(f'{i}: {value.shape}')
    else:
        print(batch)


