#!/usr/bin/env python3
"""
Demonstration of DynamicGaussianReadoutEI usage.

This script shows how to use the new excitatory-inhibitory readout class
and demonstrates its key features.
"""
#%%
import torch
import torch.nn.functional as F
import sys

# Add the DataYatesV1 directory to the path
sys.path.append('DataYatesV1')

from DataYatesV1.models.modules.readout import DynamicGaussianReadoutEI

def main():
    print("DynamicGaussianReadoutEI Demonstration")
    print("=" * 50)
    
    # Create the EI readout
    readout = DynamicGaussianReadoutEI(
        in_channels=32,
        n_units=5,
        bias=True,
        initial_std_ex=1.5,    # Excitatory Gaussians are more focused
        initial_std_inh=3.0,   # Inhibitory Gaussians are more spread out
        weight_constraint_fn=torch.relu  # Default: ReLU constraint for positive weights
    )
    
    print(f"Created EI readout:")
    print(f"  - Input channels: {readout.in_channels}")
    print(f"  - Output units: {readout.n_units}")
    print(f"  - Excitatory std: {readout.std_ex.mean().item():.2f}")
    print(f"  - Inhibitory std: {readout.std_inh.mean().item():.2f}")
    print(f"  - Weight constraint: {readout.weight_constraint_fn.__name__}")
    
    # Create sample input
    batch_size = 3
    H, W = 20, 20
    x = torch.randn(batch_size, 32, H, W)
    
    print(f"\nInput shape: {x.shape}")
    
    # Forward pass
    output = readout(x)
    print(f"Output shape: {output.shape}")
    print(f"Output values: {output[0].detach().numpy()}")
    
    # Demonstrate positive weight constraints
    print(f"\nWeight constraints:")
    print(f"  - Excitatory weights min: {readout.features_ex_weight.min().item():.6f}")
    print(f"  - Inhibitory weights min: {readout.features_inh_weight.min().item():.6f}")
    print(f"  - All weights are non-negative: {(readout.features_ex_weight >= 0).all() and (readout.features_inh_weight >= 0).all()}")
    
    # Show spatial weight information
    spatial_weights = readout.get_spatial_weights()
    ex_spatial = spatial_weights['excitatory']
    inh_spatial = spatial_weights['inhibitory']
    
    print(f"\nSpatial weights:")
    print(f"  - Excitatory shape: {ex_spatial.shape}")
    print(f"  - Inhibitory shape: {inh_spatial.shape}")
    print(f"  - Excitatory peak: {ex_spatial.max().item():.6f}")
    print(f"  - Inhibitory peak: {inh_spatial.max().item():.6f}")
    print(f"  - Excitatory is more focused: {ex_spatial.max() > inh_spatial.max()}")
    
    # Demonstrate different weight constraints
    print(f"\nTesting different weight constraints:")
    
    # Softplus constraint
    readout_softplus = DynamicGaussianReadoutEI(
        in_channels=32, n_units=3, weight_constraint_fn=F.softplus
    )
    
    # Square constraint  
    readout_square = DynamicGaussianReadoutEI(
        in_channels=32, n_units=3, weight_constraint_fn=lambda x: x**2
    )
    
    # Set negative hidden weights to test constraints
    with torch.no_grad():
        for readout_test in [readout_softplus, readout_square]:
            readout_test._features_ex_weight.data.fill_(-1.0)
    
    print(f"  - ReLU(-1.0) = {torch.relu(torch.tensor(-1.0)).item():.6f}")
    print(f"  - Softplus(-1.0) = {F.softplus(torch.tensor(-1.0)).item():.6f}")
    print(f"  - Square(-1.0) = {(torch.tensor(-1.0)**2).item():.6f}")
    
    # Test 5D input (with sequence dimension)
    print(f"\nTesting 5D input (with sequence dimension):")
    x_5d = torch.randn(2, 32, 10, H, W)  # (batch, channels, sequence, height, width)
    output_5d = readout(x_5d)
    print(f"  - 5D input shape: {x_5d.shape}")
    print(f"  - Output shape: {output_5d.shape}")
    print(f"  - Uses last timestep automatically")
    
    # Demonstrate plotting (if matplotlib is available)
    try:
        print(f"\nGenerating plots...")
        fig, axs = readout.plot_weights()
        if fig is not None:
            print("  - Feature weights plot: Shows red (excitatory) and blue (inhibitory) overlaid")
            print("  - Spatial weights plot: Shows Gaussian masks with red/blue overlay")
            print("  - Zero weights are transparent due to positive constraints")
            print("  - Plots saved as PNG files")
        else:
            print("  - Plotting failed")
    except Exception as e:
        print(f"  - Plotting not available: {e}")
    
    print(f"\nKey features of DynamicGaussianReadoutEI:")
    print(f"  ✓ Separate excitatory and inhibitory pathways")
    print(f"  ✓ Positive-constrained feature weights with configurable activation")
    print(f"  ✓ Separate Gaussian parameters for each pathway")
    print(f"  ✓ Output = (excitatory_space * excitatory_features) - (inhibitory_space * inhibitory_features)")
    print(f"  ✓ Custom plotting with red/blue overlay and alpha blending")
    print(f"  ✓ Compatible with 4D and 5D inputs")
    print(f"  ✓ Inherits from BaseFactorizedReadout for consistency")
    return readout

if __name__ == "__main__":
    readout = main()

#%%