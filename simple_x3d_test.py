#!/usr/bin/env python3
"""
Simple test for X3DNet dropout functionality.
"""

import torch
from DataYatesV1.models.config_builders import build_x3dnet_config
from DataYatesV1.models.modules.convnet import X3DNet

def test_basic_x3dnet():
    """Test basic X3DNet functionality."""
    print("Testing basic X3DNet...")
    
    config = {
        'model_type': 'x3d',
        'dim': 3,
        'initial_channels': 6,
        'channels': [32],
        'depth': [1],
        't_kernel': 3,
        's_kernel': 3,
        'exp_ratio': 2,
        'norm_type': 'grn',
        'act_type': 'silu',
        'stride_stages': [1],
        'dropout': 0.0,
        'stochastic_depth': 0.0,
        'checkpointing': False
    }
    
    model = X3DNet(config)
    print(f"Model created successfully")
    
    x = torch.randn(1, 6, 4, 16, 16)
    print(f"Input shape: {x.shape}")
    
    # Test forward pass
    model.eval()
    y = model(x)
    print(f"Output shape: {y.shape}")
    print("✓ Basic X3DNet works!")

def test_dropout_config():
    """Test dropout configuration."""
    print("\nTesting dropout configuration...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[32],
        dropout=0.2,
        stochastic_depth=0.1
    )
    
    print(f"Dropout in config: {config.get('dropout', 'NOT FOUND')}")
    print(f"Stochastic depth in config: {config.get('stochastic_depth', 'NOT FOUND')}")
    
    model = X3DNet(config)
    print("✓ X3DNet with dropout config created successfully!")

def test_training_vs_eval():
    """Test difference between training and eval modes."""
    print("\nTesting training vs eval modes...")
    
    config = build_x3dnet_config(
        initial_channels=6,
        channels=[32],
        dropout=0.3,  # High dropout to see effect
        stochastic_depth=0.0  # Disable stochastic depth for this test
    )
    
    model = X3DNet(config)
    x = torch.randn(1, 6, 4, 16, 16)
    
    # Training mode
    model.train()
    y1_train = model(x)
    y2_train = model(x)
    train_diff = (y1_train - y2_train).abs().max().item()
    print(f"Training mode max diff: {train_diff:.6f}")
    
    # Eval mode
    model.eval()
    y1_eval = model(x)
    y2_eval = model(x)
    eval_diff = (y1_eval - y2_eval).abs().max().item()
    print(f"Eval mode max diff: {eval_diff:.6f}")
    
    print("✓ Training/eval mode test completed!")

if __name__ == "__main__":
    test_basic_x3dnet()
    test_dropout_config()
    test_training_vs_eval()
    print("\n🎉 Simple X3DNet tests completed!")
