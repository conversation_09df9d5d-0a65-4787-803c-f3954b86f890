#%%#!/usr/bin/env python

import yaml
from pathlib import Path
from tqdm import tqdm

import torch
import numpy as np
import matplotlib.pyplot as plt

from DataYatesV1 import (
    DictDataset, enable_autoreload, get_gaborium_sta_ste, get_session, ensure_tensor,
    get_complete_sessions, print_batch, set_seeds, calc_sta
)

from scipy.ndimage import gaussian_filter

set_seeds(1002)

# Enable autoreload for interactive development
enable_autoreload()
device = 'cuda:1'

base_config_path = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/data_configs/multi_dataset_temporal_basis/multi_data_stimembed_base.yaml")
with open(base_config_path, 'r') as f:
    base_config = yaml.safe_load(f)

#%% Utility Functions
def get_quality_metrics(sess, n_lags=24):
    
    # -------------------------------------------------------------------------------------------
    # Visual Responsiveness Metric
    # -------------------------------------------------------------------------------------------
    sta_data, ste_data = get_gaborium_sta_ste(sess, n_lags)

    signal = np.abs(ste_data - np.median(ste_data, axis=(2,3), keepdims=True))
    sigma = [0, 2, 2, 2]
    signal = gaussian_filter(signal, sigma)
    noise = np.median(signal[:,0], axis=(1,2))
    snr_per_lag = np.max(signal, axis=(2,3)) / noise[:,None]
    cluster_lag = snr_per_lag.argmax(axis=1)

    from DataYatesV1 import plot_stas
    plot_stas(signal[:,:,None,:,:])

    visual_snr = snr_per_lag.max(axis=1)

    # -------------------------------------------------------------------------------------------
    # Spike sorting quality metrics
    # -------------------------------------------------------------------------------------------


    # extract missing % and contamination %
    spike_clusters = sess.ks_results.spike_clusters
    spike_amplitudes = sess.ks_results.spike_amplitudes
    cids = np.unique(spike_clusters)

    refractory = np.load(sess.sess_dir / 'qc' / 'refractory' / 'refractory.npz')
    refractory_periods = refractory['refractory_periods']
    min_contam_proportions = refractory['min_contam_props']
    contam_pct = np.array([np.min(min_contam_proportions[iU])*100 for iU in range(len(cids))])

    truncation = np.load(sess.sess_dir / 'qc' / 'amp_truncation' / 'truncation.npz')
    med_missing_pct = np.array([np.median(truncation['mpcts'][truncation['cid']==iU]) for iU in range(len(cids))])

    return visual_snr, med_missing_pct, contam_pct

def process_session_config(sess, base_config, output_dir, snr_thresh, missing_thresh, contam_thresh, n_lags=24):
    """
    Process a single session to create a session-specific config file

    Parameters
    ----------
    sess : YatesV1Session
        The session object
    base_config : dict
        The base configuration dictionary
    output_dir : Path
        Directory to save the new config file
    snr_thresh : float
        SNR threshold for visual responsiveness
    missing_thresh : float
        Missing spikes threshold
    contam_thresh : float
        Contamination threshold
    n_lags : int
        Number of lags for STA/STE computation

    Returns
    -------
    dict
        Dictionary with session statistics
    """
    print(f"Processing session: {sess.name}")

    # Get quality metrics for this session
    visual_snr, med_missing_pct, contam_pct = get_quality_metrics(sess, n_lags=n_lags)

    # Calculate unit lists
    visually_responsive = np.where(visual_snr > snr_thresh)[0]
    not_missing_spikes = np.where(med_missing_pct < missing_thresh)[0]
    not_contaminated = np.where(contam_pct < contam_thresh)[0]
    good_units = np.intersect1d(visually_responsive, not_contaminated)

    # Create new config by copying base config
    session_config = base_config.copy()

    # Add session-specific fields
    session_config['cids'] = good_units.tolist()
    session_config['session'] = sess.name
    session_config['visual'] = visually_responsive.tolist()
    session_config['qcmissing'] = not_missing_spikes.tolist()
    session_config['qccontam'] = not_contaminated.tolist()
    session_config['snr'] = snr_thresh
    session_config['missingth'] = missing_thresh
    session_config['contamth'] = contam_thresh

    # Save the session config with custom formatting for lists
    output_file = output_dir / f"{sess.name}.yaml"

    # Custom representer to format lists in flow style (inline)
    def represent_list(dumper, data):
        return dumper.represent_sequence('tag:yaml.org,2002:seq', data, flow_style=True)

    yaml.add_representer(list, represent_list)

    with open(output_file, 'w') as f:
        yaml.dump(session_config, f, default_flow_style=False, sort_keys=False)

    # Return statistics
    stats = {
        'session': sess.name,
        'total_units': len(visual_snr),
        'visually_responsive': len(visually_responsive),
        'not_missing': len(not_missing_spikes),
        'not_contaminated': len(not_contaminated),
        'good_units': len(good_units)
    }

    print(f"  {stats['visually_responsive']}/{stats['total_units']} visually responsive")
    print(f"  {stats['not_missing']}/{stats['total_units']} pass missing threshold")
    print(f"  {stats['not_contaminated']}/{stats['total_units']} pass contamination threshold")
    print(f"  {stats['good_units']}/{stats['total_units']} good units (visual + contamination)")
    print(f"  Saved: {output_file}")

    return stats

# %%
# Main processing loop
snr_threshold = 5
missing_threshold = 25
contamination_threshold = 50

sessions = get_complete_sessions()
output_dir = base_config_path.parent

print(f"Found {len(sessions)} complete sessions")
print(f"Output directory: {output_dir}")
print(f"Thresholds - SNR: {snr_threshold}, Missing: {missing_threshold}%, Contamination: {contamination_threshold}%")
print()

#%%
# Process all sessions
all_stats = []
for sess in tqdm(sessions, desc="Processing sessions"):
    try:
        stats = process_session_config(
            sess, base_config, output_dir,
            snr_threshold, missing_threshold, contamination_threshold
        )
        all_stats.append(stats)
    except Exception as e:
        print(f"Error processing {sess.name}: {e}")
        continue
    print()

# %%
# Summary statistics
print("="*60)
print("SUMMARY")
print("="*60)

if all_stats:
    total_sessions = len(all_stats)
    total_units = sum(s['total_units'] for s in all_stats)
    total_visual = sum(s['visually_responsive'] for s in all_stats)
    total_good = sum(s['good_units'] for s in all_stats)

    print(f"Successfully processed: {total_sessions} sessions")
    print(f"Total units across all sessions: {total_units}")
    print(f"Total visually responsive units: {total_visual} ({100*total_visual/total_units:.1f}%)")
    print(f"Total good units: {total_good} ({100*total_good/total_units:.1f}%)")
    print()

    # Per-session breakdown
    print("Per-session breakdown:")
    for stats in all_stats:
        pct_good = 100 * stats['good_units'] / stats['total_units'] if stats['total_units'] > 0 else 0
        print(f"  {stats['session']}: {stats['good_units']}/{stats['total_units']} good units ({pct_good:.1f}%)")

# %%

