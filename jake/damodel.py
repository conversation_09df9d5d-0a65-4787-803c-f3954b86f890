import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.checkpoint import checkpoint
from torch.nn.utils.parametrizations import weight_norm
import matplotlib.pyplot as plt
import numpy as np
import warnings # For warnings

inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta
ensure_tensor = lambda x, device: torch.tensor(x, dtype=torch.float32, device=device) if not isinstance(x, torch.Tensor) else x.to(device=device, dtype=torch.float32)

# --- Standardized Input Tensor Format Convention ---
# Throughout this refactored code, we will assume the primary tensor format for spatio-temporal data is:
# (Batch, Channels, Sequence/Time, Height, Width) -> (N, C, S, H, W)
# Docstrings and comments will reflect this.

# --- Modular V1Model Architecture ---
class ModularV1Model(nn.Module):
    """
    A modular V1 model architecture that allows easy swapping of components.
    Processes inputs of shape: (N, C, S, H, W)

    Components:
    1. Frontend: Processes raw input (DA, TemporalConv, etc.)
    2. ConvNet: Feature extraction (DenseNet, CNN, etc.)
    3. Recurrent: Temporal processing (ConvLSTM, ConvGRU, etc.)
    4. Modulator: Optional behavioral modulation
    5. Readout: Output layer (DynamicGaussian, Linear, etc.)
    """
    def __init__(self,
                 # Input dimensions (optional, can be inferred or used for assertions)
                 height=None,
                 width=None,
                 sampling_rate=240,
                 
                 # Component selection
                 frontend_type='da',        # 'da', 'temporal_conv', 'none'
                 convnet_type='densenet',   # 'densenet', 'cnn', 'none'
                 recurrent_type='convgru', # 'convlstm', 'convgru', 'modulated_convlstm', 'modulated_convgru', 'convsru', 'attention', 'none'
                 modulator_type='none',     # 'lstm', 'linear', 'none'
                 readout_type='gaussian',   # 'gaussian', 'linear'
                 
                 # Component parameters (passed as dicts)
                 frontend_params=None,
                 convnet_params=None,
                 recurrent_params=None,
                 modulator_params=None,
                 readout_params=None,
                 # General model params
                 output_activation=nn.Softplus(), # Identity if using canonincal link
                 init_rates=None, # If provided, initializes readout bias to match these firing rates
                 initial_input_channels=1): # Default to 1 for grayscale video

        super().__init__()
        
        self.height = height
        self.width = width
        self.sampling_rate = sampling_rate
        self.initial_input_channels = initial_input_channels
        
        # Initialize component parameters with defaults if not provided
        frontend_params = frontend_params or {}
        convnet_params = convnet_params or {}
        recurrent_params = recurrent_params or {}
        modulator_params = modulator_params or {}
        readout_params = readout_params or {}
        
        # Track channel dimensions between components
        current_channels = self.initial_input_channels
        
        # --- 1. Frontend ---
        self.frontend, frontend_out_channels = self._build_frontend(
            frontend_type, 
            frontend_params,
            input_channels=current_channels # Pass initial channels to frontend
        )
        current_channels = frontend_out_channels
        
        # --- 2. ConvNet ---
        self.convnet, convnet_out_channels = self._build_convnet(
            convnet_type, 
            convnet_params,
            input_channels=current_channels
        )
        current_channels = convnet_out_channels
        
        # --- 3. Recurrent ---
        # Modulator needs to be built first to know its output dimension for recurrent layer
        self.modulator, modulator_output_dim = self._build_modulator(
            modulator_type, 
            modulator_params
        )

        self.recurrent, recurrent_out_channels = self._build_recurrent(
            recurrent_type, 
            recurrent_params,
            input_channels=current_channels,
            modulator_dim=modulator_output_dim # Pass modulator_dim here
        )
        current_channels = recurrent_out_channels
        
        # --- 4. Readout ---
        # Note: Modulator is built before recurrent, so it's not step 4 in sequence here.
        self.readout = self._build_readout(
            readout_type, 
            readout_params,
            input_channels=current_channels
        )

        self.activation = output_activation

        # 6. Readout Bias Initialization
        if init_rates is not None and self.readout.bias is not None:
            print("Initializing readout bias.")
            assert len(init_rates) == self.readout.n_units, 'init_rates must have the same length as n_units'
            # Use a small epsilon to prevent log(0) if init_rates are zero
            init_rates_tensor = ensure_tensor(init_rates, device=self.readout.bias.device) + 1e-7
            with torch.no_grad():
                self.readout.bias.data = inv_softplus(init_rates_tensor)
        elif init_rates is not None:
            print("Warning: init_rates provided but readout has no bias.")

    def _build_frontend(self, frontend_type, params, input_channels):
        """Build the frontend component."""
        if frontend_type == 'none':
            return nn.Identity(), input_channels # Pass through input_channels
        
        elif frontend_type == 'da':
            da_defaults = {
                'alpha': 1.0, 'beta': 0.00008, 'gamma': 0.5,
                'tau_y_ms': 5.0, 'tau_z_ms': 60.0, 'n_y': 5.0, 'n_z': 2.0,
                'filter_length': 200, 'learnable_params': False
            }
            da_params = {**da_defaults, **params}
            
            frontend = DAModel(
                sampling_rate=self.sampling_rate,
                height=self.height, 
                width=self.width,
                **da_params
            )
            return frontend, 1 # DAModel outputs 1 channel
        
        elif frontend_type == 'temporal_conv':
            temp_conv_defaults = {
                'out_channels': 3, # This is the ConvBlock's out_channels before SplitReLU
                'kernel_size': (15, 3, 3), 'stride': 1, 'use_layernorm': True
            }
            temp_conv_params = {**temp_conv_defaults, **params}
            
            frontend = ConvBlock(
                in_channels=input_channels, # Use passed input_channels
                **temp_conv_params
            )
            # ConvBlock uses SplitRelu, which doubles the out_channels
            return frontend, temp_conv_params['out_channels'] * 2 
        
        else:
            raise ValueError(f"Unknown frontend type: {frontend_type}")

    def _build_convnet(self, convnet_type, params, input_channels):
        """Build the convnet component."""
        if convnet_type == 'none':
            return nn.Identity(), input_channels
        
        elif convnet_type == 'densenet':
            densenet_defaults = {
                'growth_rate': 4, 'num_blocks': 4, 'kernel_size': (3, 3, 3),
                'use_layernorm': True, 'use_checkpointing': True
            }
            densenet_params = {**densenet_defaults, **params}
            
            convnet = DenseNet(
                initial_channels=input_channels,
                **densenet_params
            )
            # DenseNet output channels are initial + sum of (growth_rate * 2) for each block
            out_channels = input_channels + (densenet_params['num_blocks'] * densenet_params['growth_rate'] * 2)
            return convnet, out_channels
        
        elif convnet_type == 'cnn':
            cnn_defaults = {
                'out_channels': 32, 'kernel_size': (3, 3, 3), 'stride': 1,
                'use_layernorm': True
            }
            cnn_params = {**cnn_defaults, **params}
            
            convnet = ConvBlock(
                in_channels=input_channels,
                **cnn_params
            )
            return convnet, cnn_params['out_channels'] * 2 # *2 for SplitRelu
        
        else:
            raise ValueError(f"Unknown convnet type: {convnet_type}")

    def _build_recurrent(self, recurrent_type, params, input_channels, modulator_dim):
        """Build the recurrent component."""
        if recurrent_type == 'none':
            return nn.Identity(), input_channels
        
        recurrent_defaults = {
            'hidden_dim': 64, 'kernel_size': 3, 'dropout': 0.5, 
            'bias': True, 'start_time': 10, 'return_sequences': False
        }
        # For ConvLSTM specific forget_bias_init
        if 'convlstm' in recurrent_type:
            recurrent_defaults['forget_bias_init'] = 1.0
        
        recurrent_params_merged = {**recurrent_defaults, **params}

        if recurrent_type == 'convlstm':
            recurrent = ConvLSTM(
                input_dim=input_channels,
                **recurrent_params_merged
            )
            return recurrent, recurrent_params_merged['hidden_dim']
        
        elif recurrent_type == 'convgru':
            recurrent = ConvGRU(
                input_dim=input_channels,
                **recurrent_params_merged
            )
            return recurrent, recurrent_params_merged['hidden_dim']

        elif recurrent_type == 'modulated_convlstm':
            recurrent = ModulatedConvLSTM(
                input_dim=input_channels,
                modulator_dim=modulator_dim,
                modulation_type=params.get('modulation_type', 'additive'), # Get from specific params or default
                **recurrent_params_merged
            )
            return recurrent, recurrent_params_merged['hidden_dim']

        elif recurrent_type == 'modulated_convgru':
            recurrent = ModulatedConvGRU(
                input_dim=input_channels,
                modulator_dim=modulator_dim,
                modulation_type=params.get('modulation_type', 'additive'), # Get from specific params or default
                **recurrent_params_merged
            )
            return recurrent, recurrent_params_merged['hidden_dim']
            
        elif recurrent_type == 'convsru':
            recurrent = ConvSRU(
                input_dim=input_channels,
                **recurrent_params_merged
            )
            return recurrent, recurrent_params_merged['hidden_dim']
        
        elif recurrent_type == 'attention':
            attention_defaults = {'num_heads': 4} # hidden_dim is already in recurrent_defaults
            attention_params_merged = {**attention_defaults, **recurrent_params_merged} 
            # Ensure 'hidden_dim' from recurrent_params_merged is used for TemporalAttention's hidden_dim
            
            recurrent = TemporalAttention(
                input_dim=input_channels,
                hidden_dim=attention_params_merged['hidden_dim'], # Explicitly pass hidden_dim
                num_heads=attention_params_merged['num_heads'],
                dropout=attention_params_merged['dropout'],
                bias=attention_params_merged['bias']
            )
            return recurrent, attention_params_merged['hidden_dim']
        
        else:
            raise ValueError(f"Unknown recurrent type: {recurrent_type}")

    def _build_modulator(self, modulator_type, params):
        """Build the modulator component."""
        if modulator_type == 'none':
            return None, 0 # No modulator, 0 output dimension
        
        elif modulator_type == 'lstm':
            modulator_defaults = {
                'n_vars': 2, 'K': 32, 'lstm_layers': 1, 'bidirectional': False
            }
            modulator_params_merged = {**modulator_defaults, **params}
            
            modulator = ModulatorLSTM(**modulator_params_merged)
            modulator_dim = modulator_params_merged['K'] * (2 if modulator_params_merged['bidirectional'] else 1)
            return modulator, modulator_dim
        
        elif modulator_type == 'linear':
            modulator_defaults = {'n_vars': 2, 'K': 32}
            modulator_params_merged = {**modulator_defaults, **params}
            
            modulator = nn.Linear(modulator_params_merged['n_vars'], modulator_params_merged['K'])
            return modulator, modulator_params_merged['K']
        
        else:
            raise ValueError(f"Unknown modulator type: {modulator_type}")

    def _build_readout(self, readout_type, params, input_channels):
        """Build the readout component."""
        if readout_type == 'gaussian':
            readout_defaults = {
                'n_units': 16, 'bias': True, 'initial_std': 5.0
            }
            readout_params_merged = {**readout_defaults, **params}
            
            return DynamicGaussianReadout(
                in_channels=input_channels,
                **readout_params_merged
            )
        
        elif readout_type == 'linear':
            readout_defaults = {'n_units': 16, 'bias': True}
            readout_params_merged = {**readout_defaults, **params}
            
            # For a linear readout on (N, C, H, W) or (N, C, S, H, W) from recurrent,
            # we need to flatten spatial/temporal dims if not already done.
            # Assuming recurrent layer outputs (N, C_out, H, W) if not return_sequences
            # or (N, C_out, S_out, H, W) if return_sequences.
            # DynamicGaussianReadout handles (N,C,H,W) and takes last time step if 5D.
            # A simple nn.Linear expects (N, *, InFeatures).
            # We'll add a small wrapper to handle flattening if needed.
            class FlattenedLinearReadout(nn.Module):
                def __init__(self, in_channels, n_units, bias):
                    super().__init__()
                    self.in_channels = in_channels
                    self.n_units = n_units
                    # The actual nn.Linear will be created in forward,
                    # once spatial dimensions are known, or use adaptive pooling.
                    # For simplicity here, let's use AdaptiveAvgPool2d to get fixed size.
                    self.adaptive_pool = nn.AdaptiveAvgPool2d((1,1)) # Output (N, C, 1, 1)
                    self.fc = nn.Linear(in_channels, n_units, bias=bias)

                def forward(self, x):
                    # x: (N, C, S, H, W) or (N, C, H, W)
                    if x.dim() == 5: # (N, C, S, H, W)
                        # Take last time step
                        x = x[:, :, -1, :, :] # -> (N, C, H, W)
                    # Now x is (N, C, H, W)
                    x = self.adaptive_pool(x) # -> (N, C, 1, 1)
                    x = torch.flatten(x, 1)    # -> (N, C)
                    return self.fc(x)

            return FlattenedLinearReadout(
                in_channels=input_channels,
                n_units=readout_params_merged['n_units'],
                bias=readout_params_merged['bias']
            )
        
        else:
            raise ValueError(f"Unknown readout type: {readout_type}")

    def forward(self, stimulus, behavior=None):
        """
        Forward pass through the model.
        
        Parameters:
        stimulus (Tensor): Visual stimulus. Expected (N, C_in, S, H, W) or (N, S, H, W).
                           If (N,S,H,W), channel dim will be added.
        behavior (Tensor, optional): Behavioral data for modulation (N, num_behavior_vars, S_behavior).
        
        Returns:
        Tensor: Model output, typically (N, n_readout_units).
        """
        # Ensure stimulus is (N, C, S, H, W)
        if stimulus.dim() == 4:  # (N, S, H, W)
            stimulus = stimulus.unsqueeze(1) # Add channel dim -> (N, 1, S, H, W)
        elif stimulus.dim() == 5: # (N, C, S, H, W)
            pass # Already in correct format
        else:
            raise ValueError(f"Stimulus must have 4 or 5 dimensions (N,S,H,W or N,C,S,H,W), got {stimulus.dim()}")

        # Frontend processing
        # DAModel expects (N,S,H,W) or (N,1,S,H,W) and outputs (N,1,S,H,W)
        # ConvBlock expects (N,C,S,H,W) and outputs (N,C_out,S_out,H,W)
        if isinstance(self.frontend, DAModel) and stimulus.shape[1] != 1:
             # If DAModel is used, it expects a single channel input.
             # If stimulus has more, this might be an issue or require specific handling.
             # For now, assuming stimulus channel matches DAModel expectation or initial_input_channels.
             pass
        x = self.frontend(stimulus)
        
        # ConvNet processing
        x = self.convnet(x)
        
        # Modulator processing (if enabled)
        modulator_signal_for_recurrent = None
        if self.modulator is not None and behavior is not None:
            # ModulatorLSTM expects (N, Lags, n_vars) but behavior is (N, n_vars, Lags)
            # So ModulatorLSTM handles the permute.
            modulator_signal_for_recurrent = self.modulator(behavior) 
        
        # Recurrent processing (with modulation if available)
        # Modulated recurrent layers accept 'modulator' kwarg
        if 'modulated' in self.recurrent.__class__.__name__.lower() : # Check if it's a modulated recurrent layer
            x = self.recurrent(x, modulator=modulator_signal_for_recurrent)
        else:
            x = self.recurrent(x)
        
        # Readout
        # DynamicGaussianReadout expects (N,C,H,W) or (N,C,S,H,W) [takes last time step from S]
        output = self.readout(x)

        # softplus
        output = self.activation(output)
        
        return output

# --- Dynamic adaptation model ---
class DAModel(nn.Module):
    """
    PyTorch implementation of the simplified Dynamical Adaptation (DA) model
    based on Clark et al., 2013, assuming tau_r ≈ 0 (Eq. 15).

    Processes inputs of shape: (N, S, H, W) or (N, 1, S, H, W)
    Outputs tensor of shape: (N, 1, S, H, W).
    Handles time constants based on provided sampling rate.
    """
    def __init__(self, sampling_rate=240,
                 height=None, width=None, 
                 alpha=1.0, beta=0.00008, gamma=0.5,
                 tau_y_ms=5.0, tau_z_ms=60.0,
                 n_y=5., n_z=2.,
                 filter_length=200,
                 learnable_params=False):
        super().__init__()

        self.height = height # Optional, for assertion
        self.width = width   # Optional, for assertion
        self.filter_length = filter_length
        self.sampling_rate = float(sampling_rate)
        self.dt = 1.0 / self.sampling_rate # Timestep in seconds
        self.learnable_params = learnable_params

        self.initial_params = {
            'alpha': alpha, 'beta': beta, 'gamma': gamma,
            'tau_y_ms': tau_y_ms, 'tau_z_ms': tau_z_ms,
            'n_y': n_y, 'n_z': n_z
        }

        self.alpha = nn.Parameter(torch.tensor(float(alpha)), requires_grad=learnable_params)
        self.beta = nn.Parameter(torch.tensor(float(beta)), requires_grad=learnable_params)
        self.gamma = nn.Parameter(torch.tensor(float(gamma)), requires_grad=learnable_params)
        self.tau_y_ms = nn.Parameter(torch.tensor(float(tau_y_ms)), requires_grad=learnable_params)
        self.n_y = nn.Parameter(torch.tensor(float(n_y)), requires_grad=learnable_params)
        self.tau_z_ms = nn.Parameter(torch.tensor(float(tau_z_ms)), requires_grad=learnable_params)
        self.n_z = nn.Parameter(torch.tensor(float(n_z)), requires_grad=learnable_params)

        self.padding = (self.filter_length - 1, 0)

        if not learnable_params:
            Ky_filter, Kz_filter = self._calculate_filters(
                device=torch.device('cpu'), dtype=torch.float32 # Precompute on CPU
            )
            self.register_buffer('Ky_filter', Ky_filter)
            self.register_buffer('Kz_filter', Kz_filter)

    def _calculate_filters(self, device, dtype):
        """ Helper to calculate filters based on current parameters. """
        # Ensure parameters are on the correct device for calculation
        gamma = self.gamma.to(device=device, dtype=dtype)
        tau_y_ms = self.tau_y_ms.to(device=device, dtype=dtype)
        n_y = self.n_y.to(device=device, dtype=dtype)
        tau_z_ms = self.tau_z_ms.to(device=device, dtype=dtype)
        n_z = self.n_z.to(device=device, dtype=dtype)

        tau_y_ms_clamped = torch.clamp(tau_y_ms, min=1e-2)
        tau_z_ms_clamped = torch.clamp(tau_z_ms, min=1e-2)
        n_y_clamped = torch.clamp(n_y, min=0.5) # Original had .5
        n_z_clamped = torch.clamp(n_z, min=0.1) # Original had .1
        gamma_clamped = torch.clamp(gamma, 0.0, 1.0)

        tau_y_sec = tau_y_ms_clamped / 1000.0
        tau_z_sec = tau_z_ms_clamped / 1000.0
        dt_tensor = torch.tensor(self.dt, device=device, dtype=dtype)
        tau_y_steps = torch.clamp(tau_y_sec / dt_tensor, min=0.1)
        tau_z_steps = torch.clamp(tau_z_sec / dt_tensor, min=0.1)

        t = torch.arange(0.0, float(self.filter_length), device=device, dtype=dtype)
        
        # Using torch.lgamma for potentially better stability with n + 1
        lgamma_n_y_plus_1 = torch.lgamma(n_y_clamped + 1.0 + 1e-9) # Small epsilon
        lgamma_n_z_plus_1 = torch.lgamma(n_z_clamped + 1.0 + 1e-9) # Small epsilon

        # Normalization factors
        norm_Ky = (tau_y_steps**(n_y_clamped + 1.0)) * torch.exp(lgamma_n_y_plus_1)
        norm_Kz_slow = (tau_z_steps**(n_z_clamped + 1.0)) * torch.exp(lgamma_n_z_plus_1)
        
        norm_Ky = torch.clamp(norm_Ky, min=1e-9)
        norm_Kz_slow = torch.clamp(norm_Kz_slow, min=1e-9)

        Ky = torch.pow(t, n_y_clamped) * torch.exp(-t / tau_y_steps)
        Kz_slow = torch.pow(t, n_z_clamped) * torch.exp(-t / tau_z_steps)

        Ky = Ky / norm_Ky
        Kz_slow = Kz_slow / norm_Kz_slow
        Kz = gamma_clamped * Ky + (1.0 - gamma_clamped) * Kz_slow

        Ky_filter = Ky.flip(0).unsqueeze(0).unsqueeze(0) # (1, 1, filter_length)
        Kz_filter = Kz.flip(0).unsqueeze(0).unsqueeze(0) # (1, 1, filter_length)
        return Ky_filter, Kz_filter

    def forward(self, s):
        """
        Applies the simplified DA model.
        Args:
            s (torch.Tensor): Input (N, 1, S, H, W) or (N, S, H, W). S is sequence/frames.
        Returns:
            torch.Tensor: Output (N, 1, S, H, W)
        """
        original_shape = s.shape
        if s.dim() == 4:  # (N, S, H, W)
            s = s.unsqueeze(1) # -> (N, 1, S, H, W)
        
        batch_size, channels, num_frames, height, width = s.shape
        if channels != 1:
            raise ValueError(f"DAModel expects input with 1 channel, got {channels} channels. Input shape: {original_shape}")

        if self.height is not None and (height != self.height or width != self.width):
            warnings.warn(f"Input spatial dimensions {height}x{width} don't match DAModel's expected {self.height}x{self.width}")

        input_device = s.device
        input_dtype = s.dtype

        if self.learnable_params:
            Ky_filter, Kz_filter = self._calculate_filters(device=input_device, dtype=input_dtype)
        else:
            Ky_filter = self.Ky_filter.to(device=input_device, dtype=input_dtype)
            Kz_filter = self.Kz_filter.to(device=input_device, dtype=input_dtype)
        
        # Reshape for 1D convolution: (N*H*W, 1, S)
        s_reshaped = s.permute(0, 3, 4, 1, 2).reshape(batch_size * height * width, 1, num_frames)

        # Pad for causal convolution. Using value=0.0 for simplicity and gradient friendliness.
        # Original used: pad_value = s_reshaped[:, :, 0].mean().item() which detaches from graph.
        s_padded = F.pad(s_reshaped, self.padding, mode='constant', value=0.0) 

        y = F.conv1d(s_padded, Ky_filter)
        z = F.conv1d(s_padded, Kz_filter)
        
        # REMOVED: del s_reshaped, s_padded and torch.cuda.empty_cache()
        # These are not standard practice in forward methods and can hurt performance.

        alpha_eff = self.alpha.to(device=input_device, dtype=input_dtype)
        beta_eff = F.relu(self.beta.to(device=input_device, dtype=input_dtype)) # Ensure beta is non-negative

        denominator = 1.0 + beta_eff * z
        safe_denominator = torch.clamp(denominator, min=1e-6) # Avoid division by zero
        r = alpha_eff * y / safe_denominator
        
        # Reshape Output: (N*H*W, 1, S) -> (N, H, W, 1, S) -> (N, 1, S, H, W)
        r_reshaped = r.reshape(batch_size, height, width, 1, num_frames).permute(0, 3, 4, 1, 2)
        return r_reshaped

# --- Split Relu Activation ---
class SplitRelu(nn.Module):
    def __init__(self, split_dim=1, trainable_gain=False): # Changed default split to split_dim
        """SplitRelu activation. Output channels are doubled along split_dim."""
        super().__init__()
        self.split_dim = split_dim
        self.ongain = nn.Parameter(torch.ones(1), requires_grad=trainable_gain)

    def forward(self, x):
        # Output channels are doubled along the channel dimension (dim=1 for NCHW, NCSHW)
        return torch.cat([F.relu(x) * self.ongain.abs(), F.relu(-x)], dim=self.split_dim)

# --- Cropping Helper ---
def chomp(tensor, target_spatial_shape):
    """Crops spatial dimensions (H, W) of a 5D tensor NCDHW or 4D NCHW"""
    # Input tensor shape: (N, C, S, H, W) or (N, C, H, W)
    # target_spatial_shape: (H_target, W_target)
    
    if tensor.dim() == 5: # N C S H W
        current_H, current_W = tensor.shape[-2:]
    elif tensor.dim() == 4: # N C H W
        current_H, current_W = tensor.shape[-2:]
    else:
        raise ValueError(f"Unsupported tensor dim for chomp: {tensor.dim()}")

    target_H, target_W = target_spatial_shape

    diff_H = current_H - target_H
    diff_W = current_W - target_W

    if diff_H < 0 or diff_W < 0:
        raise ValueError(f"Target shape ({target_H},{target_W}) is larger than tensor shape ({current_H},{current_W}) in spatial dimensions.")

    start_H = diff_H // 2
    end_H = start_H + target_H
    start_W = diff_W // 2
    end_W = start_W + target_W

    if tensor.dim() == 5:
        return tensor[:, :, :, start_H:end_H, start_W:end_W]
    else: # tensor.dim() == 4
        return tensor[:, :, start_H:end_H, start_W:end_W]


# --- 3D Spatio-Temporal Conv Block ---
class ConvBlock(nn.Module):
    """
    3D Spatio-temporal Convolutional Block: Conv3D -> Norm -> SplitReLU
    Input shape: (N, C_in, S, H, W)
    Output shape: (N, C_out*2, S_out, H_out, W_out) due to SplitReLU
    """
    def __init__(self, in_channels, out_channels, kernel_size=(3, 3, 3),
                 stride=(1, 1, 1), use_layernorm=False, ln_eps=1e-5):
        super().__init__()
        self.use_layernorm = use_layernorm
        self.kernel_size = kernel_size
        # Causal padding for time dimension (kernel_size[0]-1)
        # Spatial padding is handled by chomp or by ensuring conv output matches
        self.temporal_padding_amount = kernel_size[0] - 1 
        self.out_channels_pre_split = out_channels # Channels before SplitReLU
        self.ln_eps = ln_eps

        # Convolution Layer: No padding here, will be handled manually for causal time & chomp for space
        self.conv = nn.Conv3d(in_channels, self.out_channels_pre_split, kernel_size,
                              stride=stride, padding=0, bias=False) # both batch norm and layernorm will have bias
        self.conv = weight_norm(self.conv, name='weight', dim=0)

        if self.use_layernorm:
            # LayerNorm applied over C,S,H,W. For per-channel stats, use GroupNorm.
            # This LayerNorm normalizes across all features for each batch element.
            # If you want per-channel normalization like BatchNorm, use GroupNorm(num_groups=out_channels, ...)
            # For this implementation, using LayerNorm as defined.
            # Shape for LayerNorm: (out_channels_pre_split, S_out, H_out, W_out)
            # PyTorch nn.LayerNorm takes normalized_shape.
            # We will apply it after conv, so we need to know output S,H,W.
            # This is tricky if stride changes S,H,W.
            # For simplicity, if layernorm, bias is false in conv, and we apply affine transform.
            # Using custom LayerNorm implementation as in original code for now.
            self.ln_weight = nn.Parameter(torch.ones(self.out_channels_pre_split))
            self.ln_bias = nn.Parameter(torch.zeros(self.out_channels_pre_split))
        else:
            self.batch_norm = nn.BatchNorm3d(self.out_channels_pre_split)

        self.activation = SplitRelu(split_dim=1, trainable_gain=True) # Split along channel dimension

    def forward(self, x_cat):
        # x_cat shape: (N, C_in, S, H, W)
        
        # Causal Padding for time dimension (left pad)
        # Spatial padding will be implicitly handled by 'chomp' in DenseNet if needed,
        # or convolutions are designed to maintain/reduce spatial dims.
        # Here, we only apply temporal padding for the conv.
        x_padded_temporal = F.pad(x_cat, (0, 0,  # W pad (left, right)
                                           0, 0,  # H pad (left, right)
                                           self.temporal_padding_amount, 0), # S pad (left, right)
                                   mode='reflect') # Or 'replicate' or 'constant'

        x_conv = self.conv(x_padded_temporal)
        # x_conv shape: (N, C_out_pre_split, S_out, H_out, W_out)

        if self.use_layernorm:
            # Custom LayerNorm implementation (normalizing across C,S,H,W for each N)
            # Or more likely, normalizing across S,H,W for each N,C (like original)
            # Original implementation normalized over dim=1 (channels) which is not typical LayerNorm.
            # Let's stick to the original's per-channel normalization logic for LayerNorm.
            mean = torch.mean(x_conv, dim=(2,3,4), keepdim=True) # Mean over S,H,W for each N,C
            var = torch.var(x_conv, dim=(2,3,4), keepdim=True, unbiased=False) # Var over S,H,W for each N,C
            
            x_norm = (x_conv - mean) / torch.sqrt(var + self.ln_eps)
            
            # Apply affine transform per channel
            weight_shape = [1, self.out_channels_pre_split, 1, 1, 1]
            ln_weight = self.ln_weight.view(*weight_shape)
            ln_bias = self.ln_bias.view(*weight_shape)
            x_norm = x_norm * ln_weight + ln_bias
        else:
            x_norm = self.batch_norm(x_conv)

        x_out = self.activation(x_norm)
        # x_out shape: (N, C_out_pre_split * 2, S_out, H_out, W_out)
        return x_out

# --- Checkpointed DenseNet ---
class DenseNet(nn.Module):
    """
    Memory-efficient DenseNet using gradient checkpointing.
    Input: (N, C_initial, S, H, W)
    Output: (N, C_final, S_out, H_out, W_out)
    S_out, H_out, W_out depend on ConvBlock strides and chomp.
    """
    def __init__(self, initial_channels, growth_rate, num_blocks,
                 kernel_size=(3,3,3), use_layernorm=True, use_checkpointing=True):
        super().__init__()
        self.growth_rate = growth_rate # This is ConvBlock's out_channels_pre_split
        self.use_checkpointing = use_checkpointing
        self.blocks = nn.ModuleList()

        current_channels = initial_channels
        for i in range(num_blocks):
            block = ConvBlock(
                in_channels=current_channels,
                out_channels=growth_rate, # This is out_channels_pre_split for ConvBlock
                kernel_size=kernel_size,
                use_layernorm=use_layernorm
            )
            self.blocks.append(block)
            current_channels += (growth_rate * 2) # *2 because ConvBlock uses SplitRelu

        self.out_channels = current_channels # Final number of output channels after all concatenations

    def _run_single_block(self, feature_maps_list, block_idx):
        block = self.blocks[block_idx]
        # Target spatial shape for chomp is from the *output* of the current block's input.
        # This implies all inputs to concat must match.
        # The first feature map in the list determines the target spatial and temporal shape.
        # However, conv blocks can change spatial/temporal dims.
        # Chomp should be applied to ensure all concatenated features match spatial dims.
        # Temporal dim matching is handled by causal padding in ConvBlock.
        
        # Determine target H, W from the *first* feature map in the list for this block's input
        # This assumes all feature maps in `feature_maps_list` should be chomped to match
        # the H,W of the feature map that has undergone the most spatial reduction so far.
        # A simpler strategy: chomp all to the H,W of `feature_maps_list[0]` if no striding,
        # or to the H,W of `feature_maps_list[-1]` if that's the smallest.
        # Let's assume chomp to the smallest H,W among the current feature_maps_list.
        
        min_H = min(f.shape[-2] for f in feature_maps_list)
        min_W = min(f.shape[-1] for f in feature_maps_list)
        target_spatial_shape_for_concat = (min_H, min_W)

        chomped_features = [chomp(f, target_spatial_shape_for_concat) for f in feature_maps_list]
        x_cat = torch.cat(chomped_features, dim=1) # Concatenate along channel dim
        return block(x_cat)

    def _run_block_checkpoint(self, *inputs_and_block_idx):
        # Last input is block_idx, rest are feature maps
        block_idx = inputs_and_block_idx[-1]
        feature_maps_list = inputs_and_block_idx[:-1]
        return self._run_single_block(list(feature_maps_list), block_idx)

    def forward(self, x):
        # x: (N, C_initial, S, H, W)
        feature_maps = [x]

        for i in range(len(self.blocks)):
            # Prepare inputs for the block: all previously generated feature maps
            current_block_input_feature_maps = list(feature_maps)

            if self.use_checkpointing and self.training:
                # Pass block_idx as a non-tensor argument for checkpoint
                # checkpoint requires tensor inputs, so we pass block_idx as part of *inputs
                # and unpack it in _run_block_checkpoint.
                # To avoid issues with non-tensor args, make block_idx a tensor.
                # However, it's simpler if _run_single_block is the function checkpointed,
                # and it receives its inputs appropriately.
                # The original code passed block_idx as a tensor.
                # Let's refine this.
                # checkpoint(fn, *args)
                # fn should be self._run_single_block
                # args should be (current_block_input_feature_maps, i)
                # This means current_block_input_feature_maps needs to be flattened into *args
                
                # Create a tuple of tensors for checkpointing
                # The block_idx will be passed as a standard Python arg, not part of *inputs for checkpoint
                # This requires a wrapper that accepts *feature_maps and block_idx separately.
                
                # Re-thinking checkpointing arguments:
                # The function to checkpoint should be `self.blocks[i]`.
                # The input to `self.blocks[i]` is `x_cat`.
                # So, the operations *before* `block(x_cat)` are not checkpointed if we do it this way.
                # The original code checkpointed `_run_block_checkpoint` which includes chomp and cat.
                
                # Inputs for checkpoint: feature maps + block_idx (as tensor)
                # This is a bit tricky because checkpoint expects tensor inputs.
                # A common pattern is to make a small wrapper.
                
                # Using the original checkpointing strategy:
                inputs_for_checkpoint = tuple(current_block_input_feature_maps) + (torch.tensor(i),)
                x_out = checkpoint(self._run_block_checkpoint, *inputs_for_checkpoint, use_reentrant=False) # PyTorch >=1.11 prefers use_reentrant=False

            else:
                x_out = self._run_single_block(current_block_input_feature_maps, i)
            
            feature_maps.append(x_out)

        # Final output: concatenate all feature maps (initial input + all block outputs)
        # Chomp all to the spatial dimensions of the *last* feature map produced
        final_target_spatial_shape = feature_maps[-1].shape[-2:]
        final_features_chomped = [chomp(f, final_target_spatial_shape) for f in feature_maps]
        
        return torch.cat(final_features_chomped, dim=1)

    def extra_repr(self):
        return f'growth_rate={self.growth_rate}, num_blocks={len(self.blocks)}, checkpointing={self.use_checkpointing}'

    # plot_weights and spy_weights methods can remain as they are, assuming jake.utils.visualization is available.
    # For this refactoring, I will comment them out if 'jake' is not a standard library.
    # def plot_weights(self, save_dir=None, max_blocks=None):
    #     from jake.utils.visualization import visualize_densenet # Assuming this exists
    #     return visualize_densenet(self, save_dir, max_blocks)

    # def spy_weights(self, block_idx=0, channel_idx=0, cmap='coolwarm'):
    #     # ... (implementation using matplotlib)
    #     pass


# --- Jacobian-Friendly Recurrent Cores ---

class OptimizedConvLSTMCell(nn.Module):
    """
    A ConvLSTM cell designed to be potentially more friendly for `torch.func` transformations like `jacrev`.
    This cell contains the logic for a single timestep of a ConvLSTM.
    Processes inputs for a single time step.
    Input: input_tensor (N, C_in, H, W), cur_state ( (N,C_hidden,H,W), (N,C_hidden,H,W) )
    Output: h_next (N,C_hidden,H,W), c_next (N,C_hidden,H,W)
    """
    def __init__(self, input_dim, hidden_dim, kernel_size, bias=True, forget_bias_init=1.0):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size if isinstance(kernel_size, tuple) else (kernel_size, kernel_size)
        self.padding = (self.kernel_size[0] // 2, self.kernel_size[1] // 2)
        self.bias = bias
        
        self.conv = nn.Conv2d(
            in_channels=input_dim + hidden_dim,
            out_channels=4 * hidden_dim,  # i, f, o, g gates
            kernel_size=self.kernel_size,
            padding=self.padding,
            bias=bias
        )
        
        if bias and forget_bias_init != 0:
            # Initialize forget gate bias (typically the second chunk of biases)
            # Bias tensor shape: (4 * hidden_dim)
            # Gates order: i, f, o, g (input, forget, output, cell/candidate)
            # Forget gate biases are from hidden_dim to 2*hidden_dim
            with torch.no_grad():
                self.conv.bias.data[hidden_dim : 2 * hidden_dim].fill_(forget_bias_init)

    def forward(self, input_tensor, cur_state):
        h_cur, c_cur = cur_state
        
        combined = torch.cat([input_tensor, h_cur], dim=1)
        gates = self.conv(combined)
        
        i_gate, f_gate, o_gate, g_gate = gates.chunk(4, dim=1)
        
        i = torch.sigmoid(i_gate)
        f = torch.sigmoid(f_gate)
        o = torch.sigmoid(o_gate)
        g = torch.tanh(g_gate)
        
        c_next = f * c_cur + i * g
        h_next = o * torch.tanh(c_next)
        
        return h_next, c_next

    def init_hidden(self, batch_size, image_spatial_shape, device):
        height, width = image_spatial_shape
        return (torch.zeros(batch_size, self.hidden_dim, height, width, device=device),
                torch.zeros(batch_size, self.hidden_dim, height, width, device=device))

class ConvLSTM(nn.Module):
    """
    ConvLSTM layer using OptimizedConvLSTMCell, designed for `jacrev` compatibility.
    Processes sequence data: (N, C_in, S, H, W)
    Outputs:
        - If return_sequences=True: (N, C_hidden, S_out, H, W)
        - If return_sequences=False: (N, C_hidden, H, W) (last time step output)
    S_out is S - start_time.
    """
    def __init__(self, input_dim, hidden_dim, kernel_size, dropout=0.5, 
                 bias=True, forget_bias_init=1.0, start_time=10, return_sequences=False):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.start_time = start_time
        self.return_sequences = return_sequences

        self.cell = OptimizedConvLSTMCell(
            input_dim=input_dim, hidden_dim=hidden_dim, kernel_size=kernel_size,
            bias=bias, forget_bias_init=forget_bias_init
        )
        self.dropout_layer = nn.Dropout(dropout)

    def forward(self, x, hidden_state_init=None):
        # x shape: (N, C_in, S, H, W)
        N, _, S, H, W = x.shape
        device = x.device

        if hidden_state_init is None:
            h, c = self.cell.init_hidden(N, (H, W), device)
        else:
            h, c = hidden_state_init
        
        outputs = []
        # If start_time > 0 and return_sequences, we need to pad initial outputs
        # The loop for actual processing runs from start_time to S.
        # The output sequence length will be S if padding, or S-start_time if not.
        # Let's match OptimizedConvLSTM's original behavior: output sequence has length S.
        
        processed_outputs = []
        for t in range(S):
            if t < self.start_time:
                if self.return_sequences:
                    # Pad with zeros matching hidden state spatial dimensions
                    outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
                continue # Skip actual computation for these early steps

            input_t = x[:, :, t, :, :] # (N, C_in, H, W)
            h_prev, c_prev = h, c # Store pre-update state for potential use
            
            h, c = self.cell(input_tensor=input_t, cur_state=(h, c))
            
            # Apply dropout to the output hidden state of this time step
            h_output = self.dropout_layer(h)
            
            if self.return_sequences:
                outputs.append(h_output)
            # Note: 'h' (pre-dropout) is used for the next recurrent step.
            # 'c' is always pre-dropout.

        if not self.return_sequences:
            if not outputs and S > self.start_time: # If sequence was processed but no outputs stored
                 # This case happens if start_time means we only care about the very last h
                 h_final_output = self.dropout_layer(h) # Apply dropout to the final h
                 return h_final_output
            elif not outputs: # Sequence was too short or start_time too large
                 return torch.zeros(N, self.hidden_dim, H, W, device=device) # Return zeros
            else: # Should not happen if logic is correct
                 return outputs[-1]


        # If return_sequences is True
        if not outputs: # e.g. if S <= start_time and return_sequences was true
            # Fill with all zeros if no processing happened but sequences are returned
            for _ in range(S):
                 outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
        
        # Stack along channel dim (dim=1) or time dim (dim=2)?
        # Original OptimizedConvLSTM: torch.stack(outputs, dim=2) -> (N, C_hidden, S_effective, H, W)
        # This seems more consistent if C_hidden is the "channel" dim.
        return torch.stack(outputs, dim=2) # (N, C_hidden, S, H, W)


class ConvGRUCell(nn.Module):
    """
    A ConvGRU cell, analogous to OptimizedConvLSTMCell.
    Processes inputs for a single time step.
    Input: input_tensor (N, C_in, H, W), h_cur (N,C_hidden,H,W)
    Output: h_next (N,C_hidden,H,W)
    """
    def __init__(self, input_dim, hidden_dim, kernel_size, bias=True):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size if isinstance(kernel_size, tuple) else (kernel_size, kernel_size)
        self.padding = (self.kernel_size[0] // 2, self.kernel_size[1] // 2)
        self.bias = bias

        # Convolution for all gates (reset, update, new)
        self.conv_gates = nn.Conv2d(
            in_channels=input_dim + hidden_dim,
            out_channels=3 * hidden_dim, 
            kernel_size=self.kernel_size,
            padding=self.padding,
            bias=bias
        )
    
    def forward(self, input_tensor, h_cur):
        combined = torch.cat([input_tensor, h_cur], dim=1)
        gates = self.conv_gates(combined)
        
        r_gate, z_gate, n_gate = gates.chunk(3, dim=1)
        
        r = torch.sigmoid(r_gate)  # Reset gate
        z = torch.sigmoid(z_gate)  # Update gate
        
        # Candidate hidden state calculation needs separate conv if n_gate is just W_n * x_t
        # If n_gate is W_n * x_t + U_n * (r * h_cur), then it's part of the combined conv.
        # The common GRU formulation:
        # n = tanh( W_n * x_t + U_n * (r * h_t-1) ) -> requires separate conv for W_n*x_t and U_n*h_reset
        # Or: n = tanh( W_n_x * x_t + b_nx + r * (W_n_h * h_t-1 + b_nh) )
        # The current `conv_gates` includes contributions for n_gate from both x and h.
        # So, n_gate = W_nx * x_t + W_nh * h_cur (before applying r)
        # Let's assume n_gate is for the candidate state that combines with h_cur after reset.
        # n_tilde = tanh( W_nx * x_t + b_nx + r * (U_nh * h_cur + b_nh) )
        # This requires splitting the n_gate's conv.
        # A simpler GRU (often used in ConvGRU):
        # n_gate from combined conv is for W_nx * x_t + W_nh * (r_t * h_{t-1})
        # Or, n_gate is just for W_n * x_t, and another conv for U_n * (r_t * h_{t-1})
        # Let's use the common ConvGRU structure where n_gate is computed from x_t and (r_t * h_{t-1})
        
        # This is one common way:
        # combined_for_n = torch.cat([input_tensor, r * h_cur], dim=1)
        # n = torch.tanh(self.conv_candidate(combined_for_n)) # Requires another conv layer
        
        # The provided monolithic ConvGRU implies n_gate is part of the main conv:
        # gates = self.conv(combined) where combined = [x_t, h_state]
        # r_gate, z_gate, n_gate = gates.chunk(3, dim=1)
        # n = torch.tanh(n_gate)
        # h_state = (1 - z) * n + z * h_state  <-- This is not standard GRU update.
        # Standard GRU: h_t = (1-z_t) * h_{t-1} + z_t * n_t
        # Or h_t = z_t * h_{t-1} + (1-z_t) * n_t  (depending on z definition)
        # Let's use: h_t = (1-z) * h_{t-1} + z * n_candidate
        # where n_candidate = tanh(W_nx*x_t + b_nx + r * (U_nh*h_{t-1} + b_nh))
        # This requires two convs for n_candidate or a specific structure for the single conv's n_gate.

        # For simplicity and matching the monolithic ConvGRU's likely structure (one big conv):
        # Assume n_gate from self.conv_gates is the candidate state pre-activation
        n_candidate_pre_act = n_gate 
        n = torch.tanh(n_candidate_pre_act)
        
        # h_next = (1 - z) * h_cur + z * n # Common update
        h_next = z * h_cur + (1-z) * n # Alternative update (used in some libraries)
                                       # Let's stick to the (1-z)*n + z*h from original OptimizedConvGRU like structure
        h_next = (1-z) * n + z * h_cur


        return h_next

    def init_hidden(self, batch_size, image_spatial_shape, device):
        height, width = image_spatial_shape
        return torch.zeros(batch_size, self.hidden_dim, height, width, device=device)

class ConvGRU(nn.Module):
    """
    ConvGRU layer using ConvGRUCell.
    Processes sequence data: (N, C_in, S, H, W)
    Outputs:
        - If return_sequences=True: (N, C_hidden, S_out, H, W)
        - If return_sequences=False: (N, C_hidden, H, W) (last time step output)
    """
    def __init__(self, input_dim, hidden_dim, kernel_size, dropout=0.5, 
                 bias=True, start_time=10, return_sequences=False):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.start_time = start_time
        self.return_sequences = return_sequences

        self.cell = ConvGRUCell(
            input_dim=input_dim, hidden_dim=hidden_dim, kernel_size=kernel_size, bias=bias
        )
        self.dropout_layer = nn.Dropout(dropout)

    def forward(self, x, hidden_state_init=None):
        # x shape: (N, C_in, S, H, W)
        N, _, S, H, W = x.shape
        device = x.device

        if hidden_state_init is None:
            h = self.cell.init_hidden(N, (H, W), device)
        else:
            h = hidden_state_init
        
        outputs = []
        for t in range(S):
            if t < self.start_time:
                if self.return_sequences:
                    outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
                continue

            input_t = x[:, :, t, :, :]
            h = self.cell(input_tensor=input_t, h_cur=h)
            h_output = self.dropout_layer(h) # Apply dropout to output
            
            if self.return_sequences:
                outputs.append(h_output)
            # 'h' (pre-dropout) is used for the next recurrent step.

        if not self.return_sequences:
            if not outputs and S > self.start_time:
                 h_final_output = self.dropout_layer(h)
                 return h_final_output
            elif not outputs :
                 return torch.zeros(N, self.hidden_dim, H, W, device=device)
            else: # Should not happen
                 return outputs[-1]

        if not outputs: # S <= start_time and return_sequences
            for _ in range(S):
                 outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
        
        return torch.stack(outputs, dim=2) # (N, C_hidden, S, H, W)


# --- BaseFactorizedReadout and DynamicGaussianReadout ---
# (Assuming these are largely okay, minor adjustments for consistency if needed)
class BaseFactorizedReadout(nn.Module):
    def __init__(self, in_channels, n_units, bias=True):
        super().__init__()
        self.n_units = n_units
        self.in_channels = in_channels
        self.features = nn.Conv2d(in_channels, n_units, kernel_size=1, bias=False) # 1x1 conv for feature mapping
        if bias:
            self.bias = nn.Parameter(torch.zeros(n_units))
        else:
            self.register_parameter('bias', None)

    def forward(self, x):
        raise NotImplementedError("Subclasses should implement forward method.")

    def get_spatial_weights(self):
        raise NotImplementedError("Subclasses should implement get_spatial_weights method.")

    def plot_weights(self):
        # Input x for this readout is (N, C_in, H, W) or (N, C_in, S, H, W)
        # If 5D, DynamicGaussianReadout takes last time step.
        # Feature weights are (n_units, C_in, 1, 1)
        feature_weights = self.features.weight.detach().cpu().squeeze().numpy() # (n_units, C_in)
        
        try:
            spatial_weights = self.get_spatial_weights().detach().cpu().numpy() # (n_units, H, W)
        except Exception as e:
            warnings.warn(f"Could not get spatial weights for plotting (maybe forward pass needed, or H,W not set?): {e}")
            return None, None

        if spatial_weights is None or spatial_weights.size == 0:
            warnings.warn(f"Spatial weights are empty or not computed, cannot plot.")
            return None, None
        
        n_units_spatial, H, W = spatial_weights.shape
        n_units_feat, in_channels_feat = feature_weights.shape if feature_weights.ndim == 2 else (feature_weights.shape[0], 1)


        if n_units_spatial != self.n_units or n_units_feat != self.n_units:
             warnings.warn(f"Mismatch in n_units for plotting. Spatial: {n_units_spatial}, Feat: {n_units_feat}, Expected: {self.n_units}")
             # Fallback if feature_weights is 1D (e.g. n_units=1, C_in > 1 or vice-versa)
             if feature_weights.ndim == 1 and self.n_units == 1: # Case: 1 unit, C_in features
                 feature_weights = feature_weights.reshape(1, -1) # (1, C_in)
             elif feature_weights.ndim == 1 and self.in_channels == 1: # Case: N units, 1 C_in
                  feature_weights = feature_weights.reshape(-1, 1) # (N_units, 1)


        fig, axs = plt.subplots(2, 1, figsize=(max(8, self.n_units * 0.6), 10), squeeze=False)
        axs = axs.ravel()


        # Plot feature weights
        if feature_weights.ndim == 2:
            feat_max = np.abs(feature_weights).max()
            im_feat = axs[0].imshow(feature_weights.T, cmap='coolwarm', interpolation='none', 
                                    vmin=-feat_max if feat_max > 0 else -1, 
                                    vmax=feat_max if feat_max > 0 else 1, aspect='auto')
            axs[0].set_xlabel('Unit Index')
            axs[0].set_ylabel(f'Input Channel ({self.in_channels})')
            plt.colorbar(im_feat, ax=axs[0], fraction=0.046, pad=0.04)
        else:
            axs[0].text(0.5, 0.5, "Feature weights not 2D, cannot plot heatmap.", ha='center', va='center')
        axs[0].set_title('Feature Weights (Channels to Units)')


        # Plot spatial weights (Gaussian masks)
        n_cols_spatial = int(np.ceil(np.sqrt(self.n_units)))
        n_rows_spatial = int(np.ceil(self.n_units / n_cols_spatial))
        
        # Create a new figure for spatial weights if needed, or use axs[1]
        # For simplicity, we create a grid in axs[1]
        axs[1].clear() # Clear previous content if any
        gs = axs[1].get_gridspec()
        # remove the underlying axes
        # for ax_inner in axs[1]. Kollegen:
        #     ax_inner.remove()
        
        # Create subplots for each unit's spatial weight
        # This part of plotting was complex and might need a dedicated figure.
        # For now, let's simplify to show one example or improve the layout.
        # The original code for plotting spatial weights was a bit convoluted with extent.
        # A simpler grid:
        if H > 0 and W > 0 :
            spatial_fig_size_w = n_cols_spatial * 2.5
            spatial_fig_size_h = n_rows_spatial * 2.5
            # Create a new figure for spatial weights for clarity
            fig_spatial, axs_spatial_grid = plt.subplots(n_rows_spatial, n_cols_spatial, 
                                                        figsize=(spatial_fig_size_w, spatial_fig_size_h), 
                                                        squeeze=False)
            fig_spatial.suptitle(f'Spatial Weights (Unit Masks - {H}x{W})', fontsize=14)
            
            spatial_max_val = np.abs(spatial_weights).max()
            for i in range(self.n_units):
                r, c = i // n_cols_spatial, i % n_cols_spatial
                ax_sp = axs_spatial_grid[r, c]
                if i < n_units_spatial: # Check if spatial weight exists for this unit
                    im_sp = ax_sp.imshow(spatial_weights[i], cmap='coolwarm', 
                                        vmin=-spatial_max_val if spatial_max_val > 0 else -1, 
                                        vmax=spatial_max_val if spatial_max_val > 0 else 1)
                    ax_sp.set_title(f'Unit {i}')
                    # plt.colorbar(im_sp, ax=ax_sp, fraction=0.046, pad=0.04) # Optional colorbar per subplot
                ax_sp.axis('off')
            
            for i in range(self.n_units, n_rows_spatial * n_cols_spatial): # Turn off unused subplots
                r, c = i // n_cols_spatial, i % n_cols_spatial
                axs_spatial_grid[r,c].axis('off')

            fig_spatial.tight_layout(rect=[0, 0, 1, 0.96]) # Adjust for suptitle
            # We return the main fig (with feature weights) and this new spatial_fig
            # axs[1].set_title(f'Spatial Weights ({H}x{W}) - See separate plot')
            # axs[1].axis('off') # Turn off the second subplot in the original figure
            # For now, let's not display the spatial weights in the original axs[1] to avoid complexity.
            axs[1].text(0.5, 0.5, "Spatial weights plotted in a separate figure if successful.", ha='center', va='center')

        else:
            axs[1].text(0.5, 0.5, "Spatial weights H or W is 0, cannot plot.", ha='center', va='center')
        
        fig.tight_layout()
        return fig, axs # Potentially return fig_spatial as well, or handle display outside


class DynamicGaussianReadout(BaseFactorizedReadout):
    def __init__(self, in_channels, n_units, bias=True, initial_std=5.0):
        super().__init__(in_channels, n_units, bias)
        self.mean = nn.Parameter(torch.zeros(n_units, 2)) 
        self.std = nn.Parameter(torch.ones(n_units, 2) * initial_std)
        self.theta = nn.Parameter(torch.zeros(n_units)) 

        self._cached_grid = None
        self._cached_H = None
        self._cached_W = None

    def _create_grid(self, H, W, device):
        y_coords = torch.linspace(-(H - 1) / 2., (H - 1) / 2., H, device=device)
        x_coords = torch.linspace(-(W - 1) / 2., (W - 1) / 2., W, device=device)
        grid_y, grid_x = torch.meshgrid(y_coords, x_coords, indexing='ij')
        grid = torch.stack([grid_y, grid_x], dim=-1) # (H, W, 2)
        # print(f"Created grid for H={H}, W={W}") # Optional: for debugging
        return grid

    def compute_gaussian_mask(self, H, W, device):
        if H != self._cached_H or W != self._cached_W or \
           self._cached_grid is None or self._cached_grid.device != device:
            if H <=0 or W <=0: # Cannot create grid if H or W is zero or negative
                warnings.warn(f"Cannot compute Gaussian mask for H={H}, W={W}. Returning empty tensor.")
                return torch.empty(self.n_units, H if H > 0 else 1, W if W > 0 else 1, device=device) # Return something sensible
            self._cached_grid = self._create_grid(H, W, device)
            self._cached_H = H
            self._cached_W = W
        grid = self._cached_grid

        std_clamped = self.std.clamp(min=1e-3) 
        mean_expanded = self.mean.unsqueeze(1).unsqueeze(1) # (n_units, 1, 1, 2)
        std_expanded = std_clamped.unsqueeze(1).unsqueeze(1) # (n_units, 1, 1, 2)
        grid_expanded = grid.unsqueeze(0) # (1, H, W, 2)

        cos_theta = torch.cos(self.theta)
        sin_theta = torch.sin(self.theta)
        R_y = torch.stack([cos_theta, -sin_theta], dim=-1) # Part of Rot matrix row 1
        R_x = torch.stack([sin_theta,  cos_theta], dim=-1) # Part of Rot matrix row 2
        R = torch.stack([R_y, R_x], dim=-2) # (n_units, 2, 2)

        centered_grid = grid_expanded - mean_expanded # (n_units, H, W, 2)
        rotated_grid = torch.einsum('nhwi,nij->nhwj', centered_grid, R)

        exponent = -0.5 * torch.sum((rotated_grid / std_expanded) ** 2, dim=-1) # (n_units, H, W)
        gaussian_mask = torch.exp(exponent)
        
        # Normalize sum over H,W to 1 for each unit's mask
        normalization_factor = gaussian_mask.sum(dim=(-1, -2), keepdim=True)
        gaussian_mask = gaussian_mask / (normalization_factor + 1e-8) # Add epsilon for stability
        return gaussian_mask

    def forward(self, x):
        # x shape: (N, C_in, S, H, W) or (N, C_in, H, W)
        if x.dim() == 5:
            # Using last time step if sequence is provided
            # warnings.warn("DynamicGaussianReadout received 5D tensor, using last element along sequence dim (dim 2).")
            x = x[:, :, -1, :, :] # (N, C_in, H, W)
        elif x.dim() != 4:
            raise ValueError(f"DynamicGaussianReadout expects 4D (N,C,H,W) or 5D (N,C,S,H,W) input, got {x.dim()}D")

        N, C_in, H, W = x.shape
        device = x.device

        feat = self.features(x) # (N, n_units, H, W)
        
        if H <= 0 or W <= 0:
            # If spatial dimensions are zero or invalid, cannot compute mask or pool.
            # Output zeros of the expected shape (N, n_units).
            warnings.warn(f"DynamicGaussianReadout received input with H={H}, W={W}. Outputting zeros.")
            out = torch.zeros(N, self.n_units, device=device, dtype=feat.dtype)
        else:
            gaussian_mask = self.compute_gaussian_mask(H, W, device) # (n_units, H, W)
            # Apply masks: (N, n_units, H, W) * (1, n_units, H, W) -> sum over H,W
            out = (feat * gaussian_mask.unsqueeze(0)).sum(dim=(-2, -1)) # (N, n_units)

        if self.bias is not None:
            out = out + self.bias
        return out

    def get_spatial_weights(self):
        if self._cached_grid is None or self._cached_H is None or self._cached_W is None:
            warnings.warn("Grid not cached in DynamicGaussianReadout. Call forward pass with valid H,W first.")
            return torch.empty(0) 
        if self._cached_H <= 0 or self._cached_W <=0:
            warnings.warn(f"Cannot get spatial weights for H={self._cached_H}, W={self._cached_W}.")
            return torch.empty(0)
            
        mask = self.compute_gaussian_mask(self._cached_H, self._cached_W, self._cached_grid.device)
        return mask # Return on whatever device it was computed, detach if sending to CPU later


# --- Modulator Components ---
class ModulatorLSTM(nn.Module):
    """
    LSTM-based modulator for behavioral data.
    Input: behavioral_data (N, n_vars, S_behavior)
    Output: final_hidden_state (N, K) or (N, 2*K if bidirectional)
    """
    def __init__(self, n_vars, K, lstm_layers=1, bidirectional=False):
        super().__init__()
        self.n_vars = n_vars
        self.K = K # LSTM hidden size (per direction)
        self.lstm_layers = lstm_layers
        self.num_directions = 2 if bidirectional else 1

        self.lstm = nn.LSTM(input_size=n_vars,
                            hidden_size=K,
                            num_layers=lstm_layers,
                            batch_first=True, # Expect input (N, S_behavior, n_vars)
                            bidirectional=bidirectional)

    def forward(self, behavioral_data):
        # behavioral_data: (N, n_vars, S_behavior)
        # Permute to (N, S_behavior, n_vars) for LSTM
        x = behavioral_data.permute(0, 2, 1) 

        # output: (N, S_behavior, K * num_directions)
        # h_n: (num_layers * num_directions, N, K)
        # c_n: (num_layers * num_directions, N, K)
        _, (h_n, _) = self.lstm(x)

        # Extract the final hidden state from the last layer
        if self.num_directions == 2: # Bidirectional
            # h_n shape: (lstm_layers*2, N, K)
            # Last layer, forward: h_n[-2, :, :]
            # Last layer, backward: h_n[-1, :, :]
            final_h_fwd = h_n[-2, :, :]
            final_h_bwd = h_n[-1, :, :]
            final_h = torch.cat([final_h_fwd, final_h_bwd], dim=1) # (N, 2*K)
        else: # Unidirectional
            # h_n shape: (lstm_layers, N, K)
            final_h = h_n[-1, :, :] # (N, K)
        return final_h

# Kept the single, more generic SpatialModulationExpander
class SpatialModulationExpander(nn.Module):
    """
    Expands a modulation vector to a spatial feature map for modulating convolutional features.
    Input: modulator_state (N, modulator_dim), target_spatial_shape (H,W)
    Output: (N, output_channels, H, W)
    """
    def __init__(self, modulator_dim, output_channels): # K=modulator_dim, C=target_channels (e.g. hidden_dim of ConvLSTM)
        super().__init__()
        self.projection = nn.Linear(modulator_dim, output_channels)
        self.output_channels = output_channels # Store for reshape

    def forward(self, modulator_state, target_spatial_shape):
        # modulator_state: (N, modulator_dim)
        # target_spatial_shape: (H, W)
        N = modulator_state.shape[0]
        H, W = target_spatial_shape

        mod_channels = self.projection(modulator_state) # (N, output_channels)
        
        mod_spatial = mod_channels.unsqueeze(-1).unsqueeze(-1) # (N, output_channels, 1, 1)
        mod_signal = mod_spatial.expand(-1, -1, H, W) # (N, output_channels, H, W)
        return mod_signal


# --- Modulated Recurrent Layers (Using Jacrev Cells) ---
class ModulatedConvLSTM(nn.Module):
    """
    A ConvLSTM (using OptimizedConvLSTMCell) that can be modulated by an external signal.
    Input: x (N,C_in,S,H,W), modulator (N, modulator_dim)
    Output: (N,C_hidden,S_out,H,W) or (N,C_hidden,H,W)
    """
    def __init__(self, input_dim, hidden_dim, kernel_size,
                 modulator_dim, modulation_type='additive', # modulator_dim is required
                 dropout=0.5, bias=True, forget_bias_init=1.0,
                 start_time=10, return_sequences=False):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.modulator_dim = modulator_dim
        self.modulation_type = modulation_type
        self.return_sequences = return_sequences
        self.start_time = start_time

        self.cell = OptimizedConvLSTMCell(
            input_dim=input_dim, hidden_dim=hidden_dim, kernel_size=kernel_size,
            bias=bias, forget_bias_init=forget_bias_init
        )

        if modulator_dim > 0:
            if modulation_type in ['additive', 'multiplicative']:
                # Modulator signal will be expanded to hidden_dim and applied to hidden state h
                self.modulation_expander = SpatialModulationExpander(modulator_dim, hidden_dim)
            elif modulation_type == 'concat':
                raise NotImplementedError("Concat modulation for ConvLSTM requires modifying cell's input_dim or a custom cell.")
            else:
                raise ValueError(f"Unknown modulation type: {modulation_type}")
        else:
            self.modulation_expander = None # Explicitly set to None

        self.dropout_layer = nn.Dropout(p=dropout)

    def forward(self, x, modulator=None, hidden_state_init=None):
        # x: (N, C_in, S, H, W)
        # modulator: (N, modulator_dim)
        N, _, S, H, W = x.shape
        device = x.device

        if hidden_state_init is None:
            h, c = self.cell.init_hidden(N, (H, W), device)
        else:
            h, c = hidden_state_init

        mod_signal_spatial = None
        if self.modulator_dim > 0 and modulator is not None and self.modulation_expander is not None:
            if self.modulation_type in ['additive', 'multiplicative']:
                mod_signal_spatial = self.modulation_expander(modulator, (H, W)) # (N, hidden_dim, H, W)
        
        outputs = []
        for t in range(S):
            if t < self.start_time:
                if self.return_sequences:
                    outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
                continue
            
            input_t = x[:, :, t, :, :]
            
            h_modulated = h # Start with current h
            if mod_signal_spatial is not None:
                if self.modulation_type == 'additive':
                    h_modulated = h + mod_signal_spatial
                elif self.modulation_type == 'multiplicative':
                    # Ensure mod_signal_spatial is positive for multiplicative stability, e.g., via sigmoid or softplus on expander output
                    # For now, direct multiplication. Consider adding activation in expander if issues.
                    h_modulated = h * torch.sigmoid(mod_signal_spatial) # Example: use sigmoid to keep it scaled 0-1
                                                                    # Or just h * mod_signal_spatial
            
            h, c = self.cell(input_tensor=input_t, cur_state=(h_modulated, c))
            h_output = self.dropout_layer(h)
            
            if self.return_sequences:
                outputs.append(h_output)

        if not self.return_sequences:
            if not outputs and S > self.start_time:
                 h_final_output = self.dropout_layer(h)
                 return h_final_output
            elif not outputs:
                 return torch.zeros(N, self.hidden_dim, H, W, device=device)
            else:
                 return outputs[-1] # This should not be reached if logic is correct for S > start_time

        if not outputs:
            for _ in range(S):
                 outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
        
        return torch.stack(outputs, dim=2)


class ModulatedConvGRU(nn.Module):
    """
    A ConvGRU (using ConvGRUCell) that can be modulated.
    Input: x (N,C_in,S,H,W), modulator (N, modulator_dim)
    Output: (N,C_hidden,S_out,H,W) or (N,C_hidden,H,W)
    """
    def __init__(self, input_dim, hidden_dim, kernel_size,
                 modulator_dim, modulation_type='additive',
                 dropout=0.5, bias=True, 
                 start_time=10, return_sequences=False):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.modulator_dim = modulator_dim
        self.modulation_type = modulation_type
        self.return_sequences = return_sequences
        self.start_time = start_time

        self.cell = ConvGRUCell(
            input_dim=input_dim, hidden_dim=hidden_dim, kernel_size=kernel_size, bias=bias
        )

        if modulator_dim > 0:
            if modulation_type in ['additive', 'multiplicative']:
                self.modulation_expander = SpatialModulationExpander(modulator_dim, hidden_dim)
            elif modulation_type == 'concat':
                 raise NotImplementedError("Concat modulation for ConvGRU requires custom cell.")
            else:
                raise ValueError(f"Unknown modulation type: {modulation_type}")
        else:
            self.modulation_expander = None

        self.dropout_layer = nn.Dropout(p=dropout)

    def forward(self, x, modulator=None, hidden_state_init=None):
        N, _, S, H, W = x.shape
        device = x.device

        if hidden_state_init is None:
            h = self.cell.init_hidden(N, (H, W), device)
        else:
            h = hidden_state_init

        mod_signal_spatial = None
        if self.modulator_dim > 0 and modulator is not None and self.modulation_expander is not None:
            if self.modulation_type in ['additive', 'multiplicative']:
                mod_signal_spatial = self.modulation_expander(modulator, (H, W))
        
        outputs = []
        for t in range(S):
            if t < self.start_time:
                if self.return_sequences:
                    outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
                continue
            
            input_t = x[:, :, t, :, :]
            
            h_modulated = h
            if mod_signal_spatial is not None:
                if self.modulation_type == 'additive':
                    h_modulated = h + mod_signal_spatial
                elif self.modulation_type == 'multiplicative':
                    h_modulated = h * torch.sigmoid(mod_signal_spatial) # Example scaling
            
            h = self.cell(input_tensor=input_t, h_cur=h_modulated)
            h_output = self.dropout_layer(h)
            
            if self.return_sequences:
                outputs.append(h_output)

        if not self.return_sequences:
            if not outputs and S > self.start_time:
                 h_final_output = self.dropout_layer(h)
                 return h_final_output
            elif not outputs:
                 return torch.zeros(N, self.hidden_dim, H, W, device=device)
            else:
                 return outputs[-1]

        if not outputs:
            for _ in range(S):
                 outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=device))
        
        return torch.stack(outputs, dim=2)


# --- Other Recurrent Alternatives (ConvSRU, TemporalAttention) ---
# These are kept as they were, assuming they are experimental or for specific use cases.
# Their jacrev compatibility or modulation capabilities are not the focus of this refactoring.

class ConvSRU(nn.Module):
    def __init__(self, input_dim, hidden_dim, kernel_size, dropout=0.5, bias=True, start_time=10, return_sequences=False):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size if isinstance(kernel_size, tuple) else (kernel_size, kernel_size)
        self.padding = (self.kernel_size[0]//2, self.kernel_size[1]//2)
        self.return_sequences = return_sequences
        self.start_time = start_time
        
        self.W = nn.Conv2d(
            in_channels=input_dim, out_channels=2 * hidden_dim, # forget and reset gates
            kernel_size=self.kernel_size, padding=self.padding, bias=bias
        )
        self.U = nn.Conv2d(
            in_channels=input_dim, out_channels=hidden_dim,    # candidate activation
            kernel_size=self.kernel_size, padding=self.padding, bias=bias
        )
        self.dropout_layer = nn.Dropout(dropout) # Changed from self.dropout
        
    def forward(self, x):
        # x shape: (N, C_in, S, H, W)
        N, _, S, H, W = x.size()
        # SRU typically has a cell state 'c'
        c = torch.zeros(N, self.hidden_dim, H, W, device=x.device)
        
        outputs = []
        for t in range(S):
            if t < self.start_time:
                if self.return_sequences:
                    # Output for SRU is h, which depends on c. If c is zero, h is also likely zero-ish.
                    outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=x.device))
                continue
                
            x_t = x[:, :, t, :, :] # (N, C_in, H, W)
            
            gates = self.W(x_t)
            f, r = gates.chunk(2, dim=1)
            f = torch.sigmoid(f) # Forget gate
            r = torch.sigmoid(r) # Reset gate (or output gate in some SRU variants)
            
            c_tilde = self.U(x_t) # Candidate for cell state (often just W_c * x_t)
            
            c = f * c + (1 - f) * c_tilde # Update cell state
            
            # Hidden state calculation in SRU can vary.
            # Original: h = r * torch.tanh(c) + (1 - r) * x_t  (Highway connection with x_t)
            # This implies hidden_dim must be same as input_dim if (1-r)*x_t is used directly.
            # Or, if hidden_dim != input_dim, (1-r)*x_t part needs projection.
            # For simplicity, let's assume h = r * tanh(c) if hidden_dim is the main output.
            # If x_t is part of output, its channels must match hidden_dim or be projected.
            # Let's use a common SRU output: h = tanh(c) (sometimes gated by r)
            h = torch.tanh(c) 
            if self.input_dim == self.hidden_dim: # Only if dims match can we do highway like original
                 h = r * h + (1-r) * x_t # Highway connection if dims match
            else: # Otherwise, just use the gated tanh(c)
                 h = r * h


            h_output = self.dropout_layer(h)
            if self.return_sequences:
                outputs.append(h_output)

        if not self.return_sequences:
            if not outputs and S > self.start_time: # Processed something
                 # Recompute last h if not stored
                 x_t_final = x[:, :, -1, :, :]
                 gates_final = self.W(x_t_final)
                 f_final, r_final = gates_final.chunk(2, dim=1)
                 f_final, r_final = torch.sigmoid(f_final), torch.sigmoid(r_final)
                 c_tilde_final = self.U(x_t_final)
                 c = f_final * c + (1-f_final) * c_tilde_final # c is carried over
                 
                 h_final = torch.tanh(c)
                 if self.input_dim == self.hidden_dim:
                     h_final = r_final * h_final + (1-r_final) * x_t_final
                 else:
                     h_final = r_final * h_final

                 return self.dropout_layer(h_final)
            elif not outputs: # Sequence too short
                 return torch.zeros(N, self.hidden_dim, H, W, device=x.device)
            else: # Should not be reached
                 return outputs[-1]


        if not outputs:
            for _ in range(S):
                 outputs.append(torch.zeros(N, self.hidden_dim, H, W, device=x.device))
        return torch.stack(outputs, dim=2)


class TemporalAttention(nn.Module):
    """
    Non-recurrent temporal processing using self-attention per time step.
    Input: (N, C_in, S, H, W)
    Output: (N, C_hidden, H, W) - typically the last time step's processed output.
            If return_sequences is True (added param), then (N, C_hidden, S, H, W)
    """
    def __init__(self, input_dim, hidden_dim, num_heads=4, dropout=0.5, bias=True, return_sequences=False):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim # This is d_model for attention
        self.num_heads = num_heads
        self.return_sequences = return_sequences
        
        if hidden_dim % num_heads != 0:
            raise ValueError(f"hidden_dim ({hidden_dim}) must be divisible by num_heads ({num_heads})")
        self.head_dim = hidden_dim // num_heads

        # Project input to Q, K, V for all heads at once
        self.qkv_proj = nn.Conv2d(
            in_channels=input_dim, 
            out_channels=3 * hidden_dim, # Q, K, V each of size hidden_dim
            kernel_size=1, bias=bias
        )
        self.out_proj = nn.Conv2d(
            in_channels=hidden_dim, 
            out_channels=hidden_dim, 
            kernel_size=1, bias=bias
        )
        self.dropout_layer = nn.Dropout(dropout) # Changed from self.dropout

    def forward(self, x):
        # x shape: (N, C_in, S, H, W)
        N, _, S, H, W = x.shape
        
        all_time_outputs = []
        for t in range(S):
            x_t = x[:, :, t, :, :] # (N, C_in, H, W)
            
            qkv = self.qkv_proj(x_t) # (N, 3*hidden_dim, H, W)
            q_f, k_f, v_f = qkv.chunk(3, dim=1) # Each (N, hidden_dim, H, W)
            
            # Reshape for multi-head attention: (N, num_heads, H*W, head_dim)
            # Original was (N, num_heads, head_dim, H*W) - let's verify standard attention format
            # Standard: (N, num_heads, seq_len_pixels, head_dim) where seq_len_pixels = H*W
            
            q = q_f.view(N, self.num_heads, self.head_dim, H * W).permute(0, 1, 3, 2) # (N, n_h, HW, h_d)
            k = k_f.view(N, self.num_heads, self.head_dim, H * W).permute(0, 1, 3, 2) # (N, n_h, HW, h_d)
            v = v_f.view(N, self.num_heads, self.head_dim, H * W).permute(0, 1, 3, 2) # (N, n_h, HW, h_d)

            # Scaled Dot-Product Attention
            # scores: (N, n_h, HW, HW) = (N,n_h,HW,h_d) @ (N,n_h,h_d,HW)
            scores = torch.matmul(q, k.transpose(-2, -1)) / (self.head_dim ** 0.5)
            attn_weights = F.softmax(scores, dim=-1)
            attn_weights = self.dropout_layer(attn_weights)
            
            # Apply attention to values
            # context: (N, n_h, HW, h_d) = (N,n_h,HW,HW) @ (N,n_h,HW,h_d)
            context = torch.matmul(attn_weights, v)
            
            # Concatenate heads and reshape back to image format
            # context: (N, n_h, HW, h_d) -> permute to (N, HW, n_h, h_d) -> reshape (N, HW, hidden_dim)
            context = context.permute(0, 2, 1, 3).contiguous().view(N, H * W, self.hidden_dim)
            # context: (N, HW, hidden_dim) -> permute (N, hidden_dim, HW) -> reshape (N, hidden_dim, H, W)
            context = context.permute(0, 2, 1).view(N, self.hidden_dim, H, W)
            
            out_t = self.out_proj(context) # (N, hidden_dim, H, W)
            all_time_outputs.append(out_t)
            
        if self.return_sequences:
            if not all_time_outputs: return torch.zeros(N, self.hidden_dim, S, H, W, device=x.device)
            return torch.stack(all_time_outputs, dim=2) # (N, C_hidden, S, H, W)
        else:
            if not all_time_outputs: return torch.zeros(N, self.hidden_dim, H, W, device=x.device)
            return all_time_outputs[-1] # Return last time step's output


# --- Factory function to create models with preset configurations ---
def create_v1_model(config_name='default', **override_params):
    """
    Create a ModularV1Model with a preset configuration.
    
    Parameters:
    config_name (str): Name of the preset configuration
                       Options: 'default', 'lightweight', 'high_performance', 'modulated', 'no_recurrent'
    override_params (dict): Parameters to override the preset configuration. 
                            Can override top-level keys (e.g., 'recurrent_type') 
                            or nested dicts (e.g., 'recurrent_params': {'dropout': 0.1}).
    
    Returns:
    ModularV1Model: The configured V1 model
    """
    # Define configurations. Recurrent types are now e.g. 'jacrev_convlstm'
    configs = {
        'default': {
            'frontend_type': 'da', 'convnet_type': 'densenet', 'recurrent_type': 'convgru',
            'modulator_type': 'none', 'readout_type': 'gaussian',
            'frontend_params': {'learnable_params': False},
            'convnet_params': {'growth_rate': 16, 'num_blocks': 4, 'use_checkpointing': True},
            'recurrent_params': {'hidden_dim': 64, 'dropout': 0.5, 'start_time': 10, 'return_sequences': False},
            'readout_params': {'n_units': 16, 'initial_std': 5.0}
        },
        'lightweight': {
            'frontend_type': 'temporal_conv', 'convnet_type': 'cnn', 'recurrent_type': 'convsru',
            'modulator_type': 'none', 'readout_type': 'gaussian',
            'frontend_params': {'out_channels': 2, 'kernel_size': (7,3,3)}, # out_channels for ConvBlock pre-split
            'convnet_params': {'out_channels': 16, 'use_layernorm': False}, # out_channels for ConvBlock pre-split
            'recurrent_params': {'hidden_dim': 32, 'dropout': 0.3, 'start_time': 5},
            'readout_params': {'n_units': 16}
        },
        'high_performance': {
            'frontend_type': 'da', 'convnet_type': 'densenet', 'recurrent_type': 'attention',
            'modulator_type': 'none', 'readout_type': 'gaussian',
            'frontend_params': {'learnable_params': True},
            'convnet_params': {'growth_rate': 24, 'num_blocks': 6},
            'recurrent_params': {'hidden_dim': 96, 'num_heads': 8, 'dropout': 0.5, 'return_sequences': False}, # Attention needs return_sequences=False for this setup
            'readout_params': {'n_units': 32}
        },
        'modulated': {
            'frontend_type': 'da', 'convnet_type': 'densenet', 
            'recurrent_type': 'modulated_convgru', # Using modulated jacrev GRU
            'modulator_type': 'lstm', 'readout_type': 'gaussian',
            'recurrent_params': {'hidden_dim': 64, 'modulation_type': 'additive'}, # Added modulation_type
            'modulator_params': {'n_vars': 2, 'K': 32},
            'readout_params': {'n_units': 16}
        },
        'no_recurrent': {
            'frontend_type': 'da', 'convnet_type': 'densenet', 'recurrent_type': 'none',
            'modulator_type': 'none', 'readout_type': 'gaussian',
            'convnet_params': {'growth_rate': 24, 'num_blocks': 6}, # Beef up convnet
            'readout_params': {'n_units': 16}
        }
    }

    if config_name not in configs:
        raise ValueError(f"Unknown configuration: {config_name}. Available: {list(configs.keys())}")
    
    config = configs[config_name]

    # Deep update for nested dicts (like component_params)
    for key, value in override_params.items():
        if key in config and isinstance(config[key], dict) and isinstance(value, dict):
            config[key].update(value)
        else:
            config[key] = value # Overwrite top-level or non-dict params
            
    return ModularV1Model(**config)


# --- Memory-efficient training utilities (largely unchanged) ---
class MemoryEfficientTrainer:
    def __init__(self, model, optimizer, criterion, device='cuda'):
        self.model = model.to(device) # Ensure model is on device
        self.optimizer = optimizer
        self.criterion = criterion
        self.device = device
        # Initialize GradScaler only if CUDA is available and device is cuda
        self.scaler = torch.cuda.amp.GradScaler(enabled=(use_amp and device == 'cuda'))
        self.model.training_step = 0 # Initialize training_step counter

    def train_step(self, inputs, targets, behavior=None, use_amp=True, accumulation_steps=1):
        inputs = inputs.to(self.device)
        targets = targets.to(self.device)
        if behavior is not None:
            behavior = behavior.to(self.device)
        
        loss_scale = 1.0 / accumulation_steps
        
        # Enable/disable scaler based on use_amp for this step
        self.scaler._enabled = (use_amp and self.device == 'cuda')

        with torch.cuda.amp.autocast(enabled=(use_amp and self.device == 'cuda')):
            if behavior is not None:
                outputs = self.model(inputs, behavior=behavior)
            else:
                outputs = self.model(inputs)
            loss = self.criterion(outputs, targets) * loss_scale
            
        self.scaler.scale(loss).backward()
        
        if (self.model.training_step + 1) % accumulation_steps == 0:
            if (use_amp and self.device == 'cuda'):
                self.scaler.unscale_(self.optimizer) 
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.scaler.step(self.optimizer)
            self.scaler.update()
            self.optimizer.zero_grad()
            
        self.model.training_step += 1
        return loss.item() * accumulation_steps

    def validate(self, dataloader, use_amp=True): # behavior input for dataloader
        self.model.eval()
        total_loss = 0.0
        
        with torch.no_grad():
            for batch in dataloader:
                inputs = batch['stim'].to(self.device) # Assuming dataloader provides dict
                targets = batch['robs'].to(self.device)
                behavior = batch.get('behavior', None) # Optional behavior data
                if behavior is not None:
                    behavior = behavior.to(self.device)

                with torch.cuda.amp.autocast(enabled=(use_amp and self.device == 'cuda')):
                    if behavior is not None:
                        outputs = self.model(inputs, behavior=behavior)
                    else:
                        outputs = self.model(inputs)
                    loss = self.criterion(outputs, targets)
                total_loss += loss.item()
                
        self.model.train()
        return total_loss / len(dataloader)

    def memory_usage_report(self):
        if self.device != 'cuda':
            return {"error": "Memory reporting only available for CUDA devices"}
        
        current_memory = torch.cuda.memory_allocated() / 1024**2  # MB
        max_memory = torch.cuda.max_memory_allocated() / 1024**2  # MB
        model_size = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**2  # MB
        
        optimizer_size = 0
        # Check if optimizer state is populated
        if self.optimizer.state:
            for param_group in self.optimizer.param_groups:
                for p in param_group['params']:
                    if p in self.optimizer.state:
                        opt_state = self.optimizer.state[p]
                        for state_val in opt_state.values():
                            if torch.is_tensor(state_val):
                                optimizer_size += state_val.numel() * state_val.element_size()
        optimizer_size /= 1024**2 # MB

        return {
            "current_memory_mb": current_memory,
            "max_memory_mb": max_memory,
            "model_size_mb": model_size,
            "optimizer_size_mb": optimizer_size,
            "other_memory_mb": max_memory - model_size - optimizer_size if max_memory >= (model_size + optimizer_size) else 0
        }

