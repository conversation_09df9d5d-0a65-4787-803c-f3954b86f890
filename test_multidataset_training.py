#!/usr/bin/env python3
"""
Interactive test script for multidataset training functionality.

This script tests the complete multidataset training pipeline:
1. Config loading and validation
2. Data preparation for multiple datasets
3. Dataloader creation with CombinedLoader
4. Lightning module creation and training setup
5. Short training run to verify everything works

Run this interactively to debug any issues in the training pipeline.
"""

import torch
import sys
import os
from pathlib import Path
import time

# Add the project root to the path
sys.path.insert(0, '/home/<USER>/repos/DataYatesV1')

def test_config_and_data_loading():
    """Test loading configs and preparing multidataset data."""
    print("=" * 60)
    print("STEP 1: Config Loading and Data Preparation")
    print("=" * 60)
    
    try:
        from DataYatesV1.models.config_loader import load_multidataset_config
        from DataYatesV1.utils.data import prepare_multidataset_data
        
        # Load configs
        train_config_path = "examples/configs/multidataset_train_config.yaml"
        print(f"Loading training config from: {train_config_path}")
        
        model_config, dataset_configs = load_multidataset_config(train_config_path)
        print(f"✓ Loaded model config (type: {model_config.get('model_type')})")
        print(f"✓ Loaded {len(dataset_configs)} dataset configs")
        
        # Prepare data (this might take a while)
        print("\nPreparing multidataset data...")
        start_time = time.time()
        
        train_datasets_dict, val_datasets_dict, updated_dataset_configs = prepare_multidataset_data(dataset_configs)
        
        prep_time = time.time() - start_time
        print(f"✓ Data preparation completed in {prep_time:.2f} seconds")
        
        # Print dataset info
        for i, (dataset_name, train_dset) in enumerate(train_datasets_dict.items()):
            val_dset = val_datasets_dict[dataset_name]
            config = updated_dataset_configs[i]
            print(f"  {dataset_name}: {len(train_dset)} train, {len(val_dset)} val, {len(config['cids'])} units")
        
        return model_config, updated_dataset_configs, train_datasets_dict, val_datasets_dict
        
    except Exception as e:
        print(f"❌ Config/data loading failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None


def test_dataloader_creation(train_datasets_dict, val_datasets_dict):
    """Test creating combined dataloaders."""
    print("\n" + "=" * 60)
    print("STEP 2: Dataloader Creation")
    print("=" * 60)
    
    if train_datasets_dict is None:
        print("❌ Skipping dataloader test due to data loading failure")
        return None, None
    
    try:
        # Import the function from model_train_multi
        sys.path.append('examples')
        from model_train_multi import create_multidataset_loaders
        
        print("Creating combined dataloaders...")
        batch_size = 32  # Small batch size for testing
        
        train_loader, val_loader = create_multidataset_loaders(
            train_datasets_dict, val_datasets_dict, batch_size=batch_size
        )
        
        print(f"✓ Created combined dataloaders with batch_size={batch_size}")
        print(f"  Train loader type: {type(train_loader).__name__}")
        print(f"  Val loader type: {type(val_loader).__name__}")
        
        # Test getting a batch
        print("\nTesting batch retrieval...")
        train_batch = next(iter(train_loader))
        print(f"✓ Retrieved training batch")
        print(f"  Batch type: {type(train_batch)}")

        # Handle different batch formats from CombinedLoader
        if isinstance(train_batch, dict):
            print(f"  Batch keys: {list(train_batch.keys())}")
            batch_dict = train_batch
        elif isinstance(train_batch, (list, tuple)):
            print(f"  Batch length: {len(train_batch)}")
            # CombinedLoader might return a list/tuple of batches
            if len(train_batch) == 2 and isinstance(train_batch[0], dict):
                # Assume it's (batch_dict, dataset_indices) or similar
                batch_dict = train_batch[0]
                print(f"  Using first element as batch_dict")
            else:
                print(f"  Unexpected batch format: {train_batch}")
                return train_loader, val_loader
        else:
            print(f"  Unexpected batch type: {type(train_batch)}")
            return train_loader, val_loader

        print(f"  Batch dict keys: {list(batch_dict.keys())}")

        for dataset_name, batch in batch_dict.items():
            print(f"  {dataset_name}:")
            print(f"    stim shape: {batch['stim'].shape}")
            print(f"    robs shape: {batch['robs'].shape}")
            if 'behavior' in batch:
                print(f"    behavior shape: {batch['behavior'].shape}")
        
        return train_loader, val_loader
        
    except Exception as e:
        print(f"❌ Dataloader creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def test_lightning_module_creation(model_config, dataset_configs):
    """Test creating the MultiDatasetPLCore module."""
    print("\n" + "=" * 60)
    print("STEP 3: Lightning Module Creation")
    print("=" * 60)
    
    if model_config is None or dataset_configs is None:
        print("❌ Skipping Lightning module test due to config loading failure")
        return None
    
    try:
        from DataYatesV1.models.lightning import MultiDatasetPLCore
        from DataYatesV1.models.build import build_model
        
        print("Creating MultiDatasetPLCore...")
        
        pl_model = MultiDatasetPLCore(
            model_class=build_model,
            model_config=model_config,
            dataset_configs=dataset_configs,
            optimizer='AdamW',
            optim_kwargs={'lr': 1e-3, 'weight_decay': 1e-5},
            accumulate_grad_batches=1,
            dataset_info=dataset_configs
        )
        
        print(f"✓ Created Lightning module")
        print(f"  Model type: {type(pl_model.model).__name__}")
        print(f"  Number of datasets: {pl_model.num_datasets}")
        print(f"  Optimizer: {pl_model.optimizer}")
        
        # Test parameter counting
        total_params = sum(p.numel() for p in pl_model.parameters())
        trainable_params = sum(p.numel() for p in pl_model.parameters() if p.requires_grad)
        
        print(f"  Total parameters: {total_params:,}")
        print(f"  Trainable parameters: {trainable_params:,}")
        
        return pl_model
        
    except Exception as e:
        print(f"❌ Lightning module creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_training_step(pl_model, train_loader):
    """Test a single training step."""
    print("\n" + "=" * 60)
    print("STEP 4: Training Step Test")
    print("=" * 60)
    
    if pl_model is None or train_loader is None:
        print("❌ Skipping training step test due to previous failures")
        return False
    
    try:
        print("Testing single training step...")
        
        # Get a batch
        train_batch = next(iter(train_loader))

        # Handle different batch formats from CombinedLoader
        if isinstance(train_batch, dict):
            batch_dict = train_batch
        elif isinstance(train_batch, (list, tuple)) and len(train_batch) >= 1:
            batch_dict = train_batch[0] if isinstance(train_batch[0], dict) else train_batch
        else:
            print(f"  Unexpected batch format: {type(train_batch)}")
            return False
        
        # Set model to training mode
        pl_model.train()
        
        # Test forward pass
        print("  Testing forward pass...")
        with torch.no_grad():
            output_dict = pl_model(batch_dict)
        
        print(f"  ✓ Forward pass successful")
        for dataset_name, batch in output_dict.items():
            print(f"    {dataset_name}: rhat shape {batch['rhat'].shape}")
        
        # Test training step
        print("  Testing training step...")
        loss = pl_model.training_step(batch_dict, 0)
        
        print(f"  ✓ Training step successful")
        print(f"    Loss: {loss.item():.6f}")
        print(f"    Loss type: {type(loss)}")
        print(f"    Loss requires_grad: {loss.requires_grad}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training step failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_short_training_run(model_config, dataset_configs, train_loader, val_loader):
    """Test a very short training run with PyTorch Lightning."""
    print("\n" + "=" * 60)
    print("STEP 5: Short Training Run")
    print("=" * 60)
    
    if any(x is None for x in [model_config, dataset_configs, train_loader, val_loader]):
        print("❌ Skipping training run due to previous failures")
        return False
    
    try:
        import lightning as pl
        from DataYatesV1.models.lightning import MultiDatasetPLCore
        from DataYatesV1.models.build import build_model
        
        print("Setting up short training run...")
        
        # Create Lightning module
        pl_model = MultiDatasetPLCore(
            model_class=build_model,
            model_config=model_config,
            dataset_configs=dataset_configs,
            optimizer='AdamW',
            optim_kwargs={'lr': 1e-3, 'weight_decay': 1e-5},
            accumulate_grad_batches=1,
            dataset_info=dataset_configs
        )
        
        # Create a simple trainer for testing
        trainer = pl.Trainer(
            max_epochs=1,
            limit_train_batches=3,
            limit_val_batches=2,
            enable_checkpointing=False,
            logger=False,
            enable_progress_bar=True,
            accelerator='cpu',  # Use CPU for testing
            devices=1
        )
        
        print("Starting short training run...")
        start_time = time.time()
        
        # Run training
        trainer.fit(pl_model, train_loader, val_loader)
        
        training_time = time.time() - start_time
        
        print(f"✓ Short training run completed in {training_time:.2f} seconds")
        print(f"  Final training metrics:")
        
        for metric_name, metric_value in trainer.callback_metrics.items():
            if isinstance(metric_value, torch.Tensor):
                print(f"    {metric_name}: {metric_value.item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Short training run failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Multidataset Training Pipeline Tests")
    print("This script will test the complete multidataset training pipeline.")
    print("Each step builds on the previous one.\n")
    
    # Step 1: Config and data loading
    model_config, dataset_configs, train_datasets_dict, val_datasets_dict = test_config_and_data_loading()
    
    # Step 2: Dataloader creation
    train_loader, val_loader = test_dataloader_creation(train_datasets_dict, val_datasets_dict)
    
    # Step 3: Lightning module creation
    pl_model = test_lightning_module_creation(model_config, dataset_configs)
    
    # Step 4: Training step test
    training_step_success = test_training_step(pl_model, train_loader)
    
    # Step 5: Short training run
    training_run_success = test_short_training_run(model_config, dataset_configs, train_loader, val_loader)
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Config & Data Loading", model_config is not None),
        ("Dataloader Creation", train_loader is not None),
        ("Lightning Module", pl_model is not None),
        ("Training Step", training_step_success),
        ("Short Training Run", training_run_success)
    ]
    
    all_passed = True
    for test_name, passed in tests:
        status = "✓ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 All tests passed! Multidataset training pipeline is working correctly.")
        print(f"\nYou can now run full training with:")
        print(f"python examples/model_train_multi.py --train-config examples/configs/multidataset_train_config.yaml")
    else:
        print(f"\n⚠️  Some tests failed. Check the output above for details.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    
    print(f"\nTest completed with {'success' if success else 'failures'}.")
    print(f"Variables available for debugging if needed.")
