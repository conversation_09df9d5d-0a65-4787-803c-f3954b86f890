#!/usr/bin/env python3
"""
Interactive test script for basic multidataset model functionality.

This script tests:
1. Loading multidataset configs
2. Creating MultiDatasetV1Model
3. Basic forward pass with different datasets
4. Model component validation

Run this interactively to see each step and debug any issues.
"""

import torch
import sys
import os
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, '/home/<USER>/repos/DataYatesV1')

def test_config_loading():
    """Test loading multidataset configurations."""
    print("=" * 60)
    print("STEP 1: Testing Config Loading")
    print("=" * 60)
    
    try:
        from DataYatesV1.models.config_loader import load_multidataset_config
        
        # Load the multidataset training config
        train_config_path = "examples/configs/multidataset_train_config.yaml"
        print(f"Loading training config from: {train_config_path}")
        
        model_config, dataset_configs = load_multidataset_config(train_config_path)
        
        print(f"✓ Successfully loaded configs")
        print(f"  Model type: {model_config.get('model_type')}")
        print(f"  Number of datasets: {len(dataset_configs)}")
        
        for i, dataset_config in enumerate(dataset_configs):
            print(f"  Dataset {i}:")
            print(f"    Frontend: {dataset_config.get('frontend', {}).get('type', 'none')}")
            print(f"    Readout: {dataset_config.get('readout', {}).get('type', 'none')}")
            print(f"    CIDs: {len(dataset_config.get('cids', []))} units")
            print(f"    Weight: {dataset_config.get('_weight', 1.0)}")
        
        return model_config, dataset_configs
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def test_model_creation(model_config, dataset_configs):
    """Test creating the MultiDatasetV1Model."""
    print("\n" + "=" * 60)
    print("STEP 2: Testing Model Creation")
    print("=" * 60)
    
    if model_config is None or dataset_configs is None:
        print("❌ Skipping model creation due to config loading failure")
        return None
    
    try:
        from DataYatesV1.models.build import build_model
        
        print("Creating MultiDatasetV1Model...")
        model = build_model(model_config, dataset_configs)
        
        print(f"✓ Model created successfully")
        print(f"  Model type: {type(model).__name__}")
        print(f"  Number of frontends: {len(model.frontends)}")
        print(f"  Number of readouts: {len(model.readouts)}")
        
        # Print model structure
        print("\nModel structure:")
        print(f"  Shared convnet: {type(model.convnet).__name__}")
        print(f"  Shared modulator: {type(model.modulator).__name__ if model.modulator else 'None'}")
        print(f"  Shared recurrent: {type(model.recurrent).__name__}")
        
        for i, (frontend, readout) in enumerate(zip(model.frontends, model.readouts)):
            print(f"  Dataset {i} frontend: {type(frontend).__name__}")
            print(f"  Dataset {i} readout: {type(readout).__name__} ({readout.n_units} units)")
        
        return model
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_forward_pass(model, dataset_configs):
    """Test forward pass through the model."""
    print("\n" + "=" * 60)
    print("STEP 3: Testing Forward Pass")
    print("=" * 60)
    
    if model is None:
        print("❌ Skipping forward pass due to model creation failure")
        return False
    
    try:
        # Create dummy input data
        batch_size = 2
        channels = 1
        seq_len = 10
        height = 32
        width = 32
        behavior_dim = 40
        
        stimulus = torch.randn(batch_size, channels, seq_len, height, width)
        behavior = torch.randn(batch_size, behavior_dim)
        
        print(f"Input stimulus shape: {stimulus.shape}")
        print(f"Input behavior shape: {behavior.shape}")
        
        # Test forward pass for each dataset
        for dataset_idx in range(len(dataset_configs)):
            print(f"\nTesting dataset {dataset_idx}:")
            
            with torch.no_grad():
                output = model(stimulus, dataset_idx, behavior)
            
            expected_units = len(dataset_configs[dataset_idx]['cids'])
            print(f"  Expected units: {expected_units}")
            print(f"  Output shape: {output.shape}")
            print(f"  Output range: [{output.min().item():.4f}, {output.max().item():.4f}]")
            
            # Validate output shape
            assert output.shape == (batch_size, expected_units), \
                f"Expected shape ({batch_size}, {expected_units}), got {output.shape}"
            
            print(f"  ✓ Forward pass successful for dataset {dataset_idx}")
        
        print(f"\n✓ All forward passes successful!")
        return True
        
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_parameters(model):
    """Test model parameter structure and sharing."""
    print("\n" + "=" * 60)
    print("STEP 4: Testing Model Parameters")
    print("=" * 60)
    
    if model is None:
        print("❌ Skipping parameter test due to model creation failure")
        return False
    
    try:
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        
        # Check shared vs per-dataset parameters
        shared_params = 0
        frontend_params = 0
        readout_params = 0
        
        # Shared components
        shared_params += sum(p.numel() for p in model.convnet.parameters())
        if model.modulator:
            shared_params += sum(p.numel() for p in model.modulator.parameters())
        shared_params += sum(p.numel() for p in model.recurrent.parameters())
        
        # Per-dataset components
        for frontend in model.frontends:
            frontend_params += sum(p.numel() for p in frontend.parameters())
        for readout in model.readouts:
            readout_params += sum(p.numel() for p in readout.parameters())
        
        print(f"\nParameter breakdown:")
        print(f"  Shared components: {shared_params:,} ({shared_params/total_params*100:.1f}%)")
        print(f"  Frontend components: {frontend_params:,} ({frontend_params/total_params*100:.1f}%)")
        print(f"  Readout components: {readout_params:,} ({readout_params/total_params*100:.1f}%)")
        
        print(f"\n✓ Parameter analysis complete!")
        return True
        
    except Exception as e:
        print(f"❌ Parameter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Multidataset Model Basic Tests")
    print("This script will test the basic functionality of multidataset models.")
    print("Run each step interactively to debug any issues.\n")
    
    # Step 1: Config loading
    model_config, dataset_configs = test_config_loading()
    
    # Step 2: Model creation
    model = test_model_creation(model_config, dataset_configs)
    
    # Step 3: Forward pass
    forward_success = test_forward_pass(model, dataset_configs)
    
    # Step 4: Parameter analysis
    param_success = test_model_parameters(model)
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Config Loading", model_config is not None),
        ("Model Creation", model is not None),
        ("Forward Pass", forward_success),
        ("Parameter Analysis", param_success)
    ]
    
    all_passed = True
    for test_name, passed in tests:
        status = "✓ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 All tests passed! Multidataset model is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Check the output above for details.")
    
    return model, model_config, dataset_configs


if __name__ == "__main__":
    # Run the tests
    model, model_config, dataset_configs = main()
    
    # Keep variables in scope for interactive debugging
    print(f"\nVariables available for debugging:")
    print(f"  model: {type(model).__name__ if model else 'None'}")
    print(f"  model_config: {'Loaded' if model_config else 'None'}")
    print(f"  dataset_configs: {len(dataset_configs) if dataset_configs else 0} configs")
