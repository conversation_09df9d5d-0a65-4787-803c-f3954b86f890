#%%

#%%
import os
from typing import Sequence
from DataYatesV1 import plot_stas

if __name__ == "__main__":
    # os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
    num_gpus = len(os.environ.get("CUDA_VISIBLE_DEVICES").split(","))
    # assert num_gpus == 2


#just get model stes and stas
#indicies for complex cells vs simple cells (look jake's supp from 2023 paper)
#make sure to save fits on directory yatesmarmov1 in subfolders
    import ray
    from ray import tune
    from ray.tune.schedulers import ASHAScheduler
    from ray.tune import JupyterNotebookReporter
    from ray.tune.search.optuna import OptunaSearch
    import optuna

    ray.init(num_cpus=64, num_gpus=num_gpus, include_dashboard=False, log_to_driver=False)



import torch
from torch import nn
from torch.nn import functional as F

import numpy as np
import matplotlib.pyplot as plt 
from tqdm import tqdm
import json
from copy import deepcopy
import gc
# Library imports
from DataYatesV1 import DictDataset, enable_autoreload, get_session, set_seeds, calc_sta, get_complete_sessions
from DataYatesV1.utils.basic_shifter import plot_sta_images
from DataYatesV1.utils.modeling import MaskedLoss
from DataYatesV1.utils.modeling.eval import PoissonBPSAggregator
from DataYatesV1.utils.rf import Gaussian2D
from DataYatesV1.utils import split_inds_by_trial

# Set environment
torch.set_float32_matmul_precision('medium')  # potential speed up
set_seeds(1002)
enable_autoreload()

verbosity = 0



#%%
def get_processed_dset(dset,
                       rf,
                       dim_for_centering=30, 
                       lags_to_use=None,
                       ):
    """Process a dataset by centering it on the RF and optionally selecting specific lags."""
    assert len(dset['stim'].shape) == 4

    if lags_to_use is None:
        lags_to_use = np.arange(dset['stim'].shape[1])
    
    # Check that lags_to_use is iterable
    assert hasattr(lags_to_use, '__iter__')

    # Center the dataset stim location on the RF
    x_min, x_max = int(rf.x0 - dim_for_centering/2), int(rf.x0 + dim_for_centering/2)
    y_min, y_max = int(rf.y0 - dim_for_centering/2), int(rf.y0 + dim_for_centering/2)
    dset_new = deepcopy(dset)

    #check that x_min, x_max, y_min, y_max are within bounds
    checks = [x_min >= 0, 
              x_max <= dset['stim'].shape[3], 
              y_min >= 0, 
              y_max <= dset['stim'].shape[2],
              x_max - x_min == dim_for_centering,
               y_max - y_min == dim_for_centering,]
    assert all(checks)


    # assert x_max - x_min == dim_for_centering and y_max - y_min == dim_for_centering

    dset_new['stim'] = dset_new['stim'][:, lags_to_use, y_min:y_max, x_min:x_max]
    return dset_new


#%%
def get_sta_with_lags(dset):
    """Calculate STAs for each lag in the dataset."""

    n_lags = dset['stim'].shape[1]
    stas = []
    for lag in tqdm(range(n_lags)):
        sta = calc_sta(dset['stim'][:, lag].detach().cpu(), 
                      dset['robs'].cpu(), 
                      [0], 
                      dfs=dset['dfs'].cpu().squeeze(), 
                      progress=False).cpu().squeeze().numpy()
        stas.append(sta)
    stas = np.stack(stas, axis=1)

    
    return stas
def get_cached_sta_with_lags(dset, rf, train_val_split, dim_for_centering):
    
    file_name = f'sta_ste_precomputed/{sess.name}/stas_centered_{dim_for_centering}_x_{int(rf.x0)}_y_{int(rf.y0)}_split_{train_val_split}.npy'
    if os.path.exists(file_name):
        stas = np.load(file_name)
    else:
        os.makedirs(os.path.dirname(file_name), exist_ok=True)
        stas = get_sta_with_lags(dset)
        np.save(file_name, stas)
    return stas

#%%
def get_dataset_for_cell(cell_to_fit, train_val_split, dim_for_centering = 30):
    sta_single_cell = stas[cell_to_fit]
    ste_single_cell = stes[cell_to_fit]
    peak_lag = np.argmax(ste_single_cell.max(axis=(1,2)))


    if verbosity > 1:
        stas_norm = sta_single_cell / np.max(np.abs(sta_single_cell), axis=(1,2), keepdims=True)
        fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}, title_prefix='lag')
        plt.show()


        stes_norm = 2 * (ste_single_cell - np.min(ste_single_cell, axis=(1,2), keepdims=True)) / np.ptp(ste_single_cell, axis=(1,2), keepdims=True) - 1
        fig, axs = plot_sta_images(stes_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}, title_prefix='lag')
        plt.show()
    # Fit a 2D Gaussian to the RF
    cell_ste = ste_single_cell[peak_lag]
    n_y, n_x = cell_ste.shape
    rf = Gaussian2D(*Gaussian2D.est_p0(cell_ste))
    rf.fit(cell_ste)
    if verbosity > 1:
        plt.imshow(cell_ste, cmap='coolwarm')
        plt.colorbar()
        plt.scatter(rf.x0, rf.y0, c='r', s=100)
        plt.show()

    
    try:
        x_min, x_max = int(rf.x0 - dim_for_centering/2), int(rf.x0 + dim_for_centering/2)
        y_min, y_max = int(rf.y0 - dim_for_centering/2), int(rf.y0 + dim_for_centering/2)
    except:
        rf.x0 = train_dset_loaded['stim'].shape[3]//2
        rf.y0 = train_dset_loaded['stim'].shape[2]//2
        print('Using center for stim')
        x_min, x_max = int(rf.x0 - dim_for_centering/2), int(rf.x0 + dim_for_centering/2)
        y_min, y_max = int(rf.y0 - dim_for_centering/2), int(rf.y0 + dim_for_centering/2)

     #check that x_min, x_max, y_min, y_max are within bounds
    # checks = [x_min >= 0, x_max <= train_dset_loaded['stim'].shape[3], y_min >= 0, y_max <= train_dset_loaded['stim'].shape[2]]
    checks = [x_min >= 0, 
              x_max <= train_dset_loaded['stim'].shape[3], 
              y_min >= 0, 
              y_max <= train_dset_loaded['stim'].shape[2],
              x_max - x_min == dim_for_centering,
               y_max - y_min == dim_for_centering,]
    if not all(checks):
        rf.x0 = train_dset_loaded['stim'].shape[3]//2
        rf.y0 = train_dset_loaded['stim'].shape[2]//2
        print('Using center for stim')
    train_dset_loaded_centered = get_processed_dset(train_dset_loaded, rf, dim_for_centering=dim_for_centering)
    val_dset_loaded_centered = get_processed_dset(val_dset_loaded, rf, dim_for_centering=dim_for_centering)

    train_dset_stas = get_cached_sta_with_lags(train_dset_loaded_centered, rf, train_val_split, dim_for_centering=dim_for_centering)

    if verbosity > 1:
        sta_single_cell = train_dset_stas[cell_to_fit]
        stas_norm = sta_single_cell / np.max(np.abs(sta_single_cell), axis=(1,2), keepdims=True)
        fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}, title_prefix='lag')
        plt.show()



    train_sta = train_dset_stas[cell_to_fit]

    return train_dset_loaded_centered, val_dset_loaded_centered, train_sta


#%%
#we need 0 mean for sta and stc computation
#fix the caching
def get_eigenvalues_and_vectors(train_data, cid, cache = False):
    # prepare cache directories
   
    cell_dir = f'STC_cache/{sess.name}/cell_{cid}'
    #check if cell_dir exists
    if os.path.exists(cell_dir) and cache:
        raise ValueError(f'There is a bug in the caching, the code with caching has a train val leak. Train val split must be accounted for in the cache name (which is not done right now).')
        print(f'Cell {cid} already exists, skipping')
        #load L and V
        L = torch.load(f'{cell_dir}/L.pt')
        V = torch.load(f'{cell_dir}/V.pt')
        # return L, V
    else:
        try:
            os.makedirs(cell_dir, exist_ok=True)

            robs = train_data['robs'][:, cid]
            # train_data['stim'] = train_data['stim'].cuda()
            # robs = robs.cuda()
            dims = train_data['stim'].shape[-3:]
            sta = torch.einsum('t, tlyx->lyx', robs, train_data['stim']) / robs.sum()

            ste = torch.einsum('t, tlyx->lyx', robs, train_data['stim']**2) / robs.sum()
            ste = ste.to('cpu')

            T    = train_data['stim'].shape[0]
            dims = train_data['stim'].shape[1:]
            D    = int(np.prod(dims))

            stim_flat = (train_data['stim'] - sta[None]).view(T, D)   # shape (T, D)
            # 2) get your weights as a column vector
            w = robs.float().unsqueeze(1)   # shape (T, 1)
            # 3) do the weighted outer‐sum in one go
            Cov = stim_flat.t() @ (w * stim_flat)
            Cov = Cov / (w.sum() - 1)

            Cov_cpu = Cov.cpu().double()


            # singular_vals = torch.linalg.eigvalsh(Cov, UPLO='U')

            # U, L, V = torch.linalg.svd(Cov)

            # U = U.to('cpu')
            # L = L.to('cpu')
            # V = V.to('cpu')
            # Cov = Cov.to('cpu')
            # sta = sta.to('cpu')

            # --- now plot & save the covariance matrix ---
            plt.figure()
            plt.imshow(Cov_cpu, cmap='gray')
            plt.colorbar()
            plt.title(f'Cell {cid}: Covariance Matrix')
            # plt.savefig(f'{cell_dir}/covariance_matrix.png')
            if verbosity > 0: plt.show()
            plt.close()

            torch.cuda.empty_cache()
            eigvals, eigvecs = torch.linalg.eigh(Cov.double(), UPLO='U')

            L = eigvals.flip(0).cpu()                 
            U = eigvecs.flip(1).cpu()                 
            V = U  
            # Cov = Cov.to('cpu')
            del Cov
            sta = sta.to('cpu')
            # train_data['stim'] = train_data['stim'].to('cpu')
            # train_data['robs'] = train_data['robs'].to('cpu')

            import gc
            for name in ['stim_flat','w','Cov','sta','eigvals','eigvecs']:
                if name in locals():
                    del locals()[name]
            gc.collect()
            torch.cuda.empty_cache() 

            if cache:
                # save L and V
                torch.save(L, f'{cell_dir}/L.pt')
                torch.save(V, f'{cell_dir}/V.pt')

            
            
        except Exception as e:
            print(f'Error processing cell {cid}: {e}')
            #check if e is a memory error
            if 'CUDA out of memory' in str(e):
                raise e
            else:
                print('Unknown error, skipping cell')
            return None, None
    
    
    plt.figure()
    plt.plot(L, 'o')
    plt.title(f'Cell {cid}: Eigenvalues of Covariance Matrix')
    plt.xlabel('Eigenvalue Index')
    plt.ylabel('Eigenvalue')
    plt.savefig(f'{cell_dir}/eigenvalues.png')
    if verbosity > 0: plt.show()
    plt.close()

    # make eig_values_range first 5 and last 5
    eig_values_range = np.concatenate([np.arange(5), np.arange(-5, 0)])
    plot_tensor = torch.stack(
        [ ste.cpu(), *[ V.T[i].cpu().reshape(dims) for i in eig_values_range ] ],
        dim=0
    )[:, :, None]

    from DataYatesV1 import plot_stas
    plot_stas(plot_tensor, 
            row_labels=['STE'] + [f'Eig {i}' for i in eig_values_range],
            col_labels=[f'lag {l}' for l in keys_lags['stim']])
    plt.title(f'Cell {cid}: STE & Eigenvectors')
    plt.savefig(f'{cell_dir}/eig_vs_ste.png')
    if verbosity > 0: plt.show()
    plt.close()

    torch.cuda.empty_cache()
    return L, V
#%%
# for cid in tqdm(range(n_units)):
#     verbosity = 0
#     train_data, val_data, train_sta = get_dataset_for_cell(cid, dim_for_centering=32)

#     L, V = get_eigenvalues_and_vectors(train_data, cid)
# import sys
# sys.exit()
# assert False, "stop here"

# verbosity = 1
# cid = 113
# train_data, val_data, train_sta = get_dataset_for_cell(cid, dim_for_centering=32)

# L, V = get_eigenvalues_and_vectors(train_data, cid)

#%%
from DataYatesV1.utils.modeling.reg import l1, laplacian, locality_conv

def l1_sta(sta):
    """L1 regularization for the STA"""
    return torch.norm(sta, p=1)

def laplacian_sta(sta):
    """Laplacian regularization for the STA"""
    assert len(sta.shape) == 3
    assert sta.shape[0] == 1 or sta.shape[0] >= 3
    sta = sta.squeeze()
    
    if sta.ndim == 2:
        sta = sta.view(1, 1, *sta.shape)
        kernel = torch.tensor([[0, 1, 0],
                              [1, -4, 1],
                              [0, 1, 0]], dtype=sta.dtype, device=sta.device).view(1, 1, 3, 3)
        conv = F.conv2d(sta, kernel, padding=1)
    elif sta.ndim == 3:
        sta = sta.view(1, 1, *sta.shape)
        kernel = (1/26) * torch.tensor([[[2, 3, 2],
                                        [3, 6, 3],
                                        [2, 3, 2]],
                                       [[3, 6, 3],
                                        [6, -88, 6],
                                        [3, 6, 3]],
                                       [[2, 3, 2],
                                        [3, 6, 3],
                                        [2, 3, 2]]], dtype=sta.dtype, device=sta.device).view(1, 1, 3, 3, 3)
        conv = F.conv3d(sta, kernel, padding=1)
    else:
        raise ValueError("Input must be a 2D or 3D tensor after squeezing.")
    
    return torch.norm(conv, p=2)




class LinearNonLinearModel(nn.Module):
    def __init__(self, input_dim):
        super(LinearNonLinearModel, self).__init__()
        self.bias = nn.Parameter(torch.tensor(1.0)) 
        assert len(input_dim) == 3
        self.kernel = nn.Parameter(torch.randn(input_dim))
        
    def forward(self, x):
        # generator = torch.einsum('ntd,td->n', 
        #                         x['stim'].view(x['stim'].shape[0], x['stim'].shape[1], -1), 
        #                         self.kernel.view(self.kernel.shape[0], -1))
        B = x['stim'].shape[0]                
        x_flat = x['stim'].view(B, -1)           # (B, D)
        k_flat = self.kernel.view(-1)            # (D,)
        generator = x_flat @ k_flat              # (B,)
        rhat = F.softplus(generator + self.bias)
        x['rhat'] = rhat.view(x['robs'].shape)
        x['generator'] = generator.view(x['robs'].shape).detach()
        return x

class AffineSoftplus(nn.Module):
    def __init__(self):
        super(AffineSoftplus, self).__init__()
        self.bias = nn.Parameter(torch.tensor(0.0)) 
        self.weight = nn.Parameter(torch.tensor(1.0))
    def forward(self, x):
        x['rhat'] = F.softplus(x['generator'] * self.weight + self.bias)
        x['rhat'] = x['rhat'].view(x['robs'].shape)
        return x
    
#%%
# def get_initial_weight_and_bias_ln(cell_to_fit, train_dset_loaded_centered, val_dset_loaded_centered, train_sta):
#     #want to first scale and bias the sta
#     batch = {
#             'stim': train_dset_loaded_centered['stim'], 
#             'robs': train_dset_loaded_centered['robs'][:, [cell_to_fit]], 
#             'dfs': train_dset_loaded_centered['dfs']
#     }
#     sta = torch.from_numpy(train_sta).float().to(device)
#     generator = torch.einsum('ntd,td->n', 
#                     batch['stim'].view(batch['stim'].shape[0], batch['stim'].shape[1], -1), 
#                     sta.view(sta.shape[0], -1))

#     generator_affine = AffineSoftplus()
#     loss = MaskedLoss(nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
#     optimizer = torch.optim.LBFGS(generator_affine.parameters(), lr=1, max_iter=10000, line_search_fn='strong_wolfe')
#     batch['generator'] = generator.detach()
#     metric_holders = {
#         'train_poisson_loss': None,
#         'val_poisson_loss': None,
#         'train_bps': None,
#         'val_bps': None,
#     }
#     def closure():
#         optimizer.zero_grad()
        
#         generator_affine(batch)
#         poisson_loss = loss(batch)
#         poisson_loss.backward()
#         return poisson_loss

#     pbar = range(3)
#     for epoch in pbar:
#         poisson_loss = optimizer.step(closure)
#         optimizer.zero_grad()
#         # pbar.set_description(f"Poiss: {poisson_loss.item():.4e}")
    
#     if verbosity > 0: print(f"Initial Poisson loss: {poisson_loss.item():.4e}")
#     batch['generator'] = batch['generator'].cpu().detach()
#     del batch['generator']
#     torch.cuda.empty_cache()
#     return generator_affine.weight.data, generator_affine.bias.data, poisson_loss.item()
def get_initial_weight_and_bias_ln(
    cell_to_fit,
    train_dset_loaded_centered,
    val_dset_loaded_centered,
    train_sta
):
    """
    Fit initial scale & bias for the LN model using LBFGS on both train & val,
    collecting train/val Poisson loss and bits-per-spike (BPS) metrics.
    """
    # Determine device
    device = train_dset_loaded_centered['stim'].device

    # Move STA to device and flatten dims
    sta = torch.from_numpy(train_sta).float().to(device)
    B_tr, T, H, W = train_dset_loaded_centered['stim'].shape
    D = T * H * W
    sta_flat = sta.view(D)  # (D,)

    # Prepare flattened stimulus for GEMV
    stim_tr_flat = train_dset_loaded_centered['stim'].view(B_tr, D)   # (B_tr, D)
    gen_tr = stim_tr_flat.mv(sta_flat)                                # (B_tr,)

    B_val = val_dset_loaded_centered['stim'].shape[0]
    stim_val_flat = val_dset_loaded_centered['stim'].view(B_val, D)    # (B_val, D)
    gen_val = stim_val_flat.mv(sta_flat)                              # (B_val,)

    # Build batches
    robs_tr  = train_dset_loaded_centered['robs'][:, [cell_to_fit]].to(device)
    dfs_tr   = train_dset_loaded_centered['dfs'].to(device)
    batch_tr = {'generator': gen_tr.unsqueeze(1), 'robs': robs_tr, 'dfs': dfs_tr}

    robs_val = val_dset_loaded_centered['robs'][:, [cell_to_fit]].to(device)
    dfs_val  = val_dset_loaded_centered['dfs'].to(device)
    batch_val= {'generator': gen_val.unsqueeze(1), 'robs': robs_val,  'dfs': dfs_val}

    # Model, loss, optimizer
    model     = AffineSoftplus().to(device)
    loss_fn   = MaskedLoss(nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
    optimizer = torch.optim.LBFGS(
        model.parameters(),
        lr=1.0,
        max_iter=10000,
        line_search_fn='strong_wolfe'
    )

    # Metrics container
    metric_holders = {
        'train_poisson_loss': None,
        'val_poisson_loss':   None,
        'train_bps':          None,
        'val_bps':            None,
    }

    # Closure to compute metrics
    def closure():
        optimizer.zero_grad()

        out_tr = model(batch_tr)
        train_loss = loss_fn(out_tr).mean()
        train_agg = PoissonBPSAggregator()
        train_agg(out_tr)
        tbps = train_agg.closure().item()

        train_loss.backward()

        with torch.no_grad():
            out_val = model(batch_val)
            val_loss = loss_fn(out_val).mean()
            val_agg = PoissonBPSAggregator()
            val_agg(out_val)
            vbps = val_agg.closure().item()

        # Fill metrics
        metric_holders['train_poisson_loss'] = train_loss.item()
        metric_holders['train_bps']          = tbps
        metric_holders['val_poisson_loss']   = val_loss.item()
        metric_holders['val_bps']            = vbps

        return train_loss

    # Run a few LBFGS steps
    for _ in range(3):
        optimizer.step(closure)
        optimizer.zero_grad()

    # Extract weight & bias to CPU
    w0 = model.weight.data.clone().cpu()
    b0 = model.bias.data.clone().cpu()

    # Move everything to CPU before cleanup
    model.to('cpu')
    for k in batch_tr.keys(): batch_tr[k] = batch_tr[k].cpu()
    for k in batch_val.keys(): batch_val[k] = batch_val[k].cpu()
    # batch_tr = {k: v.detach().cpu() for k, v in batch_tr.items()}
    # batch_val= {k: v.detach().cpu() for k, v in batch_val.items()}
    stim_tr_flat = stim_tr_flat.cpu()
    stim_val_flat= stim_val_flat.cpu()
    gen_tr = gen_tr.cpu()
    gen_val = gen_val.cpu()

    sta = sta.cpu()

    # Delete GPU references and clear cache
    del model, optimizer, loss_fn
    del stim_tr_flat, stim_val_flat, gen_tr, gen_val, sta_flat, sta
    del batch_tr, batch_val, robs_tr, dfs_tr, robs_val, dfs_val
    torch.cuda.empty_cache()

    return w0, b0, metric_holders


#%%
# Training configuration
loss_fn = MaskedLoss(nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
def orthogonality_penalty(kernels: torch.Tensor) -> torch.Tensor:
    """
    Computes Frobenius‐norm squared penalty for (K Kᵀ – I),
    encouraging the rows of `kernels` to be orthonormal.

    kernels: Tensor of shape (G, *spatiotemporal_dims*)
    returns: scalar penalty
    """
    # G = kernels.shape[0]
    # D = int(torch.prod(torch.tensor(kernels.shape[1:], device=kernels.device)))
    # W = kernels.view(G, D)                     # (G, D)
    # gram = W @ W.t()                           # (G, G)
    # I = torch.eye(G, device=gram.device)       # (G, G)
    # return torch.norm(gram - I, p='fro')**2

    G = kernels.shape[0]
    D = int(torch.prod(torch.tensor(kernels.shape[1:], device=kernels.device)))
    W = kernels.view(G, D)               # (G, D)
    gram = W @ W.t()                     # (G, G)
    mask = torch.ones_like(gram)
    mask.fill_diagonal_(0)               # zero‐out the diagonal
    return torch.norm(gram * mask, p='fro')**2

def forward(model, dset, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, cell_to_fit, ortho_coeff=0.0, padding_mode='reflect'):
    """Forward pass through the LN model with loss calculation"""
    batch = {
        'stim': dset['stim'], 
        'robs': dset['robs'][:, [cell_to_fit]], 
        'dfs': dset['dfs']
    }
    
    # Forward pass
    batch = model(batch)
    poisson_loss = loss_fn(batch)
    
    # Regularization
    # reg = laplacian_coeff * laplacian_sta(model.kernel) + l1_coeff * l1_sta(model.kernel)
    if hasattr(model, 'kernels'):
        kerns = list(model.kernels)      # EnergyModel case
    else:
        kerns = [model.kernel]           # LinearNonLinearModel case


    # compute Laplacian and L1 penalties across all kernels
    # regs_lap_check = sum(laplacian_sta(k) for k in kerns)

    regs_lap_space = sum(laplacian(k, dims=[-2, -1], padding_mode=padding_mode) for k in kerns) if laplacian_coeff_space > 0.0 else 0.0
    regs_lap_time  = sum(laplacian(k, dims=[-3], padding_mode=padding_mode) for k in kerns) if laplacian_coeff_time > 0.0 else 0.0
    regs_lap_local = sum(locality_conv(k, dims = [-2, -1]) for k in kerns) if local_coeff > 0.0 else 0.0

    # assert torch.allclose(regs_lap, regs_lap_check), f"Mismatch in Laplacian penalty: {regs_lap} vs {regs_lap_check}"
    regs_l1  = sum(l1_sta(k) for k in kerns) if l1_coeff > 0.0 else 0.0

    # combine with coefficients
    reg = laplacian_coeff_space * regs_lap_space + l1_coeff * regs_l1 + laplacian_coeff_time * regs_lap_time + local_coeff * regs_lap_local
    

    # Orthogonality penalty (only for multi‐kernel models)
    if ortho_coeff > 0.0 and hasattr(model, 'kernels'):
        ortho_pen = orthogonality_penalty(model.kernels)
        reg = reg + ortho_coeff * ortho_pen
    
    loss_val = poisson_loss + reg

    # Check for NaN values
    if torch.isnan(poisson_loss).any() or torch.isnan(reg).any() or torch.isnan(loss_val).any():
        raise ValueError("NaN detected in loss calculation")

    return loss_val, poisson_loss, reg, batch

def evaluate_model(model, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, train_dset_loaded_centered, val_dset_loaded_centered, cell_to_fit, ortho_coeff=0.0):
    """Evaluate model on train and validation sets, returning metrics as a dictionary"""
    # Train set evaluation
    # Initialize metrics aggregators
    train_bps_aggregator = PoissonBPSAggregator()
    val_bps_aggregator = PoissonBPSAggregator()
    loss_val, train_poisson_loss, train_reg, train_batch = forward(model, train_dset_loaded_centered, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, cell_to_fit, ortho_coeff=ortho_coeff)

    # Validation set evaluation
    with torch.no_grad():
        val_loss, val_poisson_loss, val_reg, val_batch = forward(model, val_dset_loaded_centered, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, cell_to_fit, ortho_coeff=ortho_coeff)
        val_bps_aggregator(val_batch)
        train_bps_aggregator(train_batch)
        train_bps = train_bps_aggregator.closure().item()
        val_bps = val_bps_aggregator.closure().item()
    
    return {
        'loss': loss_val,
        'train_poisson_loss': train_poisson_loss,
        'train_reg': train_reg,
        'val_loss': val_loss,
        'val_poisson_loss': val_poisson_loss,
        'val_reg': val_reg,
        'train_bps': train_bps,
        'val_bps': val_bps
    }

def visualize_results(model, epoch, num_epochs, metrics, max_val_bps):
    """Visualize the model's receptive field and performance metrics"""
        
    sta = model.kernel.cpu().detach().numpy()
    stas_norm = sta / np.max(np.abs(sta))
    vmin, vmax = np.min(stas_norm), np.max(stas_norm)
    
    # Ensure symmetric color scale
    if abs(vmin) > abs(vmax):
        vmax = -vmin
    else:
        vmin = -vmax
    
    if verbosity > 0:
        fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': vmin, 'vmax': vmax}, title_prefix='lag')
        plt.show()
    
        # Print performance metrics
        print(f'Train BPS: {metrics["train_bps"]}, Val BPS: {metrics["val_bps"]}')
        print(f'Epoch {epoch+1}/{num_epochs}, Train Loss: {metrics["train_poisson_loss"].item():.4f}, Val Loss: {metrics["val_loss"]:.4f}')
        print('max val bps:', max_val_bps)

# 

def train_with_lbfgs(model, 
                     laplacian_coeff_space, 
                     laplacian_coeff_time,
                     local_coeff,
                     l1_coeff, 
                     cell_to_fit,
                     train_dset_loaded_centered, val_dset_loaded_centered,
                     using_ray=True,
                     max_iter=10000,
                     ortho_coeff=0.0):
    optimizer = torch.optim.LBFGS(
        model.parameters(),
        lr=1,
        max_iter=max_iter,
        tolerance_grad=1e-6,
        # tolerance_change=1e-8,
        tolerance_change=1e-6,
        # line_search_fn='strong_wolfe'
        history_size=10
    )

    # one mutable counter so closure can increment it
    counter = [0]
    # Track metric history
    metric_history = {
        'train_poisson_loss': [],
        'train_bps': [],
        'val_poisson_loss': [],
        'val_bps': []
    }

    if verbosity > 0:
        pbar = tqdm(range(max_iter), position=0, leave=True)

    def closure():
        optimizer.zero_grad()
        metrics = evaluate_model(
            model,
            laplacian_coeff_space,
            laplacian_coeff_time,
            local_coeff,
            l1_coeff,
            train_dset_loaded_centered,
            val_dset_loaded_centered,
            cell_to_fit,
            ortho_coeff=ortho_coeff
        )
        loss = metrics['loss']
        loss.backward()

        # Store metrics in history
        metric_history['train_poisson_loss'].append(metrics['train_poisson_loss'].item())
        metric_history['train_bps'].append(metrics['train_bps'])
        metric_history['val_poisson_loss'].append(metrics['val_poisson_loss'].item())
        metric_history['val_bps'].append(metrics['val_bps'])

        # report at each internal iteration
        counter[0] += 1
        if using_ray:
            tune.report({
              "val_bps": metrics["val_bps"],  # already a scalar from .item() call
              "training_iteration": counter[0],  # already a scalar
              "train_bps": metrics["train_bps"],  # already a scalar from .item() call
              "train_poisson_loss": metrics["train_poisson_loss"].item(),  # tensor with gradients
              "train_reg": metrics["train_reg"].item(),  # tensor with gradients
              "val_poisson_loss": metrics["val_poisson_loss"].item(),  # tensor with gradients
              "val_reg": metrics["val_reg"].item(),  # tensor with gradients
              "loss": loss.item(),  # already has .item()
            })
        if verbosity > 0:
            pbar.set_description(
                f"Poiss: {metrics['train_poisson_loss']:.4e}|Reg: {metrics['train_reg']:.4e}|"
                f"T_BPS: {metrics['train_bps']:.4f}|V_BPS:{metrics['val_bps']:.4f}"
            )
            pbar.update(1)
        return loss

    # run LBFGS (will call closure() up to max_iter times)
    optimizer.step(closure)
    if verbosity > 0: pbar.close()

    # final evaluation
    with torch.no_grad():
        final_metrics = evaluate_model(
            model,
            laplacian_coeff_space, 
            laplacian_coeff_time,
            local_coeff,
            l1_coeff,
            train_dset_loaded_centered,
            val_dset_loaded_centered,
            cell_to_fit,
            ortho_coeff=ortho_coeff
        )
    final_metrics['history'] = metric_history
    return final_metrics

def train_with_adamw(model, 
                    laplacian_coeff_space, 
                    laplacian_coeff_time,
                    local_coeff,
                    l1_coeff, 
                    cell_to_fit,
                    train_dset_loaded_centered, val_dset_loaded_centered,
                    using_ray=True,
                    max_iter=10000,
                    ortho_coeff=0.0,
                    patience=100, 
                    tolerance=5e-4,
                    lr=1e-3):
    """Train the model using AdamW optimizer with early stopping"""
    # Configure AdamW optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr)
    
    # Early stopping variables
    best_val_bps = -np.inf
    best_metrics = None
    best_state_dict = None
    no_improve_count = 0
    
    # Counter for ray reporting
    counter = [0]
    
    # Track metric history
    metric_history = {
        'train_poisson_loss': [],
        'train_bps': [],
        'val_poisson_loss': [],
        'val_bps': []
    }
    
    # Training loop with progress bar
    pbar = tqdm(range(max_iter), position=0, leave=True) if verbosity > 0 else range(max_iter)
    for epoch in pbar:
        # Standard gradient descent step
        optimizer.zero_grad()
        metrics = evaluate_model(
            model,
            laplacian_coeff_space,
            laplacian_coeff_time,
            local_coeff,
            l1_coeff,
            train_dset_loaded_centered,
            val_dset_loaded_centered,
            cell_to_fit,
            ortho_coeff=ortho_coeff
        )
        loss = metrics['loss']
        loss.backward()
        optimizer.step()
        
        val_bps = metrics['val_bps']
        
        # Store metrics in history
        metric_history['train_poisson_loss'].append(metrics['train_poisson_loss'].item())
        metric_history['train_bps'].append(metrics['train_bps'])
        metric_history['val_poisson_loss'].append(metrics['val_poisson_loss'].item())
        metric_history['val_bps'].append(metrics['val_bps'])
        
        # Track best model
        if val_bps > best_val_bps:
            best_val_bps = val_bps
            best_metrics = metrics.copy()
            best_state_dict = deepcopy(model.state_dict())
            no_improve_count = 0
        else:
            no_improve_count += 1
        
        # Report metrics for ray tuning
        counter[0] += 1
        if using_ray:
            tune.report({
              "val_bps": metrics["val_bps"],
              "training_iteration": counter[0],
              "train_bps": metrics["train_bps"],
              "train_poisson_loss": metrics["train_poisson_loss"].item(),
              "train_reg": metrics["train_reg"].item(),
              "val_poisson_loss": metrics["val_poisson_loss"].item(),
              "val_reg": metrics["val_reg"].item(),
              "loss": loss.item(),
            })
        
        if verbosity > 0:
            # Update progress bar
            pbar.set_description(
                f"Poiss: {metrics['train_poisson_loss']:.4e}|Reg: {metrics['train_reg']:.4e}|"
                f"T_BPS: {metrics['train_bps']:.4f}|V_BPS:{val_bps:.4f}|Patience: {no_improve_count}/{patience}"
            )
        
        # Check if we should stop early
        if no_improve_count >= patience:
            if verbosity > 0: 
                print(f"Early stopping at epoch {epoch+1}: No improvement in validation BPS for {patience} epochs")
            break
    
    if verbosity > 0: 
        pbar.close()
        print("\nTraining complete!")
        print(f"Cell {cell_to_fit}: Best Val BPS = {best_val_bps:.4f}")
    
    # Restore best model
    if best_state_dict is not None:
        model.load_state_dict(best_state_dict)
    
    # Final evaluation with best model
    with torch.no_grad():
        final_metrics = evaluate_model(
            model,
            laplacian_coeff_space, 
            laplacian_coeff_time,
            local_coeff,
            l1_coeff,
            train_dset_loaded_centered,
            val_dset_loaded_centered,
            cell_to_fit,
            ortho_coeff=ortho_coeff
        )
    
    final_metrics['history'] = metric_history
    return final_metrics


#%%
class AffineEnergy(nn.Module):
    def __init__(self, n_generators: int):
        super(AffineEnergy, self).__init__()
        # one weight & bias for each generator
        self.weights = nn.Parameter(torch.ones(n_generators))
        # self.biases  = nn.Parameter(torch.zeros(n_generators))
        self.bias = nn.Parameter(torch.tensor(0.0))

    def forward(self, x):
        # x['generators']: (n_generators, N)
        G = x['generators']                   # tensor, shape [n_gens, N]
        # compute per‐generator energy: w_i * G_i^2 + b_i
        # broadcast weights/biases over the N dimension
        E = (self.weights[:, None] * G)**2
        # sum across generators → shape [N]
        E_sum = E.sum(dim=0)
        # nonlinear readout
        R = F.softplus(E_sum + self.bias) 
        # reshape into whatever shape your robs expects
        x['rhat'] = R.view(x['robs'].shape)
        return x
def get_initial_weight_and_bias_energy(
    train_data,
    val_data,
    cid: int,
    eigenvecs_T: torch.Tensor,
    eig_indices,
    device: str = 'cuda'
):
    """
    Fit initial per-generator weights and biases for the energy model using LBFGS,
    computing generators internally from eigenvectors.

    Args:
      train_data: dict-like with 'stim', 'robs', 'dfs'
      cid:        int, cell index
      eigenvecs_T: Tensor of shape (D, D), each row is an eigenvector
      eig_indices: sequence of ints, indices of rows in eigenvecs_T to use
      device:     device string for computation

    Returns:
      weights, biases: Tensors each of shape (n_generators,)
    """
    stim_tr = train_data['stim'].to(device) 
    # assert train_data['stim'].device == torch.device('cpu') 
    T      = stim_tr.shape[0]
    D      = int(torch.prod(torch.tensor(stim_tr.shape[1:], device=device)))
    stim_tr_flat = stim_tr.view(T, D)              # (T, D)

    # Move validation stim
    stim_val      = val_data['stim'].to(device)
    T_val         = stim_val.shape[0]
    stim_val_flat = stim_val.view(T_val, D)        # (T_val, D)

    # Select eigenvectors and compute generators
    ev      = eigenvecs_T.to(device)               # (D, D)
    sel     = ev[eig_indices, :]                   # (G, D)
    gen_tr  = sel @ stim_tr_flat.T                  # (G, T)
    gen_val = sel @ stim_val_flat.T                 # (G, T_val)

    # Prepare batches
    robs_tr    = train_data['robs'][:, cid].unsqueeze(1).to(device)
    dfs_tr     = train_data['dfs'].to(device)
    batch_tr   = {'generators': gen_tr,  'robs': robs_tr,  'dfs': dfs_tr}

    robs_val   = val_data['robs'][:, cid].unsqueeze(1).to(device)
    dfs_val    = val_data['dfs'].to(device)
    batch_val  = {'generators': gen_val, 'robs': robs_val, 'dfs': dfs_val}

    # Model, loss, optimizer
    n_gens  = len(eig_indices)
    model   = AffineEnergy(n_gens).to(device)
    loss_fn = MaskedLoss(torch.nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
    optimizer = torch.optim.LBFGS(model.parameters(), lr=1.0, max_iter=1000, line_search_fn='strong_wolfe')

    # Progress bar (3 LBFGS steps)
    pbar = tqdm(range(3), desc=f"InitEnergy cell{cid}", leave=True) if verbosity>0 else None

    metric_holders = {
        'train_poisson_loss': None,
        'val_poisson_loss': None,
        'train_bps': None,
        'val_bps': None,
    }
    def closure():
        optimizer.zero_grad()
        out_tr = model(batch_tr)
        poisson_loss = loss_fn(out_tr).mean()

        # Train BPS
        train_agg = PoissonBPSAggregator()
        train_agg(out_tr)
        tbps = train_agg.closure().item()

        

        poisson_loss.backward()

        with torch.no_grad():
            # Val BPS
            out_val = model(batch_val)
            val_loss = loss_fn(out_val).mean()
            val_agg = PoissonBPSAggregator()
            val_agg(out_val)
            vbps = val_agg.closure().item()
        if verbosity > 0:
            pbar.set_description(f"Poiss:{poisson_loss:.4e} Tbps:{tbps:.4f} Vbps:{vbps:.4f}")
            pbar.update(1)
        metric_holders['train_poisson_loss'] = poisson_loss.item()
        metric_holders['val_poisson_loss'] = val_loss.item()
        metric_holders['train_bps'] = tbps
        metric_holders['val_bps'] = vbps

        return poisson_loss

    poisson_loss = optimizer.step(closure)
    

    # model.to('cpu')
    # stim = stim.to('cpu')
    # stim_flat = stim_flat.to('cpu')
    # del stim
    # del stim_flat
    #move everything to cpu and then del to free up memory on gpu
    model.to('cpu')
    stim_tr = stim_tr.to('cpu')
    stim_val = stim_val.to('cpu')
    stim_tr_flat = stim_tr_flat.to('cpu')
    stim_val_flat = stim_val_flat.to('cpu')
    del stim_tr
    del stim_val
    del stim_tr_flat
    del stim_val_flat
    del gen_tr
    del gen_val

    torch.cuda.empty_cache()

    return model.weights.detach().cpu(), model.bias.detach().cpu(), metric_holders
class EnergyModel(nn.Module):
    def __init__(self, input_dim, n_generators):
        """
        Simple energy-model LN cascade:
          • multiple linear filters (generators)
          • square-and-sum pooling + open bias
          • softplus output nonlinearity

        Args:
          input_dim:    tuple (n_lags, H, W)
          n_generators: number of energy filters
        """
        super(EnergyModel, self).__init__()
        assert len(input_dim) == 3
        # one spatiotemporal kernel per generator
        self.kernels = nn.Parameter(torch.randn(n_generators, *input_dim))
        # one bias per generator (added after squaring)
        self.bias  = nn.Parameter(torch.tensor(0.0))

    def forward(self, x):
        # x['stim']: (batch, n_lags, H, W)
        B = x['stim'].shape[0]
        # flatten the spatiotemporal dims
        stim_flat    = x['stim'].view(B, -1)             # (B, D)
        kernels_flat = self.kernels.view(self.kernels.size(0), -1)  # (G, D)

        # linear responses of each generator
        G = stim_flat @ kernels_flat.t()                 # (B, G)

        # energy pooling: square + bias, then sum over generators
        E = G**2                 # (B, G)
        E_sum = E.sum(dim=1)                             # (B,)

        # final nonlinearity
        rhat = F.softplus(E_sum + self.bias)                         # (B,)
        x['rhat'] = rhat.view(x['robs'].shape)           # match spike-shape
        return x

#%%

def save_sta_visualization(sta, cell_to_fit, save_path):
    """
    Save visualization of original STA to the specified path
    
    Args:
        sta: The original STA (numpy array)
        cell_to_fit: Index of the cell
        save_path: Path to save the visualization
    """
    # Convert to numpy if it's a torch tensor
    if isinstance(sta, torch.Tensor):
        sta = sta.cpu().detach().numpy()
    
    stas_norm = sta / np.max(np.abs(sta))
    vmin, vmax = np.min(stas_norm), np.max(stas_norm)
    
    # Ensure symmetric color scale
    if abs(vmin) > abs(vmax):
        vmax = -vmin
    else:
        vmin = -vmax
    
    plt.figure(figsize=(12, 10))
    fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': vmin, 'vmax': vmax}, title_prefix='lag')
    plt.suptitle(f"Original STA for Cell {cell_to_fit}", fontsize=16)
    plt.tight_layout()
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    if verbosity > 0: print(f"STA visualization saved to {save_path}")
    plt.close()

def save_kernel_visualization_ln(model, cell_to_fit, best_val_bps, laplacian_space_coeff, l1_coeff, save_path, initial_val_bps=None, laplacian_time_coeff=None, local_coeff=None, ortho_coeff=None):
    """
    Save visualization of model kernel to the specified path
    
    Args:
        model: The trained LinearNonLinearModel
        cell_to_fit: Index of the cell that was fit
        best_val_bps: The best validation BPS value achieved by the model
        laplacian_space_coeff: The spatial Laplacian regularization coefficient
        l1_coeff: The L1 regularization coefficient
        save_path: Path to save the visualization
        initial_val_bps: The initial validation BPS value (before training)
        laplacian_time_coeff: The temporal Laplacian regularization coefficient
        local_coeff: The local coefficient
        ortho_coeff: The orthogonality coefficient
    """
    sta = model.kernel.cpu().detach().numpy()
    stas_norm = sta / np.max(np.abs(sta))
    vmin, vmax = np.min(stas_norm), np.max(stas_norm)
    
    # Ensure symmetric color scale
    if abs(vmin) > abs(vmax):
        vmax = -vmin
    else:
        vmin = -vmax
    
    plt.figure(figsize=(12, 10))
    plt.rcParams['axes.grid'] = False
    fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': vmin, 'vmax': vmax}, title_prefix='lag')
    
    # Create more detailed title with all parameters
    title = f"Model Kernel for Cell {cell_to_fit}\n"
    
    if initial_val_bps is not None:
        title += f"Init Val BPS={initial_val_bps:.3f}, "
    
    title += f"Val BPS={best_val_bps:.3f}\n"
    title += f"lap_space={laplacian_space_coeff:.1e}"
    
    if laplacian_time_coeff is not None:
        title += f", lap_time={laplacian_time_coeff:.1e}"
    
    if local_coeff is not None:
        title += f", local={local_coeff:.1e}"
    
    if l1_coeff > 0:
        title += f", l1={l1_coeff:.1e}"
        
    if ortho_coeff is not None and ortho_coeff > 0:
        title += f", ortho={ortho_coeff:.1e}"
    
    plt.suptitle(title, fontsize=16)
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    if verbosity > 0: print(f"Kernel visualization saved to {save_path}")
    plt.close()

def save_kernel_visualization_energy(
    model: EnergyModel,
    eigenvecs: torch.Tensor,
    eig_indices: Sequence[int],
    cell_to_fit: int,
    initial_val_bps: float,
    best_val_bps: float,
    laplacian_space_coeff: float,
    laplacian_time_coeff: float,
    local_coeff: float,
    l1_coeff: float,
    ortho_coeff: float,
    save_path: str
):
    """
    Visualize and save both the original eigen‐maps and the learned energy kernels.
    
    Args:
      model:           trained EnergyModel
      eigenvecs:       Tensor (n_gens, n_lags, H, W) of the original eigenvectors
      eig_indices:     list of ints, which eigen‐maps were used
      cell_to_fit:     cell index
      best_val_bps:    best validation BPS
      laplacian_coeff: Laplacian regularization coefficient
      l1_coeff:        L1 regularization coefficient
      save_path:       where to write the PNG
    """
    import torch
    import matplotlib.pyplot as plt
    from DataYatesV1 import plot_stas

    # pull everything to CPU & numpy‐compatible
    orig = eigenvecs.detach().cpu()         # (G, lags, H, W)
    learned = model.kernels.detach().cpu()  # (G, lags, H, W)

    # stack: first eigen‐maps, then learned kernels
    plot_tensor = torch.cat([orig, learned], dim=0)[:, :, None]  # → (2G, lags, H, W, 1)

    # row labels
    eig_labels = [f"Eig {i}" for i in eig_indices]
    kern_labels = [f"Kern {i}" for i in eig_indices]
    row_labels = eig_labels + kern_labels

    # column labels (lags)
    col_labels = [f"lag {l}" for l in keys_lags['stim']]

    # normalize color scale independently per row?
    # here we let plot_stas handle defaults
    plt.rcParams['axes.grid'] = False
    
    plt.figure(figsize=(12, 2 * len(row_labels)))
    fig, axs = plot_stas(
        plot_tensor, 
        row_labels=row_labels,
        col_labels=col_labels
    )
    plt.suptitle(
        f"Cell {cell_to_fit}: Eigen‐maps (top) vs Learned Kernels (bottom)\n"
        f"Init Val BPS={initial_val_bps:.3f}, Val BPS={best_val_bps:.3f}, \n lap_space={laplacian_space_coeff:.1e}, lap_time={laplacian_time_coeff:.1e}, local={local_coeff:.1e}",
        fontsize=16
    )
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    if verbosity > 0:
        print(f"Energy kernels visualization saved to {save_path}")
    plt.close()

def select_optimal_trial(analysis, selection_method="global_best", tolerance=None, optimize_param="local"):
    """
    Select the optimal trial from Ray Tune analysis based on specified criteria.
    
    Args:
        analysis: Ray Tune analysis object
        selection_method: str, either "global_best" or "adjusted_best"
        tolerance: float, required when selection_method="adjusted_best"
            Defines how much below the best val_bps we're willing to accept
        optimize_param: str, default="local"
            When using "adjusted_best", specifies which parameter to maximize
            (e.g., "local", "laplacian_space", "laplacian_time", "l1", "ortho_coeff")
            
    Returns:
        best_trial: The selected trial
    """
    # First, get all completed trials with val_bps
    completed_trials = []
    for trial in analysis.trials:
        if (trial.status == "TERMINATED" and 
            "val_bps" in trial.last_result):
            completed_trials.append(trial)
    
    if not completed_trials:
        raise ValueError("No completed trials found with val_bps metric")
    
    # Sort trials by val_bps in descending order
    completed_trials.sort(key=lambda t: t.last_result["val_bps"], reverse=True)
    
    # Get the global best trial (highest val_bps)
    global_best_trial = completed_trials[0]
    global_best_val_bps = global_best_trial.last_result["val_bps"]
    
    if verbosity > 0:
        print(f"Global best trial: val_bps={global_best_val_bps:.4f}")
    
    if selection_method == "global_best":
        return global_best_trial
    
    elif selection_method == "adjusted_best":
        # Validate parameters
        if tolerance is None:
            raise ValueError("tolerance must be provided when selection_method='adjusted_best'")
        
        # Define threshold
        threshold_bps = global_best_val_bps - tolerance
        
        if verbosity > 0:
            print(f"Using threshold val_bps: {threshold_bps:.4f} (global best - {tolerance})")
        
        # Filter trials with good enough performance
        good_trials = []
        for trial in completed_trials:
            trial_bps = trial.last_result["val_bps"]
            if trial_bps >= threshold_bps:
                good_trials.append(trial)
                if verbosity > 0:
                    print(f"Trial with val_bps {trial_bps:.4f} meets threshold")
        
        if not good_trials:
            # This shouldn't happen since the global best should always meet the threshold
            if verbosity > 0:
                print("No trials meet threshold, falling back to global best")
            return global_best_trial
        
        # Define function to get parameter value
        def get_param_value(trial):
            if optimize_param == "local":
                return float(trial.config.get("local", 0))
            elif optimize_param == "laplacian_space":
                return float(trial.config.get("laplacian_space", 0))
            elif optimize_param == "laplacian_time":
                return float(trial.config.get("laplacian_time", 0))
            elif optimize_param == "l1":
                return float(trial.config.get("l1", 0))
            elif optimize_param == "ortho_coeff":
                return float(trial.config.get("ortho_coeff", 0))
            else:
                raise ValueError(f"Unknown optimize_param: {optimize_param}")
        
        # Find the trial with the highest value for the specified parameter
        highest_param_trial = max(good_trials, key=get_param_value)
        
        if verbosity > 0:
            print(f"Selected trial: {optimize_param}={get_param_value(highest_param_trial):.4e}, val_bps={highest_param_trial.last_result['val_bps']:.4f}")
            print(f"Global best: {optimize_param}={get_param_value(global_best_trial):.4e}, val_bps={global_best_val_bps:.4f}")
        
        return highest_param_trial
    
    else:
        raise ValueError(f"Invalid selection_method: {selection_method}. " 
                         f"Must be 'global_best' or 'adjusted_best'")

def get_all_trials_info(analysis):
    """
    Extract relevant information from all completed trials in the analysis.
    
    Args:
        analysis: Ray Tune analysis object
        
    Returns:
        list: List of dictionaries containing trial information
    """
    # Get all completed trials
    completed_trials = [trial for trial in analysis.trials if trial.status == "TERMINATED"]
    
    # Create a list to store trial information
    all_trials_info = []
    
    # Extract relevant information from each trial
    for trial in completed_trials:
        if "val_bps" in trial.last_result:
            trial_info = {
                "val_bps": trial.last_result["val_bps"],
                "laplacian_space": trial.config["laplacian_space"],
                "laplacian_time": trial.config["laplacian_time"],
                "local": trial.config["local"],
                "l1": trial.config["l1"],
                "ortho_coeff": float(trial.config.get("ortho_coeff", 0.0))
            }
            all_trials_info.append(trial_info)
    
    return all_trials_info

#%%

# ─── Your Ray trainable wrapper ────────────────────────────────────────────────
def tune_trainable(config, train_ds, val_ds, train_sta, cell_to_fit, L=None, V=None):
    # move datasets to GPU
    train_dev = {k: v.to(device) for k, v in train_ds.items()}
    val_dev   = {k: v.to(device) for k, v in val_ds.items()}
    
    # Extract hyperparameters
    lap_space = config["laplacian_space"]
    lap_time = config['laplacian_time']
    local = config['local']
    l1 = config["l1"]
    ortho_coeff = float(config.get("ortho_coeff", 0.0))
    optimizer_type = config.get("optimizer", "lbfgs")  # Default to LBFGS for backward compatibility
    
    # Extract optimizer-specific parameters
    if optimizer_type == "adamw":
        lr = config.get("lr", 1e-3)
        patience = config.get("patience", 100)
        tolerance = config.get("tolerance", 5e-4)

    if config["model_type"] == "ln":
        # convert STA to a CUDA tensor
        sta_dev = torch.from_numpy(train_sta).float().to(device)

        # build & init
        model = LinearNonLinearModel(train_sta.shape).to(device)
        init_w, init_b, _ = get_initial_weight_and_bias_ln(cell_to_fit, train_dev, val_dev, train_sta)
        model.kernel.data = sta_dev * init_w.to(device)
        model.bias.data   = init_b.to(device)
    else:
        if L is None or V is None: raise ValueError("Eigenvalues or eigenvectors are None")
        eigenvecs_T = V.T.float()  # shape (D, D)
        eig_indices = config["eig_indices"]   # list of ints

        n_gens = len(eig_indices)
        input_dim = train_dev['stim'].shape[1:]
        # first fit weights & shared bias via LBFGS initializer
        w0, b0, initial_metrics = get_initial_weight_and_bias_energy(
            train_dev, val_dev,
            cell_to_fit,
            eigenvecs_T, eig_indices,
            device=device
        )

        # build the energy model
        model = EnergyModel(input_dim, n_gens).to(device)

        # slice out the same eigen-maps and init kernels + bias
        evecs = eigenvecs_T[eig_indices]              # (n_gens, D)
        evecs = evecs.view(n_gens, *input_dim).to(device)
        model.kernels.data = (evecs * w0[:, None, None, None].to(device))
        model.bias.data    = b0.to(device)

    # Common training parameters
    train_params = {
        "model": model,
        "laplacian_coeff_space": lap_space,
        "laplacian_coeff_time": lap_time,
        "local_coeff": local,
        "l1_coeff": l1,
        "cell_to_fit": cell_to_fit,
        "train_dset_loaded_centered": train_dev,
        "val_dset_loaded_centered": val_dev,
        "using_ray": True,
        "max_iter": 2000,
        "ortho_coeff": ortho_coeff
    }

    # Train with selected optimizer
    if optimizer_type == "adamw":
        metrics = train_with_adamw(
            **train_params,
            lr=lr,
            patience=patience,
            tolerance=tolerance
        )
    else:  # lbfgs
        metrics = train_with_lbfgs(**train_params)

    # Cleanup
    model.cpu()
    del model
    for k in train_dev: train_dev[k] = train_dev[k].cpu()
    for k in val_dev:   val_dev[k]   = val_dev[k].cpu()
    del train_dev
    del val_dev
    torch.cuda.empty_cache()


# ─── Sweep setup ────────────────────────────────────────────────────────────────
#%%
# Load dataset
subject = 'Allen'

# date = '2022-04-13'
# date = '2022-04-01'
# sess = get_session(subject, date)
full_session_list = []

if __name__ == '__main__': 
    full_session_list = [session for session in get_complete_sessions() if subject in session.name] #and '2022-04-13' in session.name

    # full_session_list.append(get_session(subject, '2022-04-13'))
    #add get_session(subject, '2022-04-13') to beginning of full_session_list
    # full_session_list.insert(0, get_session('Allen', '2022-04-13'))
    full_session_list =[get_session('Allen', '2022-04-13')]

    

sess = full_session_list[0]

torch.cuda.empty_cache()
# saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
gaborium_dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
gaborium_dset['stim'] = gaborium_dset['stim'].float()
gaborium_dset['stim'] = (gaborium_dset['stim'] - gaborium_dset['stim'].mean()) / 255
# gaborium_dset['stim'] = (gaborium_dset['stim'].float() - 127) / 255
#%%
eye_pos_start = 100
eye_pos_end = 1000

plt.plot(gaborium_dset['eyepos'][eye_pos_start:eye_pos_end, 0], label='x')
plt.plot(gaborium_dset['eyepos'][eye_pos_start:eye_pos_end, 1], label='y')
plt.legend()
plt.show()

dt = np.diff(gaborium_dset['t_bins'])
velocity = np.diff(gaborium_dset['eyepos'], axis=0) / dt[:, None]
# plt.plot(velocity[:, 0], label='x')
# plt.plot(velocity[:, 1], label='y')
# plt.legend()
# plt.show()

speed = np.linalg.norm(velocity[eye_pos_start:eye_pos_end, :], axis=1)
plt.plot(speed)
speed_thresh = 40
#draw horizontal line at speed_thresh
plt.axhline(speed_thresh, color='r')

plt.show()






#%%

#%%
# Define utilities
n_lags = 18
def get_inds(dset, n_lags, speed_thresh = 40):
    dpi_valid = dset['dpi_valid']
    new_trials = torch.diff(dset['trial_inds'], prepend=torch.tensor([-1])) != 0
    dfs = ~new_trials
    dfs &= (dpi_valid > 0)

    dt = np.diff(gaborium_dset['t_bins'])
    velocity = np.diff(gaborium_dset['eyepos'], axis=0) / dt[:, None]
    speed = np.linalg.norm(velocity, axis=1)

    dfs_speed = speed < speed_thresh
    dfs_speed = np.concatenate([np.zeros(1, dtype = bool), dfs_speed])
    dfs &= dfs_speed
    
    for iL in range(n_lags):
        dfs &= torch.roll(dfs, 1)
    
    dfs = dfs.float()
    dfs = dfs[:, None]
    return dfs

n_units = gaborium_dset['robs'].shape[1]
n_y, n_x = gaborium_dset['stim'].shape[1:3]
gaborium_dset['dfs'] = get_inds(gaborium_dset, n_lags)
gaborium_inds = gaborium_dset['dfs'].squeeze().nonzero(as_tuple=True)[0]

#%%
plt.plot(gaborium_dset['eyepos'][eye_pos_start:eye_pos_end, 0], label='x')
plt.plot(gaborium_dset['eyepos'][eye_pos_start:eye_pos_end, 1], label='y')
plt.legend()
# plt.show()

plt.plot(gaborium_dset['dfs'][eye_pos_start:eye_pos_end])
plt.show()
#%%
from DataYatesV1 import CombinedEmbeddedDataset

# Define which keys and lags to use
keys_lags = {
    'robs': 0,
    'stim': np.arange(n_lags),
    'dfs': 0,
}


# Split data into training and validation sets
train_val_split = 0.6
gaborium_train_inds, gaborium_val_inds = split_inds_by_trial(gaborium_dset, gaborium_inds, train_val_split, seed=1002)
print(f'Gaborium sample split: {len(gaborium_train_inds) / len(gaborium_inds):.3f} train, {len(gaborium_val_inds) / len(gaborium_inds):.3f} val')

train_dset = CombinedEmbeddedDataset([gaborium_dset], [gaborium_train_inds], keys_lags)
val_dset = CombinedEmbeddedDataset([gaborium_dset], [gaborium_val_inds], keys_lags)


# Load datasets
train_dset_loaded = train_dset[:]
val_dset_loaded = val_dset[:]

