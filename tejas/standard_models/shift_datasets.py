#%%
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
import shutil

import os
import torch
import torch.nn as nn

import wandb as wdb
from lightning.pytorch.loggers import wandb

from DataYatesV1.utils.rf import get_countor_metrics,get_gaussian_fit_metrics, \
    get_log_gabor_metrics, get_corr_metrics, Gaussian2D
from DataYatesV1.models.losses.poisson import PoissonBPSAggregator
from DataYatesV1.utils.modeling import Noise, SplitRelu
from DataYatesV1.models.losses import MaskedLoss
from DataYatesV1 import DictDataset, enable_autoreload, \
                        set_seeds, calc_sta, ensure_tensor
from DataYatesV1.utils.grid_sample import grid_sample_coords
from DataYatesV1 import calc_sta, plot_stas, is_notebook
from scipy.ndimage import gaussian_filter

from DataYatesV1.utils.modeling import StackedConv2d

import lightning as pl
from schedulefree import AdamWScheduleFree

import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np

def preprocess_dataset(dset,
            dtype = torch.float32,
            min_num_spikes = 500,
            snr_thresh = 5,
            valid_radius = 10,
            n_lags = 20,
            grid_radius = 15,
            fig_dir = None,
            verbose=1):
    
    fig_dir = Path(fig_dir) if fig_dir is not None else None
    # Convert stim to float on range (-1, 1)
    #dset['stim'] = (dset['stim'].float() - 127) / 128
    dset['stim'] = dset['stim'].float()
    print(dset['stim'].mean())
    dset['stim'] = (dset['stim'] - dset['stim'].mean()) / dset['stim'].std()

    # Convert DPI signal from pixel values to degrees (using small angle approximation)
    dpi_pix = dset['dpi_pix'].float()
    pix_per_deg = dset.metadata['ppd']
    screen_resolution = dset.metadata['screen_resolution']
    center_pix = np.flipud((screen_resolution + 1) / 2)
    dpi_deg = (dpi_pix - center_pix) / pix_per_deg
    dpi_deg[:,0] *= -1
    dpi_deg = dpi_deg[:,[1,0]]
    dset['eyepos'] = dpi_deg.float().double()

    # Create a binary mask for valid eye positions
    dfs = np.logical_and.reduce([
        np.abs(dset['eyepos'][:,0]) < valid_radius,
        np.abs(dset['eyepos'][:,1]) < valid_radius,
        dset['dpi_valid']
    ]).astype(np.float32)
    dset['dfs'] = dfs

    # Calculate STEs
    # Determine maximally responsive lag for each cluster
    stes = calc_sta(dset['stim'], dset['robs'], 
                    n_lags, dset['dfs'], device='cuda', batch_size=10000,
                    stim_modifier=lambda x: x**2, progress=True).cpu().numpy()

    # Find maximum energy lag for each cluster
    signal = np.abs(stes - np.median(stes, axis=(2,3), keepdims=True))
    sigma = [0, 2, 2, 2]
    signal = gaussian_filter(signal, sigma)
    noise = np.median(signal[:,0], axis=(1,2))
    snr_per_lag = np.max(signal, axis=(2,3)) / noise[:,None]
    cluster_lag = snr_per_lag.argmax(axis=1)

    # Shift robs to maximally responsive lag for each cluster 
    n_frames = len(dset['robs'])
    robs = []
    for iC in range(dset['robs'].shape[1]):
        lag = cluster_lag[iC]
        max_frame = n_frames + lag - n_lags
        robs.append(dset['robs'][lag:max_frame,iC])
    robs = torch.stack(robs, axis=1)

    dset.replicates = True
    dset = dset[:-n_lags]
    good_ix = np.where(np.max(snr_per_lag, axis=1) > snr_thresh)[0]
    good_ix = np.intersect1d(good_ix, np.where(dset['robs'].sum(0) > min_num_spikes)[0])

    # Plot STEs
    fig, axs = plot_stas(signal[:,:,None,:,:])
    for iC in good_ix:
        iL = cluster_lag[iC]
        x0, x1 = iL, (iL+1)
        y0, y1 = -iC-1, -iC
        axs.plot([x0, x1, x1, x0, x0], [y1, y1, y0, y0, y1], 'r-')
    if fig_dir is not None:
        fig.savefig(fig_dir / 'stes.png')
    if verbose > 1:
        plt.show()
    else:
        plt.close(fig)

    stes = stes[good_ix]
    dset['robs'] = robs[:,good_ix]
    dset.replicates = False

    # Construct grid centered on peak of spatial STEs
    n_units, _, n_y, n_x = stes.shape
    weights = (dset['robs'].sum(dim=0) / dset['robs'].sum()).cpu().numpy()
    ste_max = np.zeros((n_y, n_x))
    for iC in range(n_units):
        lag = cluster_lag[iC]
        ste_max += stes[iC, lag] * weights[iC]

    grid_center = np.array((n_x, n_y)) // 2
    grid = torch.stack(
                torch.meshgrid(
                    torch.arange(-grid_radius,grid_radius+1), 
                    torch.arange(-grid_radius,grid_radius+1),
                    indexing='xy'
                ), 
                dim=-1).float()
    grid += grid_center[None,None,:]

    print(grid[:5,:5,:])

    x_min, x_max = grid[...,0].min(), grid[...,0].max()
    y_min, y_max = grid[...,1].min(), grid[...,1].max()

    fig, axs = plt.subplots(1, 1, figsize=(6, 6))
    im = axs.imshow(ste_max, cmap='viridis')
    fig.colorbar(im, ax=axs)
    axs.plot([x_min, x_min, x_max, x_max, x_min], [y_min, y_max, y_max, y_min, y_min], color='red')
    axs.scatter([grid_center[0]], [grid_center[1]], color='red', marker='x')
    axs.set_title('STE')
    axs.set_xlabel('X (pixels)')
    axs.set_ylabel('Y (pixels)')
    if fig_dir is not None:
        fig.savefig(fig_dir / 'ste_rf.png')
    if verbose > 1:
        plt.show()
    else:
        plt.close(fig)

    for key in dset.keys():
        if torch.is_tensor(dset[key]):
            dset[key] = dset[key].to(dtype)

    # store meta data
    dset.metadata['valid_radius'] = valid_radius
    dset.metadata['grid_center'] = grid_center
    dset.metadata['snr_thresh'] = snr_thresh
    dset.metadata['min_num_spikes'] = min_num_spikes
    dset.metadata['grid'] = grid
    
    if verbose:
        print(dset)
    
    return dset

# Shifter model

class BaseFactorizedReadout(nn.Module):
    """
    Base class for factorized readouts that separates the feature mapping 
    (via a 1x1 convolution) from the spatial readout. It provides a common 
    plotting utility that visualizes the feature weights and the effective 
    spatial weights.
    """
    def __init__(self, dims, n_units, bias=True):
        super().__init__()
        self.dims = dims  # (channels, H, W)
        self.n_units = n_units
        # Common feature mapping: maps input channels to n_units.
        self.features = nn.Conv2d(dims[0], n_units, kernel_size=1, bias=False)
        if bias:
            self.bias = nn.Parameter(torch.zeros(n_units))
    
    def forward(self, x):
        # Must be implemented by subclasses.
        raise NotImplementedError("Subclasses should implement forward method.")
    
    def get_spatial_weights(self):
        # Should return a tensor of shape (n_units, H, W) representing
        # the effective spatial weights.
        raise NotImplementedError("Subclasses should implement get_spatial_weights method.")
    
    def plot_weights(self):
        """
        Plot the feature weights and the effective spatial weights in a single figure with two axes.
        The spatial weights are arranged in a grid using the imshow extent.
        """
        # Extract feature weights.
        # The feature weights are from a 1x1 conv: shape (n_units, in_channels, 1, 1)
        # Squeeze to (n_units, in_channels)
        feature_weights = self.features.weight.detach().cpu().numpy().squeeze()
        # Extract spatial weights: expected shape (n_units, H, W)
        spatial_weights = self.get_spatial_weights().detach().cpu().numpy()
        n_units = spatial_weights.shape[0]
        
        # Create a single figure with two vertically stacked axes.
        fig, axs = plt.subplots(2, 1, figsize=(6, 10))
        
        # Plot feature weights.
        axs[0].imshow(feature_weights.T, cmap='coolwarm', interpolation='none')
        axs[0].set_title('Feature Weights')
        axs[0].set_xlabel('Unit')
        axs[0].set_ylabel('Channel')
        
        # Determine grid size for spatial weights.
        n_cols = int(np.ceil(np.sqrt(n_units)))
        n_rows = int(np.ceil(n_units / n_cols))
        
        # For spatial weights, loop over each unit and display it in the correct grid position.
        for i in range(n_units):
            r = i // n_cols
            c = i % n_cols
            ws = spatial_weights[i]
            ws_max = np.abs(ws).max()
            axs[1].imshow(ws, cmap='coolwarm', extent=[c, c+1, -r, -r-1],
                          vmin=-ws_max, vmax=ws_max)
        
        axs[1].axis('off')
        axs[1].set_title('Spatial Weights')
        axs[1].set_xlim([0, n_cols])
        axs[1].set_ylim([-n_rows, 0])
        fig.tight_layout()
        return fig, axs

# -----------------------------------------------------
# Subclass 1: Nonparametric Readout (FactorizedReadout)
# -----------------------------------------------------
class NonparametricReadout(BaseFactorizedReadout):
    def __init__(self, dims, n_units, bias=True):
        super().__init__(dims, n_units, bias)
        # The spatial component is implemented as a grouped convolution.
        self.locations = nn.Conv2d(n_units, n_units, kernel_size=dims[1:], groups=n_units, bias=False)
        if bias:
            self.bias = nn.Parameter(torch.zeros(n_units), requires_grad=True)
        
        # Initialize the location weights with a Gaussian-like profile.
        self._initialize_gaussian(center=(dims[1] // 2, dims[2] // 2),
                                  radius=min(dims[1], dims[2]) // 4,
                                  sigma_scale=0.3)
        self.normalize_spatial_weights()
    
    def forward(self, x):
        x = self.features(x)  # (batch, n_units, H, W)
        x = self.locations(x)  # (batch, n_units, 1, 1) typically
        x = x.squeeze()
        if hasattr(self, 'bias'):
            x = x + self.bias[None, :]
        return x
    
    def normalize_spatial_weights(self):
        with torch.no_grad():
            # Normalize each unit's weights to unit norm
            F.normalize(self.locations.weight, p=2, dim=[1, 2, 3], out=self.locations.weight)
    
    def _initialize_gaussian(self, center, radius, sigma_scale=0.3):
        # Initialize location weights with a Gaussian shape (with slight random offsets)
        weight_shape = self.locations.weight.shape  # (n_units, 1, H, W)
        n_units, channels_per_group, n_y, n_x = weight_shape
        
        y_coords, x_coords = torch.meshgrid(
            torch.arange(n_y, dtype=torch.float32),
            torch.arange(n_x, dtype=torch.float32),
            indexing='ij'
        )
        y_center, x_center = center
        sigma = radius * sigma_scale
        
        for unit in range(n_units):
            offset_scale = radius * 0.1
            y_offset = torch.randn(1).item() * offset_scale
            x_offset = torch.randn(1).item() * offset_scale
            unit_y_center = y_center + y_offset
            unit_x_center = x_center + x_offset
            unit_gaussian = torch.exp(-((y_coords - unit_y_center) ** 2 +
                                        (x_coords - unit_x_center) ** 2) / (2 * sigma ** 2))
            unit_gaussian = unit_gaussian / unit_gaussian.sum()
            for c in range(channels_per_group):
                self.locations.weight.data[unit, c] = unit_gaussian

    def get_spatial_weights(self):
        # Return spatial weights from the locations convolution.
        return self.locations.weight.detach().cpu().squeeze(1)

# -----------------------------------------------------
# Subclass 2: Gaussian Readout
# -----------------------------------------------------
class GaussianReadout(BaseFactorizedReadout):
    def __init__(self, dims, n_units, bias=False):
        super().__init__(dims, n_units, bias)
        # Define learnable Gaussian parameters.
        center = (dims[1] / 2, dims[2] / 2)
        self.mean = nn.Parameter(torch.tensor([[center[0], center[1]]] * n_units, dtype=torch.float32))
        radius = min(dims[1], dims[2]) / 4
        self.std = nn.Parameter(torch.ones(n_units, 2, dtype=torch.float32) * (radius * 0.3))
        self.theta = nn.Parameter(torch.zeros(n_units, dtype=torch.float32))
        
        # Create a spatial grid (pixel coordinates)
        grid_y, grid_x = torch.meshgrid(
            torch.arange(dims[1], dtype=torch.float32),
            torch.arange(dims[2], dtype=torch.float32),
            indexing='ij'
        )
        grid = torch.stack([grid_y, grid_x], dim=-1)  # shape: (H, W, 2)
        self.register_buffer('grid', grid)
    
    def compute_gaussian_mask(self):
        # Clamp std to avoid division by zero.
        std = self.std.clamp(min=1e-3)
        mean = self.mean.unsqueeze(1).unsqueeze(1)  # (n_units, 1, 1, 2)
        std = std.unsqueeze(1).unsqueeze(1)           # (n_units, 1, 1, 2)
        theta = self.theta  # (n_units,)
        cos_theta = torch.cos(theta)
        sin_theta = torch.sin(theta)
        R = torch.stack([
            torch.stack([cos_theta, -sin_theta], dim=-1),
            torch.stack([sin_theta,  cos_theta], dim=-1)
        ], dim=-2)  # (n_units, 2, 2)
        
        grid = self.grid.unsqueeze(0)  # (1, H, W, 2)
        centered_grid = grid - mean     # (n_units, H, W, 2)
        rotated_grid = torch.einsum('nhwi,nij->nhwj', centered_grid, R)
        exponent = -0.5 * ((rotated_grid / std) ** 2).sum(dim=-1)
        gaussian_mask = torch.exp(exponent)
        gaussian_mask = gaussian_mask / (gaussian_mask.sum(dim=(-1,-2), keepdim=True) + 1e-8)
        return gaussian_mask
    
    def forward(self, x):
        feat = self.features(x)  # (batch, n_units, H, W)
        gaussian_mask = self.compute_gaussian_mask()  # (n_units, H, W)
        out = (feat * gaussian_mask.unsqueeze(0)).sum(dim=(-2, -1))
        if hasattr(self, 'bias'):
            out = out + self.bias
        return out
    
    def get_spatial_weights(self):
        # The effective spatial weights in this readout are given by the Gaussian mask.
        return self.compute_gaussian_mask().detach().cpu()

# -----------------------------------------------------
# Subclass 3: Hybrid Readout
# -----------------------------------------------------
class HybridReadout(BaseFactorizedReadout):
    def __init__(self, dims, n_units, bias=True):
        super().__init__(dims, n_units, bias)
        # Nonparametric spatial weights (learnable, unconstrained map).
        self.spatial_weights = nn.Parameter(.1*torch.randn(n_units, 1, dims[1], dims[2]))
        if bias:
            self.bias = nn.Parameter(torch.zeros(n_units))
            
        center = (dims[1] / 2, dims[2] / 2)
        radius = min(dims[1], dims[2]) / 3
        
        # Learnable Gaussian parameters.
        self.mean = nn.Parameter(torch.tensor([[center[0], center[1]]] * n_units, dtype=torch.float32))
        self.std = nn.Parameter(torch.ones(n_units, 2, dtype=torch.float32) * (radius * 0.3))
        self.theta = nn.Parameter(torch.zeros(n_units, dtype=torch.float32))
        
        grid_y, grid_x = torch.meshgrid(
            torch.arange(dims[1], dtype=torch.float32),
            torch.arange(dims[2], dtype=torch.float32),
            indexing='ij'
        )
        grid = torch.stack([grid_y, grid_x], dim=-1)  # (H, W, 2)
        self.register_buffer('grid', grid)
        
        self.normalize_spatial_weights()
    
    def normalize_spatial_weights(self):
        with torch.no_grad():
            self.spatial_weights.data = F.normalize(self.spatial_weights.data, p=2, dim=(1,2,3))
    
    def compute_gaussian_mask(self):
        std = self.std.clamp(min=1e-3)
        mean = self.mean.unsqueeze(1).unsqueeze(1)
        std = std.unsqueeze(1).unsqueeze(1)
        theta = self.theta
        cos_theta = torch.cos(theta)
        sin_theta = torch.sin(theta)
        R = torch.stack([
            torch.stack([cos_theta, -sin_theta], dim=-1),
            torch.stack([sin_theta,  cos_theta], dim=-1)
        ], dim=-2)
        grid = self.grid.unsqueeze(0)
        centered_grid = grid - mean
        rotated_grid = torch.einsum('nhwi,nij->nhwj', centered_grid, R)
        exponent = -0.5 * ((rotated_grid / std) ** 2).sum(dim=-1)
        gaussian_mask = torch.exp(exponent)
        gaussian_mask = gaussian_mask / (gaussian_mask.sum(dim=(-1,-2), keepdim=True) + 1e-8)
        return gaussian_mask
    
    def forward(self, x):
        feat = self.features(x)  # (batch, n_units, H, W)
        gaussian_mask = self.compute_gaussian_mask()  # (n_units, H, W)
        A = self.spatial_weights.squeeze(1)  # (n_units, H, W)
        final_weight = A * gaussian_mask  # Hybrid effective spatial weights
        out = (feat * final_weight.unsqueeze(0)).sum(dim=(-2, -1))
        if hasattr(self, 'bias'):
            out = out + self.bias
        return out
    
    def get_spatial_weights(self):
        # Return the effective spatial weights (nonparametric weights modulated by the Gaussian).
        gaussian_mask = self.compute_gaussian_mask().detach().cpu()
        A = self.spatial_weights.detach().cpu().squeeze(1)
        return A * gaussian_mask

# Main class
class MLPPixelShifter(nn.Module):
    def __init__(self, grid, 
                hidden_dims=100,
                weight_init_multiplier=1, 
                input_dim=2,
                input_bias=False,
                mode='bilinear') -> None:
        
        super(MLPPixelShifter, self).__init__()
        self.grid = nn.Parameter(grid.float(), requires_grad=False) # grid is tensor of shape (n_row, n_col, 2)
        self.hidden_dims = hidden_dims
        self.weight_init_multiplier = weight_init_multiplier
        self.set_mode(mode)

        # If hidden_dims is a scalar integer, convert it to a list with one element
        if isinstance(hidden_dims, int):
            hidden_dims = [hidden_dims]

        # Dynamically create the layers using hidden_dims list
        layers = []
        for i in range(len(hidden_dims)):
            if i == 0:
                layers.append(nn.Linear(input_dim, hidden_dims[0], bias=input_bias))
            else:
                layers.append(nn.Linear(hidden_dims[i-1], hidden_dims[i], bias=True))
            layers.append(nn.GELU())

        layers.append(nn.Linear(hidden_dims[-1], 2, bias=True))

        self.layers = nn.Sequential(*layers)

        # Initialize weights for all layers
        for layer in self.layers:
            if isinstance(layer, nn.Linear):
                layer.weight.data *= weight_init_multiplier

    def forward(self, x):
        x['stim_in'] = x['stim']
        stim = x['stim']
        if stim.ndim == 3:
            stim = stim.unsqueeze(1) # add channel dimension

        n_frames, _,n_y, n_x = stim.shape

        shift = x['eyepos']
        shift_out = self.layers(shift).squeeze(dim=1)
        grid_shift = shift_out[:, None, None, :] + self.grid[None, ...]
        _, n_y_grid, n_x_grid, _ = grid_shift.shape

        frame_grid = torch.arange(n_frames, device=self.grid.device).float() \
                          .repeat(n_y_grid, n_x_grid, 1) \
                          .permute(2, 0, 1) \
                          .unsqueeze(-1)

        sample_grid = torch.cat([grid_shift, frame_grid], dim=-1) # 1 x T x Y x X x 3 (x, y, frame)
        extent = [
                [0, n_frames - 1],
                [0, n_y - 1],
                [0, n_x - 1]
        ]

        out = grid_sample_coords(stim.permute(1,0,2,3)[None,...], # 1 x C x T x Y x X  
                                 sample_grid[None,...], 
                                 extent, 
                                 mode=self.mode,
                                 padding_mode='zeros',
                                 align_corners=True,
                                 no_grad=False)
        out = out.squeeze(dim=(0, 1))
        x['shift_out'] = shift_out
        x['stim'] = out

        return x
    def set_mode(self, mode):
        assert mode in ['nearest', 'bilinear'], 'mode must be "nearest" or "bilinear"'
        self.mode = mode

    def plot_shifts(self, x_min, x_max, y_min, y_max, image_resolution=50, quiver_resoluiton=10):

        with torch.no_grad():
            x_im = torch.linspace(x_min, x_max, image_resolution, device=self.grid.device)
            y_im = torch.linspace(y_min, y_max, image_resolution, device=self.grid.device)
            xy_im = torch.stack(
                torch.meshgrid(x_im, y_im, indexing='xy'),
                dim=-1)
                
            x_qv = torch.linspace(x_min, x_max, quiver_resoluiton, device=self.grid.device)
            y_qv = torch.linspace(y_min, y_max, quiver_resoluiton, device=self.grid.device)
            xy_qv = torch.stack(
                torch.meshgrid(x_qv, y_qv, indexing='xy'),
                dim=-1)

            shift_im = self.layers(xy_im).norm(dim=-1)
            shift_qv = self.layers(xy_qv)        
        fig, axs = plt.subplots(1,1, figsize=(6, 6))
        im = axs.imshow(shift_im.cpu(), extent=[x_min, x_max, y_min, y_max], origin='lower')
        fig.colorbar(im, ax=axs)
        axs.quiver(xy_qv[...,0].cpu(),
                   xy_qv[...,1].cpu(), 
                   shift_qv[...,1].cpu(), 
                   -shift_qv[...,0].cpu(), 
                   color='red')
        return fig, axs

# CNN model
class CNNSingleLag(nn.Module):
    def __init__(self, dims, kernel_sizes,
                channels, noise_sigmas,
                lp_pool_sizes, n_units, strides=None,
                normalize_spatial_weights=False,
                readout='Hybrid',  # Options: 'Gaussian', 'Factorized', 'Hybrid'
                fr_init = None,
                 ) -> None:
        super(CNNSingleLag, self).__init__()
        
        self.dims = dims
        self.normalize_spatial_weights = normalize_spatial_weights

        if isinstance(kernel_sizes, int):
            kernel_sizes = [kernel_sizes]
        if isinstance(channels, int):
            channels = [channels]
        assert len(kernel_sizes) == len(channels), 'kernel_sizes and channels must have the same length'
        assert len(noise_sigmas) == len(channels), 'noise_sigmas must have the same length as channels'
        assert len(lp_pool_sizes) == len(channels) - 1, 'lp_pool_sizes must have length one less than channels'
        if strides is None:
            strides = [1] * len(kernel_sizes)
        assert len(strides) == len(kernel_sizes), 'strides must have the same length as kernel_sizes'
        
        n_layers = len(kernel_sizes)
        layers = []
        for i in range(n_layers):
            in_channels = dims[0] if i == 0 else channels[i-1] * 2
            out_channels = channels[i]
            layers.append(StackedConv2d(in_channels, out_channels, kernel_sizes[i], stride=strides[i], bias=False))
            if noise_sigmas[i] > 0:
                layers.append(Noise(noise_sigmas[i]))
            layers.append(nn.BatchNorm2d(out_channels))
            layers.append(SplitRelu())
            if i < n_layers-1 and lp_pool_sizes[i] > 1:
                layers.append(nn.LPPool2d(2, (lp_pool_sizes[i], lp_pool_sizes[i]), stride=1))

        # Calculate the output dimensions taking into account kernel sizes, strides, and pooling
        h, w = dims[1], dims[2]
        for i in range(n_layers):
            h = (h - kernel_sizes[i]) // strides[i] + 1
            w = (w - kernel_sizes[i]) // strides[i] + 1
            if i < n_layers - 1 and lp_pool_sizes[i] > 1:
                h = h - lp_pool_sizes[i] + 1
                w = w - lp_pool_sizes[i] + 1

        dims_out = [channels[-1]*2, h, w]
        if readout=='Gaussian':
            readout = GaussianReadout(dims_out, n_units, bias=True)
        elif readout == 'Factorized':
            readout = NonparametricReadout(dims_out, n_units)
        elif readout == 'Hybrid':
            readout = HybridReadout(dims_out, n_units, bias=True)
            
        layers.append(readout)
        layers.append(nn.Softplus())

        self.layers = nn.Sequential(*layers)

        inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta
        if fr_init is not None:
            assert len(fr_init) == n_units, 'init_rates must have the same length as n_units'
            self.layers[-2].bias.data = inv_softplus(
                ensure_tensor(fr_init, device=self.layers[-2].bias.device)
            )


    def forward(self, x):
        stim = x['stim'].float()
        if stim.ndim == 3:  # missing channel dimension
            stim = stim.unsqueeze(1) 
        x['rhat'] = self.layers(stim).squeeze()
        return x


# utility function for plotting STAs
def plot_sta_images(sta_images, imshow_kwargs={}):
    n_units = len(sta_images)
    n_rows = np.ceil(np.sqrt(n_units)).astype(int)
    n_cols = np.ceil(n_units / n_rows).astype(int)
    fig, axs = plt.subplots(n_rows, n_cols, figsize=(8,8))
    for iU in range(n_units):
        img = sta_images[iU]
        ax = axs.flatten()[iU]
        ax.imshow(img, **imshow_kwargs)
        ax.axis('off')

    for ax in axs.flatten()[n_units:]:
        ax.axis('off')

    # reduce the distance between subplots
    fig.subplots_adjust(hspace=0.01, wspace=0.01)
    # add colorbar
    fig.tight_layout()

    return fig, axs

def soft_threshold(x, lambda_):
    """Soft thresholding operator for L1 proximal operator"""
    with torch.no_grad():
        return torch.sign(x) * torch.clamp(torch.abs(x) - lambda_, min=0)

class ShifterCNN(pl.LightningModule):
    def __init__(self, 
                 shifter_kwargs,
                 cnn_kwargs,
                 lr=1e-3, 
                 optimizer_requested='AdamW',
                 viz_frequency = 5,
                 expensive_viz_frequency = 5,
                 lambda_l1 = 1e-4,
                 warmup_steps=3000,
                 verbose=2) -> None:
        super(ShifterCNN, self).__init__()
        self.save_hyperparameters()
        self.shifter = MLPPixelShifter(**shifter_kwargs)
        self.cnn = CNNSingleLag(**cnn_kwargs)
        self.loss = MaskedLoss(nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
        self.lr = lr
        self.verbose = verbose
        self.warmup_steps = warmup_steps
        self.train_bps_aggregator = PoissonBPSAggregator()
        self.val_bps_aggregator = PoissonBPSAggregator()

    def forward(self, x):
        x = self.shifter(x)
        x = self.cnn(x)
        return x

    def training_step(self, batch, batch_idx):
        x = self.shifter(batch)
        x = self.cnn(x)
        
        loss = self.loss(x)
        # check if loss is nan and exit if it is
        if torch.isnan(loss).any():
            # graceful exit by simulating a KeyboardInterrupt
            print("Loss is NaN, exiting training.")
            raise KeyboardInterrupt

        # Anchor shifter by adding a regularization term
        shift = batch['shift_out'].mean()
        loss += 1e-3 * shift**2

        self.log('train_loss', loss.item(), batch_size=len(batch), prog_bar=True)
        self.train_bps_aggregator(batch)
        return loss

    def configure_optimizers(self):
        if self.hparams['optimizer_requested'] == 'AdamW':
            optimizer = torch.optim.AdamW(self.parameters(), lr=self.lr, weight_decay=1e-3)
        elif self.hparams['optimizer_requested'] == 'AdamWScheduleFree':
            optimizer = AdamWScheduleFree(self.parameters(), lr=self.lr, warmup_steps=self.warmup_steps)
        elif self.hparams['optimizer_requested'] == 'Adam':
            optimizer = torch.optim.Adam(self.parameters(), lr=self.lr)
        else:
            raise ValueError(f"Optimizer {self.optimizer_requested} not recognized")
        return optimizer
    
    def on_train_epoch_start(self):
        super().on_train_epoch_start()
        optimizer = self.optimizers()
        self.train_bps_aggregator.reset()
        if isinstance(optimizer, AdamWScheduleFree):
            optimizer.train()

    def on_train_start(self):
        '''
        We want to log the visualizations for the model 
        before it starts training.
        '''
        super().on_train_start()
        self.log_state(run_viz=True,
                       run_expensive_viz=True)
    
    def on_train_end(self):
        super().on_train_end()
        self.log_state(run_viz=True,
                       run_expensive_viz=True)
    
    def on_train_epoch_end(self):
        super().on_train_epoch_end()
        
        bps = self.train_bps_aggregator.closure()
        self.log('train_bps', bps.mean().item(), prog_bar=True)
        self.train_bps_aggregator.reset()

    def on_validation_epoch_start(self):
        super().on_validation_epoch_start()
       
        # Setup the optimizer for evaluation
        optimizer = self.optimizers()
        if isinstance(optimizer, AdamWScheduleFree):
            optimizer.eval()
            
        self.val_bps_aggregator.reset()

    def on_validation_epoch_end(self):
        '''
        This is where all the logging and visualization code goes.

        It's pretty heavy so we have a flag to only run it every N epochs.
        viz_frequency
        '''
        super().on_validation_epoch_end()
        bps = self.val_bps_aggregator.closure()
        self.log('val_bps', bps.mean().item(), prog_bar=True)
        self.val_bps_aggregator.reset()


        run_viz = (self.current_epoch % self.hparams['viz_frequency'] == 0) or (self.current_epoch == 0)
        run_expensive_viz = (self.current_epoch % self.hparams['expensive_viz_frequency'] == 0) or (self.current_epoch == 0)
        # if run_viz:
        self.log_state(run_viz = run_viz,
                        run_expensive_viz = run_expensive_viz)
    
    def log_state(self,
                run_viz = True,
                run_expensive_viz = True):
        '''
        This function will log the state of the model.
        It allows it to be done on both on_fit_start and on_validation_epoch_end
        '''

        if not run_viz:
            return
        global dset
        # Only run visualizations every N epochs (e.g., every 5 epochs)
        shifter_training = self.shifter.training
        self.shifter.eval()
        
        optimizer = self.optimizers()
        if isinstance(optimizer, AdamWScheduleFree):
            optimizer.train()

        if hasattr(self, 'trainer'):
            checkpoint_dir = self.trainer.checkpoint_callback.dirpath
            # make directory if it doesn't exist
            os.makedirs(checkpoint_dir, exist_ok=True)
            epoch = self.current_epoch
            save_fig = True
        else:
            save_fig = False

        # LOGGING
        log_dict = {}

        fig, axs = self.shifter.plot_shifts(-dset.metadata['valid_radius'], dset.metadata['valid_radius'], -dset.metadata['valid_radius'], dset.metadata['valid_radius'])
        if save_fig and fig is not None:
            fig_path = os.path.join(checkpoint_dir, f'shifts_{epoch}.png')
            fig.savefig(fig_path)
            log_dict['shifts'] = wdb.Image(fig_path, caption="shifts")
        else:
            if self.verbose > 1:
                plt.show()
            else:
                plt.close(fig)

        # Plot weights from layers
        for i, layer in enumerate(self.cnn.layers):
            if hasattr(layer, 'plot_weights'):
                fig, axs = layer.plot_weights()
                if save_fig and fig is not None:
                    fig_path = os.path.join(checkpoint_dir, f'weights{i}_{epoch}.png')
                    fig.savefig(fig_path)
                    log_dict[f'weights{i}'] = wdb.Image(fig_path, caption=f"weights{i}")
                else:
                    if self.verbose > 1:
                        plt.show()
                    else:
                        plt.close()
             
        
        # Shift to generate STAs and STEs
        # Only run the most expensive operations at even lower frequency
        if run_expensive_viz:
            with torch.no_grad():
                torch.cuda.empty_cache()
                self.shifter.set_mode('nearest')
                shifter_device = self.shifter.grid.device
                loader = torch.utils.data.DataLoader(dset, batch_size=64, shuffle=False)
                shifted_stimulus = []
                for batch in loader:
                    batch = {k: v.to(shifter_device) for k, v in batch.items()}
                    batch = self.shifter(batch)
                    shifted_stimulus.append(batch['stim'].detach().cpu())
                
                shifted_stimulus = torch.cat(shifted_stimulus, dim=0).detach().cpu()
                stas = calc_sta(shifted_stimulus.detach().cpu(), dset['robs'].cpu(), [0], dfs=dset['dfs'].cpu(), progress=True).cpu().squeeze().numpy()

                imshow_kwargs = {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}
                stas_norm = stas / np.max(np.abs(stas), axis=(1,2), keepdims=True)
                fig, axs = plot_sta_images(stas_norm, imshow_kwargs)
                for i, ax in enumerate(axs.flatten()):
                    ax.set_title(f'Unit {i}', fontsize=8, pad=2)
                    if i == len(stas) - 1:
                        break
                if save_fig:
                    fig_path = os.path.join(checkpoint_dir, f'stas_{epoch}.png')
                    fig.savefig(fig_path)
                    log_dict['stas'] = wdb.Image(fig_path, caption="stas")
                else:
                    if self.verbose > 1:
                        plt.show()
                    else:
                        plt.close(fig)

                torch.cuda.empty_cache()

                stes = calc_sta(shifted_stimulus.detach().cpu(), dset['robs'].cpu(), [0], dfs=dset['dfs'].cpu(), stim_modifier=lambda x: x**2, progress=True).cpu().squeeze().numpy()

                imshow_kwargs = {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}
                stes_norm = 2 * (stes - np.min(stes, axis=(1,2), keepdims=True)) / np.ptp(stes, axis=(1,2), keepdims=True) - 1
                fig, axs = plot_sta_images(stes_norm, imshow_kwargs)
                for i, ax in enumerate(axs.flatten()):
                    ax.set_title(f'Unit {i}', fontsize=8, pad=2)
                    if i == len(stes) - 1:
                        break
                if save_fig:
                    fig_path = os.path.join(checkpoint_dir, f'stes_{epoch}.png')
                    fig.savefig(fig_path)
                    log_dict['stes'] = wdb.Image(fig_path, caption="stes")
                else:
                    if self.verbose > 1:
                        plt.show()
                    else:
                        plt.close(fig)

                rf_dir = os.path.join(checkpoint_dir, 'rf_metrics')
                # make directory if it doesn't exist
                os.makedirs(rf_dir, exist_ok=True)
                try:
                    contour_path = os.path.join(checkpoint_dir, f'rf_metrics/contour_{epoch}.png')
                    metrics_contour = get_countor_metrics(stas, stes,
                                                        save_fig_path=contour_path)
                    log_dict['contour ste'] = wdb.Image(contour_path, caption="contour ste")
                    
                    corr_path_ste = os.path.join(checkpoint_dir, f'rf_metrics/corr_ste_{epoch}.png')
                    corr_path_sta = os.path.join(checkpoint_dir, f'rf_metrics/corr_sta_{epoch}.png')
                    metrics_corr = get_corr_metrics(stas, stes,
                                                    save_fig_path_ste=corr_path_ste,
                                                    save_fig_path_sta=corr_path_sta)
                    log_dict['correlation ste'] = wdb.Image(corr_path_ste, caption="correlation ste")
                    log_dict['correlation sta'] = wdb.Image(corr_path_sta, caption="correlation sta")

                    # log_dict['simple_cell_contour_snr_average'] = np.mean([metrics_contour[cell]['snr_value'] for cell in simple_cell_idx])
                    log_dict['contour_snr_average'] = np.mean([metrics_contour[cell]['snr_value'] for cell in range(len(metrics_contour))])
                    
                    # log_dict['simple_cell_sta_corr_average'] = np.mean([metrics_corr[cell]['sta_corr_value'] for cell in simple_cell_idx])
                    log_dict['sta_corr_average'] = np.mean([metrics_corr[cell]['sta_corr_value'] for cell in range(len(metrics_corr))])

                    # log_dict['simple_cell_ste_corr_average'] = np.mean([metrics_corr[cell]['ste_corr_value'] for cell in simple_cell_idx])
                    log_dict['ste_corr_average'] = np.mean([metrics_corr[cell]['ste_corr_value'] for cell in range(len(metrics_corr))])
                except:
                    print('Error in RF metrics')

                self.shifter.set_mode('bilinear')
                self.shifter.to(shifter_device)
                torch.cuda.empty_cache()

        self.logger.experiment.log(log_dict) # log the images to wandb
        if shifter_training:
            self.shifter.train()     

    def optimizer_step(self, epoch, batch_idx, optimizer, optimizer_closure):
        super().optimizer_step(epoch, batch_idx, optimizer, optimizer_closure)
        
        if self.cnn.normalize_spatial_weights:
            for layer in self.cnn.layers:
                if hasattr(layer, 'normalize_spatial_weights'):
                    layer.normalize_spatial_weights()

        # Apply proximal operator for L1 after the gradient step. TODO: move to constructor and make optional
        targets = ['features', 'locations']
        with torch.no_grad():
            for name, param in self.named_parameters():
                if any([t in name for t in targets]):
                    param.copy_(soft_threshold(param, self.hparams['lambda_l1'] * optimizer.param_groups[0]['lr']))

    def validation_step(self, batch, batch_idx):
        x = self.shifter(batch)
        x = self.cnn(x)
        loss = self.loss(x)
        self.log('val_loss', loss.item(), batch_size=len(batch), prog_bar=True)
        self.val_bps_aggregator(batch)
        return loss

import sys
class MyProgressBar(pl.pytorch.callbacks.TQDMProgressBar):
    def init_validation_tqdm(self):
        bar = super().init_validation_tqdm()
        if not sys.stdout.isatty():
            bar.disable = True
        return bar

    def init_predict_tqdm(self):
        bar = super().init_predict_tqdm()
        if not sys.stdout.isatty():
            bar.disable = True
        return bar

    def init_test_tqdm(self):
        bar = super().init_test_tqdm()
        if not sys.stdout.isatty():
            bar.disable = True
        return bar

def run_shifter(session_dir, args):
    '''
    Run the shifter model on the given session directory.

    Parameters
    ----------
    session_dir : str or Path
        Path to the session directory.
    args : Namespace
        Arguments from the command line.

    Returns
    -------
    None

    '''
    session_dir = Path(session_dir)
    print(f'Fitting shifter to session {session_dir.name}')
    dset_dir = session_dir / 'datasets'
    if not dset_dir.exists():
        print(f'Dataset directory {dset_dir} does not exist. Exiting.')
        return
    save_dir = session_dir / 'shifter'
    save_dir.mkdir(exist_ok=True)

    preprocess_params = {
        'dtype': torch.float32,
        'min_num_spikes': args.min_spikes,
        'snr_thresh': args.snr_thresh,
        'valid_radius': args.valid_radius,
        'n_lags': args.n_lags,
        'grid_radius': args.grid_radius,
        'fig_dir': save_dir,
        'verbose': args.verbose,
    }

    f_gaborium = dset_dir / 'gaborium.dset'
    if not f_gaborium.exists():
        print(f'Gaborium dataset {f_gaborium} does not exist. Exiting.')
        return
    print(f'Loading dataset {f_gaborium}')
    global dset
    dset = DictDataset.load(f_gaborium)
    dset = preprocess_dataset(dset, **preprocess_params)

    # find shifter checkpoint if it exists
    shifters = list(save_dir.glob('**/epoch*.ckpt'))
    if len(shifters) > 0 and not args.force_fit:
        print(f'Found shifter checkpoint {shifters[0]}. Loading.')
        model = ShifterCNN.load_from_checkpoint(shifters[0])
    else:
        set_seeds(1002)
        # Data loaders
        train_set, val_set = torch.utils.data.random_split(dset, [.8, .2])
        train_loader = torch.utils.data.DataLoader(train_set, batch_size=args.batch_size, shuffle=True, num_workers=os.cpu_count(), pin_memory=True)
        val_loader = torch.utils.data.DataLoader(val_set, batch_size=args.batch_size, shuffle=False, num_workers=os.cpu_count(), pin_memory=True)

        n_units = dset['robs'].shape[1]
        n_spikes = torch.zeros(n_units)
        n_bins = torch.zeros(n_units)
        for batch in train_loader:
            n_spikes += batch['robs'].sum(dim=0)
            n_bins += batch['robs'].shape[0]
        baseline_fr = n_spikes / n_bins


        # shifter params
        shifter_kwargs = {
            'grid': dset.metadata['grid'],
            'weight_init_multiplier': 1,
            'hidden_dims': [400],
            'input_bias': True
        }
        # CNN params
        cnn_kwargs = {
            'dims': (1, dset.metadata['grid'].shape[0], dset.metadata['grid'].shape[1]),
            'kernel_sizes': [15, 9],
            'channels': [16, 16],
            'noise_sigmas': [0.01, 0.01],
            'lp_pool_sizes': [5],
            'n_units': n_units,
            'readout': args.readout,
            'normalize_spatial_weights': False,
            'fr_init': baseline_fr,
        }
        # Model params
        model_kwargs = {
            'shifter_kwargs': shifter_kwargs,
            'cnn_kwargs': cnn_kwargs,
            'lr': args.lr,
            'lambda_l1': args.l1,
            'optimizer_requested': args.optimizer,
            'warmup_steps': 1000,
            'viz_frequency': args.viz_frequency,
            'expensive_viz_frequency': args.viz_frequency,
            'verbose': args.verbose,
        }
        model = ShifterCNN(**model_kwargs)

        # Train
        name = f'{session_dir.name} mlp shifter {shifter_kwargs["hidden_dims"]} lr {args.lr} batch size {args.batch_size} grid_radius {preprocess_params["grid_radius"]} {args.readout} readout' 

        default_root_dir = save_dir

        trainer_kwargs = {
            "callbacks": [
                pl.pytorch.callbacks.EarlyStopping(monitor='val_loss', patience=args.patience, verbose=True, mode='min'),
                pl.pytorch.callbacks.ModelCheckpoint(monitor='val_loss', mode='min', save_last=True, save_top_k=1),
                MyProgressBar()
            ],
            "accelerator": "cpu" if args.device < 0 else "gpu",
            "logger": wandb.WandbLogger(
                project='Shifter',
                name=name, save_code=True, entity='yateslab',
                config=model_kwargs,
                save_dir=save_dir,
            ),
            "default_root_dir": default_root_dir,
            "gradient_clip_val": 50.0,
            "max_epochs": args.max_epochs,
            "num_sanity_val_steps": 0,
            "devices": [args.device],
            "precision": "32-true",
        }
        trainer_kwargs["logger"].watch(model, log="all")
        trainer = pl.Trainer(**trainer_kwargs)

        # Train the model
        trainer.fit(model, val_dataloaders=val_loader, train_dataloaders=train_loader)
        trainer.logger.experiment.finish()

    # Shift all datasets 
    shifter = model.shifter
    shifter.eval()
    shifter.cpu()
    shifter.set_mode('nearest')
    datasets = list(dset_dir.glob('*.dset'))
    for f_dset in datasets:
        print(f"Shifting {f_dset.name}")
        dset = DictDataset.load(f_dset)

        # Convert DPI signal from pixel values to degrees (using small angle approximation)
        dpi_pix = dset['dpi_pix'].float()
        pix_per_deg = dset.metadata['ppd']
        screen_resolution = dset.metadata['screen_resolution']
        center_pix = np.flipud((screen_resolution + 1) / 2)
        dpi_deg = (dpi_pix - center_pix) / pix_per_deg
        dpi_deg[:,0] *= -1
        dpi_deg = dpi_deg[:,[1,0]]
        dset['eyepos'] = dpi_deg.float()

        dset['stim'] = dset['stim'].float()
        dset = shifter(dset)
        dset['stim'] = dset['stim'].type(torch.uint8)
        # Also shift 'stim_phase' if it exists (i.e. a gratings dataset)
        if 'stim_phase' in dset:
            stim = dset['stim'] 
            dset['stim'] = dset['stim_phase'].float()
            dset = shifter(dset)
            dset['stim_phase'] = dset['stim']
            dset['stim'] = stim
        del dset.covariates['stim_in']
        f_out = save_dir / (f_dset.stem + '_shifted.dset')
        dset.save(f_out)

if __name__ == '__main__':
    # potential speedup for matmul
    torch.set_float32_matmul_precision('medium')

    # parse input args
    import argparse
    parser = argparse.ArgumentParser(description='Train a ShifterCNN model on gaborium data and shift all datasets for a session.')
    parser.add_argument('--base_dir', type=str, default='/mnt/ssd/YatesMarmoV1/processed',)
    parser.add_argument('--session_name', type=str, default='Allen_2022-04-13', 
                        help='Session name for the dataset (Subject_YYYY-MM-DD). Can use wildcards.')
    parser.add_argument('--verbose', type=int, default=1, help='Verbosity level (0: silent, 1: std out, 2: plots).')
    parser.add_argument('--device', type=int, default=0, help='GPU index to use (default: 0). Use -1 for CPU.')
    parser.add_argument('--readout', type=str, default='Gaussian', help='Readout type (Gaussian, Factorized, Hybrid).')
    parser.add_argument('--n_lags', type=int, default=20, help='Number of lags to use for the dataset.')
    parser.add_argument('--grid_radius', type=int, default=25, help='Sample grid radius to use for the dataset.')
    parser.add_argument('--min_spikes', type=int, default=500, help='Minimum number of spikes for cluster to be used in fitting.')
    parser.add_argument('--snr_thresh', type=float, default=5, help='SNR threshold for cluster to be used in fitting.')
    parser.add_argument('--valid_radius', type=int, default=10, help='Radius for DPI sample inclusion.')
    parser.add_argument('--optimizer', type=str, default='AdamW', help='Optimizer to use (AdamW, Adam, AdamWScheduleFree).')
    parser.add_argument('--l1', type=float, default=1e-4, help='L1 regularization strength.')
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate for the optimizer.')
    parser.add_argument('--batch_size', type=int, default=128, help='Batch size for training.')
    parser.add_argument('--max_epochs', type=int, default=100, help='Maximum number of epochs to train for.')   
    parser.add_argument('--patience', type=int, default=5, help='Patience for early stopping.')
    parser.add_argument('--viz_frequency', type=int, default=5, help='Frequency of visualization (every N epochs).')
    parser.add_argument('--force_fit', type=bool, default=False, help='Force fitting even if shifter exists.')

    #check if in ipython notebook environment
    if is_notebook():
        enable_autoreload()
        #set the args if in ipython notebook
        args = parser.parse_args(args=[])
        # args.session_name = 'Allen_2022-04-13'
        args.session_name = '*'
        args.verbose = 2
        args.device = 0
        args.readout = 'Gaussian'
    else:
        args = parser.parse_args()
#%%
session_dir = Path('/mnt/ssd/YatesMarmoV1/processed/Allen_2022-04-13')
print(f'Fitting shifter to session {session_dir.name}')
dset_dir = session_dir / 'datasets'
if not dset_dir.exists():
    print(f'Dataset directory {dset_dir} does not exist. Exiting.')
    raise FileNotFoundError(f'Dataset directory {dset_dir} does not exist. Exiting.')
save_dir = session_dir / 'shifter'
save_dir.mkdir(exist_ok=True)

preprocess_params = {
    'dtype': torch.float32,
    'min_num_spikes': args.min_spikes,
    'snr_thresh': args.snr_thresh,
    'valid_radius': args.valid_radius,
    'n_lags': args.n_lags,
    'grid_radius': args.grid_radius,
    # 'fig_dir': save_dir,
    'fig_dir': None,
    'verbose': args.verbose,
}

f_gaborium = dset_dir / 'gaborium.dset'
if not f_gaborium.exists():
    print(f'Gaborium dataset {f_gaborium} does not exist. Exiting.')
    raise FileNotFoundError(f'Gaborium dataset {f_gaborium} does not exist. Exiting.')
print(f'Loading dataset {f_gaborium}')
global dset
dset = DictDataset.load(f_gaborium)
dset = preprocess_dataset(dset, **preprocess_params)
#
# find shifter checkpoint if it exists
shifters = list(save_dir.glob('**/epoch*.ckpt'))
model = ShifterCNN.load_from_checkpoint(shifters[0])
 # Shift all datasets 
shifter = model.shifter
shifter.eval()
shifter.cpu()
shifter.set_mode('nearest')
#%%
shifter.plot_shifts(-dset.metadata['valid_radius'], dset.metadata['valid_radius'], -dset.metadata['valid_radius'], dset.metadata['valid_radius'])
#%%
pre_shift_dset = dset.copy()
#%%
datasets = list(dset_dir.glob('*.dset'))
datasets = [d for d in datasets if 'backimage' in d.name]
for f_dset in datasets:
    print(f"Shifting {f_dset.name}")
    dset = DictDataset.load(f_dset)
    pre_shift_dset = dset.copy()

    # Convert DPI signal from pixel values to degrees (using small angle approximation)
    dpi_pix = dset['dpi_pix'].float()
    pix_per_deg = dset.metadata['ppd']
    screen_resolution = dset.metadata['screen_resolution']
    center_pix = np.flipud((screen_resolution + 1) / 2)
    dpi_deg = (dpi_pix - center_pix) / pix_per_deg
    dpi_deg[:,0] *= -1
    dpi_deg = dpi_deg[:,[1,0]]
    dset['eyepos'] = dpi_deg.float()

    dset['stim'] = dset['stim'].float()
    dset = shifter(dset)
    dset['stim'] = dset['stim'].type(torch.uint8)
    # Also shift 'stim_phase' if it exists (i.e. a gratings dataset)
    if 'stim_phase' in dset:
        stim = dset['stim'] 
        dset['stim'] = dset['stim_phase'].float()
        dset = shifter(dset)
        dset['stim_phase'] = dset['stim']
        dset['stim'] = stim
    del dset.covariates['stim_in']
    f_out = save_dir / (f_dset.stem + '_shifted.dset')

   
    max_valid_shift = (pre_shift_dset['stim'].shape[-1] - 1) / 2 - args.grid_radius
    invalid_shift_indices = torch.where((torch.abs(dset['shift_out']) > max_valid_shift).any(dim=1))[0]
    print(f'{len(invalid_shift_indices)} invalid shifts found out of {len(dset)}')
    print(f'percentage of invalid shifts in {f_dset.name}: {len(invalid_shift_indices) / len(dset) * 100}%')

    # dset.save(f_out)
#%%
#find where dset['shift_out'] is greater than 0.5 of pre_shift_dset['stim'] shape 
max_valid_shift = (pre_shift_dset['stim'].shape[-1] - 1) / 2 - args.grid_radius
invalid_shift_indices = torch.where((torch.abs(dset['shift_out']) > max_valid_shift).any(dim=1))[0]
print(f'{len(invalid_shift_indices)} invalid shifts found out of {len(dset)}')
print(f'percentage of invalid shifts in {f_dset.name}: {len(invalid_shift_indices) / len(dset) * 100}%')

#show random stim from invalid shifts
# random_invalid_shift_idx =  invalid_shift_indices[torch.randint(0, len(invalid_shift_indices), (1,)).item()]
for random_invalid_shift_idx in invalid_shift_indices[:10]:
    
    if not (dset['stim'][random_invalid_shift_idx] == dset['stim'][random_invalid_shift_idx][0]).all():
        fig, axs = plt.subplots(1, 2, figsize=(10, 5))
        axs[0].set_title('Pre-shift')
        axs[0].imshow(pre_shift_dset['stim'][random_invalid_shift_idx], cmap='gray', vmin=0, vmax=255)
        axs[1].set_title('Post-shift')
        axs[1].imshow(dset['stim'][random_invalid_shift_idx], cmap='gray', vmin=0, vmax=255)
        #add a colorbar
        cbar = plt.colorbar(axs[0].imshow(pre_shift_dset['stim'][random_invalid_shift_idx], cmap='gray', vmin=0, vmax=255), ax=axs[0])
        cbar.set_label('Stimulus value')
        cbar = plt.colorbar(axs[1].imshow(dset['stim'][random_invalid_shift_idx], cmap='gray', vmin=0, vmax=255), ax=axs[1])
        cbar.set_label('Stimulus value')
        plt.show()

        print(f'shift_out (pix): {dset["shift_out"][random_invalid_shift_idx].detach()}')
        print(f'eyepos (deg): {dset["eyepos"][random_invalid_shift_idx]}')

#%%