#%%

#%%
import os
from typing import Sequence
from DataYatesV1 import plot_stas
model_to_train = 'ln'

if __name__ == "__main__":
    # os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
    num_gpus = len(os.environ.get("CUDA_VISIBLE_DEVICES").split(","))
    # assert num_gpus == 2


#just get model stes and stas
#indicies for complex cells vs simple cells (look jake's supp from 2023 paper)
#make sure to save fits on directory yatesmarmov1 in subfolders
    import ray
    from ray import tune
    from ray.tune.schedulers import ASHAScheduler
    from ray.tune import JupyterNotebookReporter
    from ray.tune.search.optuna import OptunaSearch
    import optuna

    ray.init(num_cpus=64, num_gpus=num_gpus, include_dashboard=False, log_to_driver=False)



import torch
from torch import nn
from torch.nn import functional as F

import numpy as np
import matplotlib.pyplot as plt 
from tqdm import tqdm
import json
from copy import deepcopy
import gc
# Library imports
from DataYatesV1 import DictDataset, enable_autoreload, get_session, set_seeds, calc_sta, get_complete_sessions
from DataYatesV1.utils.basic_shifter import plot_sta_images
# from DataYatesV1.utils.modeling import MaskedLoss
from DataYatesV1.models.losses.poisson import MaskedLoss
from DataYatesV1.models.losses.poisson import PoissonBPSAggregator
from DataYatesV1.utils.rf import Gaussian2D
from DataYatesV1.utils import split_inds_by_trial_train_val_test, split_inds_by_trial

# Set environment
torch.set_float32_matmul_precision('medium')  # potential speed up
set_seeds(1002)
enable_autoreload()

verbosity = 0



#%%
def get_processed_dset(dset,
                       rf,
                       dim_for_centering=30, 
                       lags_to_use=None,
                       ):
    """Process a dataset by centering it on the RF and optionally selecting specific lags."""
    assert len(dset['stim'].shape) == 4

    if lags_to_use is None:
        lags_to_use = np.arange(dset['stim'].shape[1])
    
    # Check that lags_to_use is iterable
    assert hasattr(lags_to_use, '__iter__')

    # Center the dataset stim location on the RF
    x_min, x_max = int(rf.x0 - dim_for_centering/2), int(rf.x0 + dim_for_centering/2)
    y_min, y_max = int(rf.y0 - dim_for_centering/2), int(rf.y0 + dim_for_centering/2)
    dset_new = deepcopy(dset)

    #check that x_min, x_max, y_min, y_max are within bounds
    checks = [x_min >= 0, 
              x_max <= dset['stim'].shape[3], 
              y_min >= 0, 
              y_max <= dset['stim'].shape[2],
              x_max - x_min == dim_for_centering,
               y_max - y_min == dim_for_centering,]
    assert all(checks)


    # assert x_max - x_min == dim_for_centering and y_max - y_min == dim_for_centering

    dset_new['stim'] = dset_new['stim'][:, lags_to_use, y_min:y_max, x_min:x_max]
    return dset_new


#%%
def get_sta_with_lags(dset):
    """Calculate STAs for each lag in the dataset."""

    n_lags = dset['stim'].shape[1]
    stas = []
    for lag in tqdm(range(n_lags)):
        sta = calc_sta(dset['stim'][:, lag].detach().cpu(), 
                      dset['robs'].cpu(), 
                      [0], 
                      dfs=dset['dfs'].cpu().squeeze(), 
                      progress=False).cpu().squeeze().numpy()
        stas.append(sta)
    stas = np.stack(stas, axis=1)

    
    return stas
def get_cached_sta_with_lags(dset, rf, train_val_split, dim_for_centering):
    
    file_name = f'sta_ste_precomputed/{sess.name}/stas_centered_{dim_for_centering}_x_{int(rf.x0)}_y_{int(rf.y0)}_split_{train_val_split}.npy'
    # if os.path.exists(file_name):
    #     stas = np.load(file_name)
    # else:
    os.makedirs(os.path.dirname(file_name), exist_ok=True)
    stas = get_sta_with_lags(dset)
    np.save(file_name, stas)
    return stas

#%%
def get_dataset_for_cell(cell_to_fit, train_val_split, dim_for_centering = 30):
    sta_single_cell = stas[cell_to_fit]
    ste_single_cell = stes[cell_to_fit]
    peak_lag = np.argmax(ste_single_cell.max(axis=(1,2)))


    if verbosity > 1:
        stas_norm = sta_single_cell / np.max(np.abs(sta_single_cell), axis=(1,2), keepdims=True)
        fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}, title_prefix='lag')
        plt.show()


        stes_norm = 2 * (ste_single_cell - np.min(ste_single_cell, axis=(1,2), keepdims=True)) / np.ptp(ste_single_cell, axis=(1,2), keepdims=True) - 1
        fig, axs = plot_sta_images(stes_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}, title_prefix='lag')
        plt.show()
    # Fit a 2D Gaussian to the RF
    cell_ste = ste_single_cell[peak_lag]
    n_y, n_x = cell_ste.shape
    rf = Gaussian2D(*Gaussian2D.est_p0(cell_ste))
    rf.fit(cell_ste)
    if verbosity > 1:
        plt.imshow(cell_ste, cmap='coolwarm')
        plt.colorbar()
        plt.scatter(rf.x0, rf.y0, c='r', s=100)
        plt.show()

    
    try:
        x_min, x_max = int(rf.x0 - dim_for_centering/2), int(rf.x0 + dim_for_centering/2)
        y_min, y_max = int(rf.y0 - dim_for_centering/2), int(rf.y0 + dim_for_centering/2)
    except:
        rf.x0 = train_dset_loaded['stim'].shape[3]//2
        rf.y0 = train_dset_loaded['stim'].shape[2]//2
        print('Using center for stim')
        x_min, x_max = int(rf.x0 - dim_for_centering/2), int(rf.x0 + dim_for_centering/2)
        y_min, y_max = int(rf.y0 - dim_for_centering/2), int(rf.y0 + dim_for_centering/2)

     #check that x_min, x_max, y_min, y_max are within bounds
    # checks = [x_min >= 0, x_max <= train_dset_loaded['stim'].shape[3], y_min >= 0, y_max <= train_dset_loaded['stim'].shape[2]]
    checks = [x_min >= 0, 
              x_max <= train_dset_loaded['stim'].shape[3], 
              y_min >= 0, 
              y_max <= train_dset_loaded['stim'].shape[2],
              x_max - x_min == dim_for_centering,
               y_max - y_min == dim_for_centering,]
    if not all(checks):
        rf.x0 = train_dset_loaded['stim'].shape[3]//2
        rf.y0 = train_dset_loaded['stim'].shape[2]//2
        print('Using center for stim')
    train_dset_loaded_centered = get_processed_dset(train_dset_loaded, rf, dim_for_centering=dim_for_centering)
    val_dset_loaded_centered = get_processed_dset(val_dset_loaded, rf, dim_for_centering=dim_for_centering)
    test_dset_loaded_centered = get_processed_dset(test_dset_loaded, rf, dim_for_centering=dim_for_centering)

    train_dset_stas = get_cached_sta_with_lags(train_dset_loaded_centered, rf, train_val_split, dim_for_centering=dim_for_centering)

    if verbosity > 1:
        sta_single_cell = train_dset_stas[cell_to_fit]
        stas_norm = sta_single_cell / np.max(np.abs(sta_single_cell), axis=(1,2), keepdims=True)
        fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1}, title_prefix='lag')
        plt.show()



    train_sta = train_dset_stas[cell_to_fit]

    return train_dset_loaded_centered, val_dset_loaded_centered, test_dset_loaded_centered, train_sta


#%%
#we need 0 mean for sta and stc computation
#fix the caching
def get_eigenvalues_and_vectors(train_data, cid, cache = False):
    # prepare cache directories
   
    cell_dir = f'STC_cache/{sess.name}/cell_{cid}'
    #check if cell_dir exists
    if os.path.exists(cell_dir) and cache:
        raise ValueError(f'There is a bug in the caching, the code with caching has a train val leak. Train val split must be accounted for in the cache name (which is not done right now).')
        print(f'Cell {cid} already exists, skipping')
        #load L and V
        L = torch.load(f'{cell_dir}/L.pt')
        V = torch.load(f'{cell_dir}/V.pt')
        # return L, V
    else:
        try:
            os.makedirs(cell_dir, exist_ok=True)

            robs = train_data['robs'][:, cid]
            # train_data['stim'] = train_data['stim'].cuda()
            # robs = robs.cuda()
            dims = train_data['stim'].shape[-3:]
            sta = torch.einsum('t, tlyx->lyx', robs, train_data['stim']) / robs.sum()

            ste = torch.einsum('t, tlyx->lyx', robs, train_data['stim']**2) / robs.sum()
            ste = ste.to('cpu')

            T    = train_data['stim'].shape[0]
            dims = train_data['stim'].shape[1:]
            D    = int(np.prod(dims))

            stim_flat = (train_data['stim'] - sta[None]).view(T, D)   # shape (T, D)
            # 2) get your weights as a column vector
            w = robs.float().unsqueeze(1)   # shape (T, 1)
            # 3) do the weighted outer‐sum in one go
            Cov = stim_flat.t() @ (w * stim_flat)
            Cov = Cov / (w.sum() - 1)

            Cov_cpu = Cov.cpu().double()


            # singular_vals = torch.linalg.eigvalsh(Cov, UPLO='U')

            # U, L, V = torch.linalg.svd(Cov)

            # U = U.to('cpu')
            # L = L.to('cpu')
            # V = V.to('cpu')
            # Cov = Cov.to('cpu')
            # sta = sta.to('cpu')

            # --- now plot & save the covariance matrix ---
            plt.figure()
            plt.imshow(Cov_cpu, cmap='gray')
            plt.colorbar()
            plt.title(f'Cell {cid}: Covariance Matrix')
            # plt.savefig(f'{cell_dir}/covariance_matrix.png')
            if verbosity > 0: plt.show()
            plt.close()

            torch.cuda.empty_cache()
            eigvals, eigvecs = torch.linalg.eigh(Cov.double(), UPLO='U')

            L = eigvals.flip(0).cpu()                 
            U = eigvecs.flip(1).cpu()                 
            V = U  
            # Cov = Cov.to('cpu')
            del Cov
            sta = sta.to('cpu')
            # train_data['stim'] = train_data['stim'].to('cpu')
            # train_data['robs'] = train_data['robs'].to('cpu')

            import gc
            for name in ['stim_flat','w','Cov','sta','eigvals','eigvecs']:
                if name in locals():
                    del locals()[name]
            gc.collect()
            torch.cuda.empty_cache() 

            if cache:
                # save L and V
                torch.save(L, f'{cell_dir}/L.pt')
                torch.save(V, f'{cell_dir}/V.pt')

            
            
        except Exception as e:
            print(f'Error processing cell {cid}: {e}')
            #check if e is a memory error
            if 'CUDA out of memory' in str(e):
                raise e
            else:
                print('Unknown error, skipping cell')
            return None, None
    
    
    plt.figure()
    plt.plot(L, 'o')
    plt.title(f'Cell {cid}: Eigenvalues of Covariance Matrix')
    plt.xlabel('Eigenvalue Index')
    plt.ylabel('Eigenvalue')
    plt.savefig(f'{cell_dir}/eigenvalues.png')
    if verbosity > 0: plt.show()
    plt.close()

    # make eig_values_range first 5 and last 5
    eig_values_range = np.concatenate([np.arange(5), np.arange(-5, 0)])
    plot_tensor = torch.stack(
        [ ste.cpu(), *[ V.T[i].cpu().reshape(dims) for i in eig_values_range ] ],
        dim=0
    )[:, :, None]

    from DataYatesV1 import plot_stas
    plot_stas(plot_tensor, 
            row_labels=['STE'] + [f'Eig {i}' for i in eig_values_range],
            col_labels=[f'lag {l}' for l in keys_lags['stim']])
    plt.title(f'Cell {cid}: STE & Eigenvectors')
    plt.savefig(f'{cell_dir}/eig_vs_ste.png')
    if verbosity > 0: plt.show()
    plt.close()

    torch.cuda.empty_cache()
    return L, V
#%%
# for cid in tqdm(range(n_units)):
#     verbosity = 0
#     train_data, val_data, train_sta = get_dataset_for_cell(cid, dim_for_centering=32)

#     L, V = get_eigenvalues_and_vectors(train_data, cid)
# import sys
# sys.exit()
# assert False, "stop here"

# verbosity = 1
# cid = 113
# train_data, val_data, train_sta = get_dataset_for_cell(cid, dim_for_centering=32)

# L, V = get_eigenvalues_and_vectors(train_data, cid)

#%%
from DataYatesV1.utils.modeling.reg import l1, laplacian, locality_conv

def l1_sta(sta):
    """L1 regularization for the STA"""
    return torch.norm(sta, p=1)

def laplacian_sta(sta):
    """Laplacian regularization for the STA"""
    assert len(sta.shape) == 3
    assert sta.shape[0] == 1 or sta.shape[0] >= 3
    sta = sta.squeeze()
    
    if sta.ndim == 2:
        sta = sta.view(1, 1, *sta.shape)
        kernel = torch.tensor([[0, 1, 0],
                              [1, -4, 1],
                              [0, 1, 0]], dtype=sta.dtype, device=sta.device).view(1, 1, 3, 3)
        conv = F.conv2d(sta, kernel, padding=1)
    elif sta.ndim == 3:
        sta = sta.view(1, 1, *sta.shape)
        kernel = (1/26) * torch.tensor([[[2, 3, 2],
                                        [3, 6, 3],
                                        [2, 3, 2]],
                                       [[3, 6, 3],
                                        [6, -88, 6],
                                        [3, 6, 3]],
                                       [[2, 3, 2],
                                        [3, 6, 3],
                                        [2, 3, 2]]], dtype=sta.dtype, device=sta.device).view(1, 1, 3, 3, 3)
        conv = F.conv3d(sta, kernel, padding=1)
    else:
        raise ValueError("Input must be a 2D or 3D tensor after squeezing.")
    
    return torch.norm(conv, p=2)



nl_to_use = F.softplus
class LinearNonLinearModel(nn.Module):
    def __init__(self, input_dim):
        super(LinearNonLinearModel, self).__init__()
        self.bias = nn.Parameter(torch.tensor(1.0)) 
        assert len(input_dim) == 3
        self.kernel = nn.Parameter(torch.randn(input_dim))
        
    def forward(self, x):
        # generator = torch.einsum('ntd,td->n', 
        #                         x['stim'].view(x['stim'].shape[0], x['stim'].shape[1], -1), 
        #                         self.kernel.view(self.kernel.shape[0], -1))
        B = x['stim'].shape[0]                
        x_flat = x['stim'].view(B, -1)           # (B, D)
        k_flat = self.kernel.view(-1)            # (D,)
        generator = x_flat @ k_flat              # (B,)
        # rhat = F.softplus(generator + self.bias)
        rhat = nl_to_use(generator + self.bias)
        x['rhat'] = rhat.view(x['robs'].shape)
        x['generator'] = generator.view(x['robs'].shape).detach()
        return x

class AffineSoftplus(nn.Module):
    def __init__(self):
        super(AffineSoftplus, self).__init__()
        self.bias = nn.Parameter(torch.tensor(0.0)) 
        self.weight = nn.Parameter(torch.tensor(1.0))
    def forward(self, x):
        x['rhat'] = nl_to_use(x['generator'] * self.weight + self.bias)
        x['rhat'] = x['rhat'].view(x['robs'].shape)
        return x
    
#%%
# def get_initial_weight_and_bias_ln(cell_to_fit, train_dset_loaded_centered, val_dset_loaded_centered, train_sta):
#     #want to first scale and bias the sta
#     batch = {
#             'stim': train_dset_loaded_centered['stim'], 
#             'robs': train_dset_loaded_centered['robs'][:, [cell_to_fit]], 
#             'dfs': train_dset_loaded_centered['dfs']
#     }
#     sta = torch.from_numpy(train_sta).float().to(device)
#     generator = torch.einsum('ntd,td->n', 
#                     batch['stim'].view(batch['stim'].shape[0], batch['stim'].shape[1], -1), 
#                     sta.view(sta.shape[0], -1))

#     generator_affine = AffineSoftplus()
#     loss = MaskedLoss(nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
#     optimizer = torch.optim.LBFGS(generator_affine.parameters(), lr=1, max_iter=10000, line_search_fn='strong_wolfe')
#     batch['generator'] = generator.detach()
#     metric_holders = {
#         'train_poisson_loss': None,
#         'val_poisson_loss': None,
#         'train_bps': None,
#         'val_bps': None,
#     }
#     def closure():
#         optimizer.zero_grad()
        
#         generator_affine(batch)
#         poisson_loss = loss(batch)
#         poisson_loss.backward()
#         return poisson_loss

#     pbar = range(3)
#     for epoch in pbar:
#         poisson_loss = optimizer.step(closure)
#         optimizer.zero_grad()
#         # pbar.set_description(f"Poiss: {poisson_loss.item():.4e}")
    
#     if verbosity > 0: print(f"Initial Poisson loss: {poisson_loss.item():.4e}")
#     batch['generator'] = batch['generator'].cpu().detach()
#     del batch['generator']
#     torch.cuda.empty_cache()
#     return generator_affine.weight.data, generator_affine.bias.data, poisson_loss.item()
def get_initial_weight_and_bias_ln(
    cell_to_fit,
    train_dset_loaded_centered,
    val_dset_loaded_centered,
    train_sta
):
    """
    Fit initial scale & bias for the LN model using LBFGS on both train & val,
    collecting train/val Poisson loss and bits-per-spike (BPS) metrics.
    """
    # Determine device
    device = train_dset_loaded_centered['stim'].device

    # Move STA to device and flatten dims
    sta = torch.from_numpy(train_sta).float().to(device)
    B_tr, T, H, W = train_dset_loaded_centered['stim'].shape
    D = T * H * W
    sta_flat = sta.view(D)  # (D,)

    # Prepare flattened stimulus for GEMV
    stim_tr_flat = train_dset_loaded_centered['stim'].view(B_tr, D)   # (B_tr, D)
    gen_tr = stim_tr_flat.mv(sta_flat)                                # (B_tr,)

    B_val = val_dset_loaded_centered['stim'].shape[0]
    stim_val_flat = val_dset_loaded_centered['stim'].view(B_val, D)    # (B_val, D)
    gen_val = stim_val_flat.mv(sta_flat)                              # (B_val,)

    # Build batches
    robs_tr  = train_dset_loaded_centered['robs'][:, [cell_to_fit]].to(device)
    dfs_tr   = train_dset_loaded_centered['dfs'].to(device)
    batch_tr = {'generator': gen_tr.unsqueeze(1), 'robs': robs_tr, 'dfs': dfs_tr}

    robs_val = val_dset_loaded_centered['robs'][:, [cell_to_fit]].to(device)
    dfs_val  = val_dset_loaded_centered['dfs'].to(device)
    batch_val= {'generator': gen_val.unsqueeze(1), 'robs': robs_val,  'dfs': dfs_val}

    # Model, loss, optimizer
    model     = AffineSoftplus().to(device)
    loss_fn   = MaskedLoss(nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
    optimizer = torch.optim.LBFGS(
        model.parameters(),
        lr=1.0,
        max_iter=10000,
        line_search_fn='strong_wolfe'
    )

    # Metrics container
    metric_holders = {
        'train_poisson_loss': None,
        'val_poisson_loss':   None,
        'train_bps':          None,
        'val_bps':            None,
    }

    # Closure to compute metrics
    def closure():
        optimizer.zero_grad()

        out_tr = model(batch_tr)
        train_loss = loss_fn(out_tr).mean()
        train_agg = PoissonBPSAggregator()
        train_agg(out_tr)
        tbps = train_agg.closure().item()

        train_loss.backward()

        with torch.no_grad():
            out_val = model(batch_val)
            val_loss = loss_fn(out_val).mean()
            val_agg = PoissonBPSAggregator()
            val_agg(out_val)
            vbps = val_agg.closure().item()

        # Fill metrics
        metric_holders['train_poisson_loss'] = train_loss.item()
        metric_holders['train_bps']          = tbps
        metric_holders['val_poisson_loss']   = val_loss.item()
        metric_holders['val_bps']            = vbps

        return train_loss

    # Run a few LBFGS steps
    for _ in range(3):
        optimizer.step(closure)
        optimizer.zero_grad()

    # Extract weight & bias to CPU
    w0 = model.weight.data.clone().cpu()
    b0 = model.bias.data.clone().cpu()

    # Move everything to CPU before cleanup
    model.to('cpu')
    for k in batch_tr.keys(): batch_tr[k] = batch_tr[k].cpu()
    for k in batch_val.keys(): batch_val[k] = batch_val[k].cpu()
    # batch_tr = {k: v.detach().cpu() for k, v in batch_tr.items()}
    # batch_val= {k: v.detach().cpu() for k, v in batch_val.items()}
    stim_tr_flat = stim_tr_flat.cpu()
    stim_val_flat= stim_val_flat.cpu()
    gen_tr = gen_tr.cpu()
    gen_val = gen_val.cpu()

    sta = sta.cpu()

    # Delete GPU references and clear cache
    del model, optimizer, loss_fn
    del stim_tr_flat, stim_val_flat, gen_tr, gen_val, sta_flat, sta
    del batch_tr, batch_val, robs_tr, dfs_tr, robs_val, dfs_val
    torch.cuda.empty_cache()

    return w0, b0, metric_holders


#%%
# Training configuration
loss_fn = MaskedLoss(nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
def orthogonality_penalty(kernels: torch.Tensor) -> torch.Tensor:
    """
    Computes Frobenius‐norm squared penalty for (K Kᵀ – I),
    encouraging the rows of `kernels` to be orthonormal.

    kernels: Tensor of shape (G, *spatiotemporal_dims*)
    returns: scalar penalty
    """
    # G = kernels.shape[0]
    # D = int(torch.prod(torch.tensor(kernels.shape[1:], device=kernels.device)))
    # W = kernels.view(G, D)                     # (G, D)
    # gram = W @ W.t()                           # (G, G)
    # I = torch.eye(G, device=gram.device)       # (G, G)
    # return torch.norm(gram - I, p='fro')**2

    G = kernels.shape[0]
    D = int(torch.prod(torch.tensor(kernels.shape[1:], device=kernels.device)))
    W = kernels.view(G, D)               # (G, D)
    gram = W @ W.t()                     # (G, G)
    mask = torch.ones_like(gram)
    mask.fill_diagonal_(0)               # zero‐out the diagonal
    return torch.norm(gram * mask, p='fro')**2

def forward(model, dset, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, cell_to_fit, ortho_coeff=0.0, padding_mode='reflect'):
    """Forward pass through the LN model with loss calculation"""
    batch = {
        'stim': dset['stim'], 
        'robs': dset['robs'][:, [cell_to_fit]], 
        'dfs': dset['dfs']
    }
    
    # Forward pass
    batch = model(batch)
    poisson_loss = loss_fn(batch)
    
    # Regularization
    # reg = laplacian_coeff * laplacian_sta(model.kernel) + l1_coeff * l1_sta(model.kernel)
    if hasattr(model, 'kernels'):
        kerns = list(model.kernels)      # EnergyModel case
    else:
        kerns = [model.kernel]           # LinearNonLinearModel case


    # compute Laplacian and L1 penalties across all kernels
    # regs_lap_check = sum(laplacian_sta(k) for k in kerns)

    regs_lap_space = sum(laplacian(k, dims=[-2, -1], padding_mode=padding_mode) for k in kerns) if laplacian_coeff_space > 0.0 else 0.0
    regs_lap_time  = sum(laplacian(k, dims=[-3], padding_mode=padding_mode) for k in kerns) if laplacian_coeff_time > 0.0 else 0.0
    regs_lap_local = sum(locality_conv(k, dims = [-2, -1]) for k in kerns) if local_coeff > 0.0 else 0.0

    # assert torch.allclose(regs_lap, regs_lap_check), f"Mismatch in Laplacian penalty: {regs_lap} vs {regs_lap_check}"
    regs_l1  = sum(l1_sta(k) for k in kerns) if l1_coeff > 0.0 else 0.0

    # combine with coefficients
    reg = laplacian_coeff_space * regs_lap_space + l1_coeff * regs_l1 + laplacian_coeff_time * regs_lap_time + local_coeff * regs_lap_local
    

    # Orthogonality penalty (only for multi‐kernel models)
    if ortho_coeff > 0.0 and hasattr(model, 'kernels'):
        ortho_pen = orthogonality_penalty(model.kernels)
        reg = reg + ortho_coeff * ortho_pen
    
    loss_val = poisson_loss + reg

    # Check for NaN values
    if torch.isnan(poisson_loss).any() or torch.isnan(reg).any() or torch.isnan(loss_val).any():
        raise ValueError("NaN detected in loss calculation")

    return loss_val, poisson_loss, reg, batch

def evaluate_model(model, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, train_dset_loaded_centered, val_dset_loaded_centered, cell_to_fit, ortho_coeff=0.0):
    """Evaluate model on train and validation sets, returning metrics as a dictionary"""
    # Train set evaluation
    # Initialize metrics aggregators
    train_bps_aggregator = PoissonBPSAggregator()
    val_bps_aggregator = PoissonBPSAggregator()
    loss_val, train_poisson_loss, train_reg, train_batch = forward(model, train_dset_loaded_centered, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, cell_to_fit, ortho_coeff=ortho_coeff)

    # Validation set evaluation
    with torch.no_grad():
        val_loss, val_poisson_loss, val_reg, val_batch = forward(model, val_dset_loaded_centered, laplacian_coeff_space, laplacian_coeff_time, local_coeff, l1_coeff, cell_to_fit, ortho_coeff=ortho_coeff)
        val_bps_aggregator(val_batch)
        train_bps_aggregator(train_batch)
        train_bps = train_bps_aggregator.closure().item()
        val_bps = val_bps_aggregator.closure().item()
    
    return {
        'loss': loss_val,
        'train_poisson_loss': train_poisson_loss,
        'train_reg': train_reg,
        'val_loss': val_loss,
        'val_poisson_loss': val_poisson_loss,
        'val_reg': val_reg,
        'train_bps': train_bps,
        'val_bps': val_bps
    }

def visualize_results(model, epoch, num_epochs, metrics, max_val_bps):
    """Visualize the model's receptive field and performance metrics"""
        
    sta = model.kernel.cpu().detach().numpy()
    stas_norm = sta / np.max(np.abs(sta))
    vmin, vmax = np.min(stas_norm), np.max(stas_norm)
    
    # Ensure symmetric color scale
    if abs(vmin) > abs(vmax):
        vmax = -vmin
    else:
        vmin = -vmax
    
    if verbosity > 0:
        fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': vmin, 'vmax': vmax}, title_prefix='lag')
        plt.show()
    
        # Print performance metrics
        print(f'Train BPS: {metrics["train_bps"]}, Val BPS: {metrics["val_bps"]}')
        print(f'Epoch {epoch+1}/{num_epochs}, Train Loss: {metrics["train_poisson_loss"].item():.4f}, Val Loss: {metrics["val_loss"]:.4f}')
        print('max val bps:', max_val_bps)

# 

def train_with_lbfgs(model, 
                     laplacian_coeff_space, 
                     laplacian_coeff_time,
                     local_coeff,
                     l1_coeff, 
                     cell_to_fit,
                     train_dset_loaded_centered, val_dset_loaded_centered,
                     using_ray=True,
                     max_iter=10000,
                     ortho_coeff=0.0,
                     use_best_val_bps=False):
    optimizer = torch.optim.LBFGS(
        model.parameters(),
        lr=1,
        max_iter=max_iter,
        tolerance_grad=1e-6,
        # tolerance_change=1e-8,
        tolerance_change=1e-6,
        # line_search_fn='strong_wolfe'
        history_size=10
    )

    # one mutable counter so closure can increment it
    counter = [0]
    # Track metric history
    metric_history = {
        'train_poisson_loss': [],
        'train_bps': [],
        'val_poisson_loss': [],
        'val_bps': []
    }

    # Track best model if use_best_val_bps is True
    best_val_bps = -float('inf')
    best_model_state = None
    best_metrics = None

    if verbosity > 0:
        pbar = tqdm(range(max_iter), position=0, leave=True)

    def closure():
        nonlocal best_val_bps, best_model_state, best_metrics
        optimizer.zero_grad()
        metrics = evaluate_model(
            model,
            laplacian_coeff_space,
            laplacian_coeff_time,
            local_coeff,
            l1_coeff,
            train_dset_loaded_centered,
            val_dset_loaded_centered,
            cell_to_fit,
            ortho_coeff=ortho_coeff
        )
        loss = metrics['loss']
        loss.backward()

        # Store metrics in history
        metric_history['train_poisson_loss'].append(metrics['train_poisson_loss'].item())
        metric_history['train_bps'].append(metrics['train_bps'])
        metric_history['val_poisson_loss'].append(metrics['val_poisson_loss'].item())
        metric_history['val_bps'].append(metrics['val_bps'])

        # Update best model if needed
        if use_best_val_bps and metrics['val_bps'] > best_val_bps:
            best_val_bps = metrics['val_bps']
            best_model_state = deepcopy(model.state_dict())
            # Create a copy of metrics with tensors converted to scalars
            best_metrics = {
                'train_poisson_loss': metrics['train_poisson_loss'].item(),
                'train_reg': metrics['train_reg'].item(),
                'val_loss': metrics['val_loss'].item(),
                'val_poisson_loss': metrics['val_poisson_loss'].item(),
                'val_reg': metrics['val_reg'].item(),
                'train_bps': metrics['train_bps'],
                'val_bps': metrics['val_bps'],
                'loss': metrics['loss'].item()
            }

        # report at each internal iteration
        counter[0] += 1
        if using_ray:
            tune.report({
              "val_bps": metrics["val_bps"],  # already a scalar from .item() call
              "training_iteration": counter[0],  # already a scalar
              "train_bps": metrics["train_bps"],  # already a scalar from .item() call
              "train_poisson_loss": metrics["train_poisson_loss"].item(),  # tensor with gradients
              "train_reg": metrics["train_reg"].item(),  # tensor with gradients
              "val_poisson_loss": metrics["val_poisson_loss"].item(),  # tensor with gradients
              "val_reg": metrics["val_reg"].item(),  # tensor with gradients
              "loss": loss.item(),  # already has .item()
            }) 
        if verbosity > 0:
            pbar.set_description(
                f"Poiss: {metrics['train_poisson_loss']:.4e}|Reg: {metrics['train_reg']:.4e}|"
                f"T_BPS: {metrics['train_bps']:.4f}|V_BPS:{metrics['val_bps']:.4f}"
            )
            pbar.update(1)
        return loss

    # run LBFGS (will call closure() up to max_iter times)
    optimizer.step(closure)
    if verbosity > 0: pbar.close()

    # Restore best model state if use_best_val_bps is True
    if use_best_val_bps and best_model_state is not None:
        model.load_state_dict(best_model_state)
        if verbosity > 0:
            print(f"Restored model with best validation BPS: {best_val_bps:.4f}")

    # final evaluation
    if use_best_val_bps and best_metrics is not None:
        # Use the stored metrics from the best model
        final_metrics = best_metrics
    else:
        # Do a fresh evaluation
        with torch.no_grad():
            final_metrics = evaluate_model(
                model,
                laplacian_coeff_space, 
                laplacian_coeff_time,
                local_coeff,
                l1_coeff,
                train_dset_loaded_centered,
                val_dset_loaded_centered,
                cell_to_fit,
                ortho_coeff=ortho_coeff
            )
    final_metrics['history'] = metric_history
    return final_metrics

def train_with_adamw(model, 
                    laplacian_coeff_space, 
                    laplacian_coeff_time,
                    local_coeff,
                    l1_coeff, 
                    cell_to_fit,
                    train_dset_loaded_centered, val_dset_loaded_centered,
                    using_ray=True,
                    max_iter=10000,
                    ortho_coeff=0.0,
                    patience=100, 
                    tolerance=5e-4,
                    lr=1e-3,
                    use_best_val_bps=False):
    """Train the model using AdamW optimizer with early stopping"""
    # Configure AdamW optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr)
    
    # Early stopping variables
    best_val_bps = -np.inf
    best_metrics = None
    best_state_dict = None
    no_improve_count = 0
    
    # Counter for ray reporting
    counter = [0]
    
    # Track metric history
    metric_history = {
        'train_poisson_loss': [],
        'train_bps': [],
        'val_poisson_loss': [],
        'val_bps': []
    }
    
    # Training loop with progress bar
    pbar = tqdm(range(max_iter), position=0, leave=True) if verbosity > 0 else range(max_iter)
    for epoch in pbar:
        # Standard gradient descent step
        optimizer.zero_grad()
        metrics = evaluate_model(
            model,
            laplacian_coeff_space,
            laplacian_coeff_time,
            local_coeff,
            l1_coeff,
            train_dset_loaded_centered,
            val_dset_loaded_centered,
            cell_to_fit,
            ortho_coeff=ortho_coeff
        )
        loss = metrics['loss']
        loss.backward()
        optimizer.step()
        
        val_bps = metrics['val_bps']
        
        # Store metrics in history
        metric_history['train_poisson_loss'].append(metrics['train_poisson_loss'].item())
        metric_history['train_bps'].append(metrics['train_bps'])
        metric_history['val_poisson_loss'].append(metrics['val_poisson_loss'].item())
        metric_history['val_bps'].append(metrics['val_bps'])
        
        # Track best model
        if val_bps > best_val_bps:
            best_val_bps = val_bps
            # Create a copy of metrics with tensors converted to scalars
            best_metrics = {
                'train_poisson_loss': metrics['train_poisson_loss'].item(),
                'train_reg': metrics['train_reg'].item(),
                'val_loss': metrics['val_loss'].item(),
                'val_poisson_loss': metrics['val_poisson_loss'].item(),
                'val_reg': metrics['val_reg'].item(),
                'train_bps': metrics['train_bps'],
                'val_bps': metrics['val_bps'],
                'loss': metrics['loss'].item()
            }
            best_state_dict = deepcopy(model.state_dict())
            no_improve_count = 0
        else:
            no_improve_count += 1
        
        # Report metrics for ray tuning
        counter[0] += 1
        if using_ray:
            tune.report({
              "val_bps": metrics["val_bps"],
              "training_iteration": counter[0],
              "train_bps": metrics["train_bps"],
              "train_poisson_loss": metrics["train_poisson_loss"].item(),
              "train_reg": metrics["train_reg"].item(),
              "val_poisson_loss": metrics["val_poisson_loss"].item(),
              "val_reg": metrics["val_reg"].item(),
              "loss": loss.item(),
            })
        
        if verbosity > 0:
            # Update progress bar
            pbar.set_description(
                f"Poiss: {metrics['train_poisson_loss']:.4e}|Reg: {metrics['train_reg']:.4e}|"
                f"T_BPS: {metrics['train_bps']:.4f}|V_BPS:{val_bps:.4f}|Patience: {no_improve_count}/{patience}"
            )
        
        # Check if we should stop early
        if no_improve_count >= patience:
            if verbosity > 0: 
                print(f"Early stopping at epoch {epoch+1}: No improvement in validation BPS for {patience} epochs")
            break
    
    if verbosity > 0: 
        pbar.close()
        print("\nTraining complete!")
        print(f"Cell {cell_to_fit}: Best Val BPS = {best_val_bps:.4f}")
    
    # Restore best model if use_best_val_bps is True
    if use_best_val_bps and best_state_dict is not None:
        model.load_state_dict(best_state_dict)
        if verbosity > 0:
            print(f"Restored model with best validation BPS: {best_val_bps:.4f}")
    
    # Final evaluation with best model
    if use_best_val_bps and best_metrics is not None:
        # Use the stored metrics from the best model
        final_metrics = best_metrics
    else:
        # Do a fresh evaluation
        with torch.no_grad():
            final_metrics = evaluate_model(
                model,
                laplacian_coeff_space, 
                laplacian_coeff_time,
                local_coeff,
                l1_coeff,
                train_dset_loaded_centered,
                val_dset_loaded_centered,
                cell_to_fit,
                ortho_coeff=ortho_coeff
            )
    
    final_metrics['history'] = metric_history
    return final_metrics


#%%
class AffineEnergy(nn.Module):
    def __init__(self, n_generators: int):
        super(AffineEnergy, self).__init__()
        # one weight & bias for each generator
        self.weights = nn.Parameter(torch.ones(n_generators))
        # self.biases  = nn.Parameter(torch.zeros(n_generators))
        self.bias = nn.Parameter(torch.tensor(0.0))

    def forward(self, x):
        # x['generators']: (n_generators, N)
        G = x['generators']                   # tensor, shape [n_gens, N]
        # compute per‐generator energy: w_i * G_i^2 + b_i
        # broadcast weights/biases over the N dimension
        E = (self.weights[:, None] * G)**2
        # sum across generators → shape [N]
        E_sum = E.sum(dim=0)
        # nonlinear readout
        R = F.softplus(E_sum + self.bias) 
        # reshape into whatever shape your robs expects
        x['rhat'] = R.view(x['robs'].shape)
        return x
def get_initial_weight_and_bias_energy(
    train_data,
    val_data,
    cid: int,
    eigenvecs_T: torch.Tensor,
    eig_indices,
    device: str = 'cuda'
):
    """
    Fit initial per-generator weights and biases for the energy model using LBFGS,
    computing generators internally from eigenvectors.

    Args:
      train_data: dict-like with 'stim', 'robs', 'dfs'
      cid:        int, cell index
      eigenvecs_T: Tensor of shape (D, D), each row is an eigenvector
      eig_indices: sequence of ints, indices of rows in eigenvecs_T to use
      device:     device string for computation

    Returns:
      weights, biases: Tensors each of shape (n_generators,)
    """
    stim_tr = train_data['stim'].to(device) 
    # assert train_data['stim'].device == torch.device('cpu') 
    T      = stim_tr.shape[0]
    D      = int(torch.prod(torch.tensor(stim_tr.shape[1:], device=device)))
    stim_tr_flat = stim_tr.view(T, D)              # (T, D)

    # Move validation stim
    stim_val      = val_data['stim'].to(device)
    T_val         = stim_val.shape[0]
    stim_val_flat = stim_val.view(T_val, D)        # (T_val, D)

    # Select eigenvectors and compute generators
    ev      = eigenvecs_T.to(device)               # (D, D)
    sel     = ev[eig_indices, :]                   # (G, D)
    gen_tr  = sel @ stim_tr_flat.T                  # (G, T)
    gen_val = sel @ stim_val_flat.T                 # (G, T_val)

    # Prepare batches
    robs_tr    = train_data['robs'][:, cid].unsqueeze(1).to(device)
    dfs_tr     = train_data['dfs'].to(device)
    batch_tr   = {'generators': gen_tr,  'robs': robs_tr,  'dfs': dfs_tr}

    robs_val   = val_data['robs'][:, cid].unsqueeze(1).to(device)
    dfs_val    = val_data['dfs'].to(device)
    batch_val  = {'generators': gen_val, 'robs': robs_val, 'dfs': dfs_val}

    # Model, loss, optimizer
    n_gens  = len(eig_indices)
    model   = AffineEnergy(n_gens).to(device)
    loss_fn = MaskedLoss(torch.nn.PoissonNLLLoss(log_input=False, full=False, reduction='none'))
    optimizer = torch.optim.LBFGS(model.parameters(), lr=1.0, max_iter=1000, line_search_fn='strong_wolfe')

    # Progress bar (3 LBFGS steps)
    pbar = tqdm(range(3), desc=f"InitEnergy cell{cid}", leave=True) if verbosity>0 else None

    metric_holders = {
        'train_poisson_loss': None,
        'val_poisson_loss': None,
        'train_bps': None,
        'val_bps': None,
    }
    def closure():
        optimizer.zero_grad()
        out_tr = model(batch_tr)
        poisson_loss = loss_fn(out_tr).mean()

        # Train BPS
        train_agg = PoissonBPSAggregator()
        train_agg(out_tr)
        tbps = train_agg.closure().item()

        

        poisson_loss.backward()

        with torch.no_grad():
            # Val BPS
            out_val = model(batch_val)
            val_loss = loss_fn(out_val).mean()
            val_agg = PoissonBPSAggregator()
            val_agg(out_val)
            vbps = val_agg.closure().item()
        if verbosity > 0:
            pbar.set_description(f"Poiss:{poisson_loss:.4e} Tbps:{tbps:.4f} Vbps:{vbps:.4f}")
            pbar.update(1)
        metric_holders['train_poisson_loss'] = poisson_loss.item()
        metric_holders['val_poisson_loss'] = val_loss.item()
        metric_holders['train_bps'] = tbps
        metric_holders['val_bps'] = vbps

        return poisson_loss

    poisson_loss = optimizer.step(closure)
    

    # model.to('cpu')
    # stim = stim.to('cpu')
    # stim_flat = stim_flat.to('cpu')
    # del stim
    # del stim_flat
    #move everything to cpu and then del to free up memory on gpu
    model.to('cpu')
    stim_tr = stim_tr.to('cpu')
    stim_val = stim_val.to('cpu')
    stim_tr_flat = stim_tr_flat.to('cpu')
    stim_val_flat = stim_val_flat.to('cpu')
    del stim_tr
    del stim_val
    del stim_tr_flat
    del stim_val_flat
    del gen_tr
    del gen_val

    torch.cuda.empty_cache()

    return model.weights.detach().cpu(), model.bias.detach().cpu(), metric_holders
class EnergyModel(nn.Module):
    def __init__(self, input_dim, n_generators):
        """
        Simple energy-model LN cascade:
          • multiple linear filters (generators)
          • square-and-sum pooling + open bias
          • softplus output nonlinearity

        Args:
          input_dim:    tuple (n_lags, H, W)
          n_generators: number of energy filters
        """
        super(EnergyModel, self).__init__()
        assert len(input_dim) == 3
        # one spatiotemporal kernel per generator
        self.kernels = nn.Parameter(torch.randn(n_generators, *input_dim))
        # one bias per generator (added after squaring)
        self.bias  = nn.Parameter(torch.tensor(0.0))

    def forward(self, x):
        # x['stim']: (batch, n_lags, H, W)
        B = x['stim'].shape[0]
        # flatten the spatiotemporal dims
        stim_flat    = x['stim'].view(B, -1)             # (B, D)
        kernels_flat = self.kernels.view(self.kernels.size(0), -1)  # (G, D)

        # linear responses of each generator
        G = stim_flat @ kernels_flat.t()                 # (B, G)

        # energy pooling: square + bias, then sum over generators
        E = G**2                 # (B, G)
        E_sum = E.sum(dim=1)                             # (B,)

        # final nonlinearity
        rhat = F.softplus(E_sum + self.bias)                         # (B,)
        x['rhat'] = rhat.view(x['robs'].shape)           # match spike-shape
        return x

#%%

def save_sta_visualization(sta, cell_to_fit, save_path):
    """
    Save visualization of original STA to the specified path
    
    Args:
        sta: The original STA (numpy array)
        cell_to_fit: Index of the cell
        save_path: Path to save the visualization
    """
    # Convert to numpy if it's a torch tensor
    if isinstance(sta, torch.Tensor):
        sta = sta.cpu().detach().numpy()
    
    stas_norm = sta / np.max(np.abs(sta))
    vmin, vmax = np.min(stas_norm), np.max(stas_norm)
    
    # Ensure symmetric color scale
    if abs(vmin) > abs(vmax):
        vmax = -vmin
    else:
        vmin = -vmax
    
    plt.figure(figsize=(12, 10))
    fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': vmin, 'vmax': vmax}, title_prefix='lag')
    plt.suptitle(f"Original STA for Cell {cell_to_fit}", fontsize=16)
    plt.tight_layout()
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    if verbosity > 0: print(f"STA visualization saved to {save_path}")
    plt.close()

def save_kernel_visualization_ln(model, cell_to_fit, best_val_bps, laplacian_space_coeff, l1_coeff, save_path, initial_val_bps=None, laplacian_time_coeff=None, local_coeff=None, ortho_coeff=None):
    """
    Save visualization of model kernel to the specified path
    
    Args:
        model: The trained LinearNonLinearModel
        cell_to_fit: Index of the cell that was fit
        best_val_bps: The best validation BPS value achieved by the model
        laplacian_space_coeff: The spatial Laplacian regularization coefficient
        l1_coeff: The L1 regularization coefficient
        save_path: Path to save the visualization
        initial_val_bps: The initial validation BPS value (before training)
        laplacian_time_coeff: The temporal Laplacian regularization coefficient
        local_coeff: The local coefficient
        ortho_coeff: The orthogonality coefficient
    """
    sta = model.kernel.cpu().detach().numpy()
    stas_norm = sta / np.max(np.abs(sta))
    vmin, vmax = np.min(stas_norm), np.max(stas_norm)
    
    # Ensure symmetric color scale
    if abs(vmin) > abs(vmax):
        vmax = -vmin
    else:
        vmin = -vmax
    
    plt.figure(figsize=(12, 10))
    plt.rcParams['axes.grid'] = False
    fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': vmin, 'vmax': vmax}, title_prefix='lag')
    
    # Create more detailed title with all parameters
    title = f"Model Kernel for Cell {cell_to_fit}\n"
    
    if initial_val_bps is not None:
        title += f"Init Val BPS={initial_val_bps:.3f}, "
    
    title += f"Val BPS={best_val_bps:.3f}\n"
    title += f"lap_space={laplacian_space_coeff:.1e}"
    
    if laplacian_time_coeff is not None:
        title += f", lap_time={laplacian_time_coeff:.1e}"
    
    if local_coeff is not None:
        title += f", local={local_coeff:.1e}"
    
    if l1_coeff > 0:
        title += f", l1={l1_coeff:.1e}"
        
    if ortho_coeff is not None and ortho_coeff > 0:
        title += f", ortho={ortho_coeff:.1e}"
    
    plt.suptitle(title, fontsize=16)
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    if verbosity > 0: print(f"Kernel visualization saved to {save_path}")
    plt.close()

def save_kernel_visualization_energy(
    model: EnergyModel,
    eigenvecs: torch.Tensor,
    eig_indices: Sequence[int],
    cell_to_fit: int,
    initial_val_bps: float,
    best_val_bps: float,
    laplacian_space_coeff: float,
    laplacian_time_coeff: float,
    local_coeff: float,
    l1_coeff: float,
    ortho_coeff: float,
    save_path: str
):
    """
    Visualize and save both the original eigen‐maps and the learned energy kernels.
    
    Args:
      model:           trained EnergyModel
      eigenvecs:       Tensor (n_gens, n_lags, H, W) of the original eigenvectors
      eig_indices:     list of ints, which eigen‐maps were used
      cell_to_fit:     cell index
      best_val_bps:    best validation BPS
      laplacian_coeff: Laplacian regularization coefficient
      l1_coeff:        L1 regularization coefficient
      save_path:       where to write the PNG
    """
    import torch
    import matplotlib.pyplot as plt
    from DataYatesV1 import plot_stas

    # pull everything to CPU & numpy‐compatible
    orig = eigenvecs.detach().cpu()         # (G, lags, H, W)
    learned = model.kernels.detach().cpu()  # (G, lags, H, W)

    # stack: first eigen‐maps, then learned kernels
    plot_tensor = torch.cat([orig, learned], dim=0)[:, :, None]  # → (2G, lags, H, W, 1)

    # row labels
    eig_labels = [f"Eig {i}" for i in eig_indices]
    kern_labels = [f"Kern {i}" for i in eig_indices]
    row_labels = eig_labels + kern_labels

    # column labels (lags)
    col_labels = [f"lag {l}" for l in keys_lags['stim']]

    # normalize color scale independently per row?
    # here we let plot_stas handle defaults
    plt.rcParams['axes.grid'] = False
    
    plt.figure(figsize=(12, 2 * len(row_labels)))
    fig, axs = plot_stas(
        plot_tensor, 
        row_labels=row_labels,
        col_labels=col_labels
    )
    plt.suptitle(
        f"Cell {cell_to_fit}: Eigen‐maps (top) vs Learned Kernels (bottom)\n"
        f"Init Val BPS={initial_val_bps:.3f}, Val BPS={best_val_bps:.3f}, \n lap_space={laplacian_space_coeff:.1e}, lap_time={laplacian_time_coeff:.1e}, local={local_coeff:.1e}",
        fontsize=16
    )
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    if verbosity > 0:
        print(f"Energy kernels visualization saved to {save_path}")
    plt.close()

def select_optimal_trial(analysis, selection_method="global_best", top_percentage=None, optimize_param="local", use_best_val_bps=False):
    """
    Select the optimal trial from Ray Tune analysis based on specified criteria.
    
    Args:
        analysis: Ray Tune analysis object
        selection_method: str, either "global_best" or "adjusted_best"
        top_percentage: float between 0 and 1, required when selection_method="adjusted_best"
        optimize_param: str, default="local"
        use_best_val_bps: bool, whether to use best val bps instead of last result
    """
    # Get dataframe with appropriate filtering
    if use_best_val_bps:
        # Get results where val_bps is maximal for each trial
        df = analysis.dataframe(metric="val_bps", mode="max")
    else:
        # Get last results for each trial
        df = analysis.dataframe()
    
    # Filter out trials that don't have val_bps
    df = df[df['val_bps'].notna()]
    
    if len(df) == 0:
        raise ValueError("No completed trials found with val_bps metric")
    
    # Sort by val_bps in descending order
    df_sorted = df.sort_values('val_bps', ascending=False)
    
    # Get the global best trial
    best_trial_id = df_sorted.iloc[0]['trial_id']
    global_best_trial = None
    for trial in analysis.trials:
        if trial.trial_id == best_trial_id:
            global_best_trial = trial
            break
    
    global_best_val_bps = df_sorted.iloc[0]['val_bps']
    
    if verbosity > 0:
        print(f"Global best trial: val_bps={global_best_val_bps:.4f}")
    
    if selection_method == "global_best":
        return global_best_trial
    
    elif selection_method == "adjusted_best":
        # Validate parameters
        if top_percentage is None or not (0 < top_percentage <= 1):
            raise ValueError("top_percentage must be provided and be between 0 and 1 when selection_method='adjusted_best'")
        
        # Calculate number of trials to consider
        n_trials = len(df_sorted)
        n_top_trials = max(1, int(n_trials * top_percentage))
        
        if verbosity > 0:
            print(f"Considering top {n_top_trials} trials out of {n_trials} total trials")
        
        # Get top percentage of trials
        top_df = df_sorted.head(n_top_trials)
        
        # Find the trial with the highest value for the specified parameter
        if optimize_param in top_df.columns:
            selected_row = top_df.loc[top_df[optimize_param].idxmax()]
        else:
            # Fallback to config columns
            config_col = f"config/{optimize_param}"
            if config_col in top_df.columns:
                selected_row = top_df.loc[top_df[config_col].idxmax()]
            else:
                raise ValueError(f"Parameter {optimize_param} not found in dataframe columns")
        
        # Find the corresponding trial object
        selected_trial_id = selected_row['trial_id']
        highest_param_trial = None
        for trial in analysis.trials:
            if trial.trial_id == selected_trial_id:
                highest_param_trial = trial
                break
        
        if verbosity > 0:
            param_value = selected_row.get(optimize_param, selected_row.get(f"config/{optimize_param}", "N/A"))
            selected_val_bps = selected_row['val_bps']
            global_param_value = df_sorted.iloc[0].get(optimize_param, df_sorted.iloc[0].get(f"config/{optimize_param}", "N/A"))
            
            print(f"Selected trial: {optimize_param}={param_value:.4e}, val_bps={selected_val_bps:.4f}")
            print(f"Global best: {optimize_param}={global_param_value:.4e}, val_bps={global_best_val_bps:.4f}")
        
        return highest_param_trial
    
    else:
        raise ValueError(f"Invalid selection_method: {selection_method}. " 
                         f"Must be 'global_best' or 'adjusted_best'")

def get_all_trials_info(analysis, use_best_val_bps=False):
    """
    Extract relevant information from all completed trials in the analysis.
    
    Args:
        analysis: Ray Tune analysis object
        use_best_val_bps: bool, whether to use best val bps instead of last result
        
    Returns:
        list: List of dictionaries containing trial information
    """
    # Get dataframe with appropriate filtering
    if use_best_val_bps:
        # Get results where val_bps is maximal for each trial
        df = analysis.dataframe(metric="val_bps", mode="max")
    else:
        # Get last results for each trial
        df = analysis.dataframe()
    
    # Filter out trials that don't have val_bps
    df = df[df['val_bps'].notna()]
    
    # Create a list to store trial information
    all_trials_info = []
    
    # Extract relevant information from each trial
    for _, row in df.iterrows():
        trial_info = {
            "val_bps": row["val_bps"],
            "laplacian_space": row.get("config/laplacian_space", row.get("laplacian_space")),
            "laplacian_time": row.get("config/laplacian_time", row.get("laplacian_time")),
            "local": row.get("config/local", row.get("local")),
            "l1": row.get("config/l1", row.get("l1")),
            "ortho_coeff": float(row.get("config/ortho_coeff", row.get("ortho_coeff", 0.0)))
        }
        all_trials_info.append(trial_info)
    
    return all_trials_info

#%%

# ─── Your Ray trainable wrapper ────────────────────────────────────────────────
def tune_trainable(config, train_ds, val_ds, train_sta, cell_to_fit, L=None, V=None):
    # move datasets to GPU
    train_dev = {k: v.to(device) for k, v in train_ds.items()}
    val_dev   = {k: v.to(device) for k, v in val_ds.items()}
    
    # Extract hyperparameters
    lap_space = config["laplacian_space"]
    lap_time = config['laplacian_time']
    local = config['local']
    l1 = config["l1"]
    ortho_coeff = float(config.get("ortho_coeff", 0.0))
    optimizer_type = config.get("optimizer", "lbfgs")  # Default to LBFGS for backward compatibility
    
    # Extract optimizer-specific parameters
    if optimizer_type == "adamw":
        lr = config.get("lr", 1e-3)
        patience = config.get("patience", 100)
        tolerance = config.get("tolerance", 5e-4)

    if config["model_type"] == "ln":
        # convert STA to a CUDA tensor
        sta_dev = torch.from_numpy(train_sta).float().to(device)

        # build & init
        model = LinearNonLinearModel(train_sta.shape).to(device)
        init_w, init_b, _ = get_initial_weight_and_bias_ln(cell_to_fit, train_dev, val_dev, train_sta)
        model.kernel.data = sta_dev * init_w.to(device)
        model.bias.data   = init_b.to(device)
    else:
        if L is None or V is None: raise ValueError("Eigenvalues or eigenvectors are None")
        eigenvecs_T = V.T.float()  # shape (D, D)
        eig_indices = config["eig_indices"]   # list of ints

        n_gens = len(eig_indices)
        input_dim = train_dev['stim'].shape[1:]
        # first fit weights & shared bias via LBFGS initializer
        w0, b0, initial_metrics = get_initial_weight_and_bias_energy(
            train_dev, val_dev,
            cell_to_fit,
            eigenvecs_T, eig_indices,
            device=device
        )

        # build the energy model
        model = EnergyModel(input_dim, n_gens).to(device)

        # slice out the same eigen-maps and init kernels + bias
        evecs = eigenvecs_T[eig_indices]              # (n_gens, D)
        evecs = evecs.view(n_gens, *input_dim).to(device)
        model.kernels.data = (evecs * w0[:, None, None, None].to(device))
        model.bias.data    = b0.to(device)

    # Common training parameters
    train_params = {
        "model": model,
        "laplacian_coeff_space": lap_space,
        "laplacian_coeff_time": lap_time,
        "local_coeff": local,
        "l1_coeff": l1,
        "cell_to_fit": cell_to_fit,
        "train_dset_loaded_centered": train_dev,
        "val_dset_loaded_centered": val_dev,
        "using_ray": True,
        "max_iter": 2000,
        "ortho_coeff": ortho_coeff
    }

    # Train with selected optimizer
    if optimizer_type == "adamw":
        metrics = train_with_adamw(
            **train_params,
            lr=lr,
            patience=patience,
            tolerance=tolerance
        )
    else:  # lbfgs
        metrics = train_with_lbfgs(**train_params)

    # Cleanup
    model.cpu()
    del model
    for k in train_dev: train_dev[k] = train_dev[k].cpu()
    for k in val_dev:   val_dev[k]   = val_dev[k].cpu()
    del train_dev
    del val_dev
    torch.cuda.empty_cache()


# ─── Sweep setup ────────────────────────────────────────────────────────────────
#%%
# Load dataset
subject = 'Allen'

# date = '2022-04-13'
# date = '2022-04-01'
# sess = get_session(subject, date)
full_session_list = []

if __name__ == '__main__': 
    full_session_list = [session for session in get_complete_sessions() if subject in session.name] #and '2022-04-13' in session.name

    # full_session_list.append(get_session(subject, '2022-04-13'))
    #add get_session(subject, '2022-04-13') to beginning of full_session_list
    # full_session_list.insert(0, get_session('Allen', '2022-04-13'))
    full_session_list =[get_session('Allen', '2022-04-13')]
    # full_session_list =[get_session('Allen', '2022-04-15')]
    # full_session_list =[get_session('Logan', '2019-12-31')]

    

for sess in full_session_list:

    try:
        torch.cuda.empty_cache()
        # saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
        gaborium_dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
        gaborium_dset['stim'] = gaborium_dset['stim'].float()
        gaborium_dset['stim'] = (gaborium_dset['stim'] - gaborium_dset['stim'].mean()) / 255
        # gaborium_dset['stim'] = (gaborium_dset['stim'].float() - 127) / 255

        # Define utilities
        n_lags = 18
        def get_inds(dset, n_lags, speed_thresh = 40):
            dpi_valid = dset['dpi_valid']
            new_trials = torch.diff(dset['trial_inds'], prepend=torch.tensor([-1])) != 0
            dfs = ~new_trials
            dfs &= (dpi_valid > 0)

            dt = np.diff(gaborium_dset['t_bins'])
            velocity = np.diff(gaborium_dset['eyepos'], axis=0) / dt[:, None]
            speed = np.linalg.norm(velocity, axis=1)

            dfs_speed = speed < speed_thresh
            dfs_speed = np.concatenate([np.zeros(1, dtype = bool), dfs_speed])
            dfs &= dfs_speed
            
            for iL in range(n_lags):
                dfs &= torch.roll(dfs, 1)
            
            dfs = dfs.float()
            dfs = dfs[:, None]
            return dfs

        n_units = gaborium_dset['robs'].shape[1]
        n_y, n_x = gaborium_dset['stim'].shape[1:3]
        gaborium_dset['dfs'] = get_inds(gaborium_dset, n_lags)
        gaborium_inds = gaborium_dset['dfs'].squeeze().nonzero(as_tuple=True)[0]


        from DataYatesV1 import CombinedEmbeddedDataset

        # Define which keys and lags to use
        keys_lags = {
            'robs': 0,
            'stim': np.arange(n_lags),
            'dfs': 0,
        }


        # Split data into training and validation sets
        train_val_split = 0.6
        gaborium_train_inds, gaborium_val_inds, gaborium_test_inds = split_inds_by_trial_train_val_test(gaborium_dset, gaborium_inds, train_split = 0.6, val_split = 0.2, seed=1002)
        print(f'Gaborium sample split: {len(gaborium_train_inds) / len(gaborium_inds):.3f} train, {len(gaborium_val_inds) / len(gaborium_inds):.3f} val')

        
        train_dset = CombinedEmbeddedDataset([gaborium_dset], [gaborium_train_inds], keys_lags)
        val_dset = CombinedEmbeddedDataset([gaborium_dset], [gaborium_val_inds], keys_lags)
        test_dset = CombinedEmbeddedDataset([gaborium_dset], [gaborium_test_inds], keys_lags)



        # Load datasets
        train_dset_loaded = train_dset[:]
        val_dset_loaded = val_dset[:]
        test_dset_loaded = test_dset[:]


        # # # Calculate STAs and STEs
        sta_file = f'sta_ste_precomputed/{sess.name}/stas_split_{train_val_split}.npy'
        ste_file = f'sta_ste_precomputed/{sess.name}/stes_split_{train_val_split}.npy'
        # if os.path.exists(sta_file) and os.path.exists(ste_file):
        #     stas = np.load(sta_file)
        #     stes = np.load(ste_file)
        # else:
        os.makedirs(os.path.dirname(sta_file), exist_ok=True)
        stas = calc_sta(gaborium_dset['stim'].detach().cpu(), 
                    gaborium_dset['robs'].cpu(), 
                    range(n_lags), 
                    dfs=gaborium_dset['dfs'].cpu().repeat(1, gaborium_dset['robs'].shape[-1]), 
                    progress=True).cpu().squeeze().numpy()

        stes = calc_sta(gaborium_dset['stim'].detach().cpu(), 
                    gaborium_dset['robs'].cpu(), 
                    range(n_lags), 
                    dfs=gaborium_dset['dfs'].cpu().repeat(1, gaborium_dset['robs'].shape[-1]), 
                    stim_modifier=lambda x: x**2, 
                    progress=True).cpu().squeeze().numpy()
        #save stas and stes
        np.save(sta_file, stas)
        np.save(ste_file, stes)
        

        # #load precomputed stas and stes
        # stas = np.load('sta_ste_precomputed/stas.npy')
        # stes = np.load('sta_ste_precomputed/stes.npy')


        if verbosity > 1 or True:
            peak_lags_for_cells = 7
            # Visualize STAs and STEs at a specific lag
            sta_single_lag = stas[:, peak_lags_for_cells]
            stas_norm = sta_single_lag / np.max(np.abs(sta_single_lag), axis=(1,2), keepdims=True)
            fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1})
            plt.show()

            ste_single_lag = stes[:, peak_lags_for_cells]
            stes_norm = 2 * (ste_single_lag - np.min(ste_single_lag, axis=(1,2), keepdims=True)) / np.ptp(ste_single_lag, axis=(1,2), keepdims=True) - 1
            fig, axs = plot_sta_images(stes_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1})
            plt.show()
  


        # cells_to_fit = [31, 33, 39, 50, 57, 58, 59, 60, 68, 70, 74, 85, 90, 94, 100, 101, 106, 107, 108, 110, 126, 127, 137]
        cells_to_fit = list(range(train_dset_loaded['robs'].shape[-1]))
        # qc_units = np.load(f'/mnt/ssd/YatesMarmoV1/processed/{sess.name}/inclusion/inclusion.npz')['qc_units']
        # cells_to_fit = qc_units
        # cells_to_fit = [31, 33, 39, 57, 58, 59, 60, 90, 94, 100, 101, 106, 126, 127] #112
        # cells_to_fit = [33, 101]
        #reverse the list
        # cells_to_fit = cells_to_fit[::-1]
        # cells_to_fit.insert(0, 101)
        # cells_to_fit.insert(0, 33)
        # cells_to_fit = [107]
        #add [31, 33, 39, 57, 58, 59, 60, 90, 94, 100, 101, 106, 126, 127] to beginning of cells_to_fit
        cells_to_fit = [31, 33, 39, 57, 58, 59, 60, 90, 94, 100, 101, 106, 126, 127] + cells_to_fit

        use_best_val_bps = True  # Add this variable to control whether to use best val bps

        verbosity = 0

        # device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print("using device:", device)
        for cell_to_fit in cells_to_fit:
            try:
                cell_to_fit = int(cell_to_fit)
                # define your search space
                config = {
                    # "laplacian_space": tune.loguniform(1e-6, 1e-1),
                    # # "l1":        tune.loguniform(1e-6, 1e-2),
                    # "l1":        0.0,
                    # # "laplacian_time": tune.loguniform(1e-6, 1e-1),
                    # # "laplacian_time": tune.loguniform(1e-10, 1e-1),
                    # "laplacian_time": 0.0,
                    # "local": tune.loguniform(1e-1, 1e2),
                    "laplacian_space": tune.loguniform(1e-5, 1e-1),
                    # "l1": tune.loguniform(1e-15, 1e0),
                    "l1": 0.0,
                    "laplacian_time": tune.loguniform(1e-16, 1e0),
                    "local": tune.loguniform(1e-1, 1e2),
                    "ortho_coeff": 0.0,
                    "eig_indices": [0, 1], # list of ints
                    'model_type': model_to_train,
                    # 'optimizer': tune.choice(['lbfgs', 'adamw']),  # Choose optimizer
                    'optimizer': 'lbfgs',
                    # AdamW specific parameters

                    'lr': 1e-3,
                    'patience': 100,
                    'tolerance': 5e-4,
                    # 'lr': tune.loguniform(1e-4, 1e-2) if config.get('optimizer') == 'adamw' else 1e-3,
                    # 'patience': tune.choice([50, 100, 200]) if config.get('optimizer') == 'adamw' else 100,
                    # 'tolerance': tune.loguniform(1e-5, 1e-3) if config.get('optimizer') == 'adamw' else 5e-4,
                }
                file_dir = f"/mnt/ssd/YatesMarmoV1/standard_model_fits/ray_{config['model_type']}_sweep_6040_30trials_noleak_only_fixations3_true_testset/{sess.name}"
                #check if cell_to_fit cell_[number]_best folder exists
                if os.path.exists(f"{file_dir}/cell_{cell_to_fit}_best"):
                    print(f"Cell {cell_to_fit} already fitted, skipping...")
                    continue
                print("Fitting cell:", cell_to_fit)
                # prepare the datasets once (on CPU)
                train_ds, val_ds, test_ds, train_sta = get_dataset_for_cell(cell_to_fit, train_val_split)
                for k in train_ds: train_ds[k] = train_ds[k].cpu()
                for k in val_ds:   val_ds[k]   = val_ds[k].cpu()
                for k in test_ds: test_ds[k] = test_ds[k].cpu()

                L, V = None, None
                if config["model_type"] == "energy":
                    for k in train_ds: train_ds[k] = train_ds[k].cuda()
                    L, V = get_eigenvalues_and_vectors(train_ds, cell_to_fit)
                    for k in train_ds: train_ds[k] = train_ds[k].cpu()
                    torch.cuda.empty_cache()
                    if L is None or V is None:
                        #dont fit this cell, continue on and print error
                        print(f"Eigenvalues or eigenvectors are None for cell {cell_to_fit}, skipping...")
                        continue
                

                # wrap the fixed args into a Ray trainable
                trainable = tune.with_parameters(
                    tune_trainable,
                    train_ds=train_ds,
                    val_ds=val_ds,
                    train_sta=train_sta,
                    cell_to_fit=cell_to_fit,
                    L=L,
                    V=V,

                )

                

                # ASHA scheduler setup
                scheduler = ASHAScheduler(
                    time_attr="training_iteration",   # ← tell ASHA to look at that key
                    metric="val_bps",
                    mode="max",
                    max_t=2000,
                    grace_period=50,

                    reduction_factor=4
                )
                # give TPE fewer pure‑random pulls
                tpe_sampler = optuna.samplers.TPESampler(
                    n_startup_trials=15,     # only 10 fully random trials, then TPE from trial #11 on
                    multivariate=True,      # model correlations between l1 & laplacian
                )
                # Optuna search algorithm
                search_alg = OptunaSearch(metric="val_bps", 
                                        mode="max", 
                                        sampler=tpe_sampler)


                #check if in ipython notebook
                import sys
                if "ipykernel" in sys.modules:
                    # if in ipython notebook, use JupyterNotebookReporter
                    # raise NotImplementedError("JupyterNotebookReporter is not implemented yet")
                    progress_reporter = JupyterNotebookReporter(overwrite=True)
                else:
                    from ray.tune import CLIReporter
                    reporter = CLIReporter(
                        parameter_columns=["laplacian", "l1"],
                        metric_columns=["val_bps"],
                        print_intermediate_tables=False
                    )


                analysis = tune.run(
                    trainable,
                    config=config,
                    num_samples=50,
                    scheduler=scheduler,
                    search_alg=search_alg, 
                    max_concurrent_trials=2,
                    resources_per_trial={"cpu": 16, "gpu":0.5},
                    
                    progress_reporter=JupyterNotebookReporter(overwrite=True),
                    name="ln_ray_tune",
                    log_to_file=False,
                    verbose = 1,
                    fail_fast=False,
                    raise_on_failed_trial=False,

                )
                # 5) Prepare output directory
                out_dir = f"{file_dir}/cell_{cell_to_fit}_best"
                os.makedirs(out_dir, exist_ok=True)
                # ← insert Optuna‐plotting here
                import optuna.visualization.matplotlib as ovm

                # studies = list(search_alg._studies.values())
                # if len(studies) != 1:
                #     raise RuntimeError(f"Expected one study, got {len(studies)}")
                # study = studies[0]
                study = search_alg._ot_study

                hist_fig = ovm.plot_optimization_history(study)
                hist_fig.figure.savefig(os.path.join(out_dir, "ray_optimization_history.png"),
                                        dpi=300, bbox_inches="tight")
                imp_fig = ovm.plot_param_importances(study)
                imp_fig.figure.savefig(os.path.join(out_dir, "ray_param_importances.png"),
                                    dpi=300, bbox_inches="tight")
                
                # Define debug function to help with trial selection
                def debug_trials(analysis, optimize_param="laplacian_space"):
                    """
                    Debug function to print information about all trials in the analysis.
                    
                    Args:
                        analysis: Ray Tune analysis object
                        optimize_param: Parameter to highlight in the output
                    """
                    print("\n--- DEBUG TRIALS INFORMATION ---")
                    
                    # Get all completed trials
                    completed_trials = [trial for trial in analysis.trials if trial.status == "TERMINATED" and "val_bps" in trial.last_result]
                    
                    if not completed_trials:
                        print("No completed trials found with val_bps metric")
                        return
                    
                    # Sort by val_bps
                    completed_trials.sort(key=lambda t: t.last_result["val_bps"], reverse=True)
                    
                    # Print header
                    print(f"{'val_bps':<10} | {optimize_param:<15} | {'trial_id':<20}")
                    print("-" * 50)
                    
                    # Print each trial
                    for trial in completed_trials:
                        val_bps = trial.last_result["val_bps"]
                        param_value = float(trial.config.get(optimize_param, 0))
                        print(f"{val_bps:<10.4f} | {param_value:<15.6e} | {trial.trial_id}")
                    
                    print("--- END DEBUG INFORMATION ---\n")
                
                # # pick the trial with the highest *last* val_bps
                # best_trial = analysis.get_best_trial(
                #     metric="val_bps",
                #     mode="max",
                #     scope="last"   # ← look only at each trial's final report
                # )

                selection_method = "adjusted_best"  # or "global_best"
                percentage = 0.10  # Only needed for "adjusted_best"
                optimize_param = "laplacian_space"
                
                # Set verbosity temporarily to debug the trial selection
                old_verbosity = verbosity
                verbosity = 1
                
                # Debug the trials before selection
                debug_trials(analysis, optimize_param)
                
                best_trial= select_optimal_trial(
                    analysis,
                    selection_method=selection_method,
                    top_percentage=percentage if selection_method == "adjusted_best" else None,
                    optimize_param=optimize_param,
                    use_best_val_bps=use_best_val_bps
                )
                
                # Restore original verbosity
                verbosity = old_verbosity

                # now extract its hyperparameters & final metric
                best_config    = best_trial.config
                
                # Get the best val_bps using the same approach as the selection
                if use_best_val_bps:
                    # Get results where val_bps is maximal for the selected trial
                    df = analysis.dataframe(metric="val_bps", mode="max")
                    trial_row = df[df['trial_id'] == best_trial.trial_id]
                    if not trial_row.empty:
                        best_final_bps = trial_row.iloc[0]['val_bps']
                    else:
                        raise ValueError(f"Selected trial {best_trial.trial_id} not found in filtered dataframe")
                else:
                    best_final_bps = best_trial.last_result["val_bps"]

                print("Best hyperparams:", best_config)
                print("Best final val_bps:", best_final_bps)
                # ─── Re‑train & save the best model ────────────────────────────────────────────
                import json

                # Unpack best hyperparameters
                best_lap_space = best_config["laplacian_space"]
                best_lap_time  = best_config["laplacian_time"]
                best_local     = best_config["local"]

                best_l1  = best_config["l1"]
                torch.cuda.empty_cache()
                ray.shutdown() 
                # 1) Move data to GPU
                for k in train_ds:
                    train_ds[k] = train_ds[k].to(device)
                for k in val_ds:
                    val_ds[k] = val_ds[k].to(device)
                for k in test_ds:
                    test_ds[k] = test_ds[k].to(device)

                if config["model_type"] == "ln":
                    # 2) Initialize weight & bias from STA
                    init_w, init_b, intial_metrics = get_initial_weight_and_bias_ln(
                        cell_to_fit, train_ds, val_ds, train_sta
                    )

                    # 3) Build model
                    model = LinearNonLinearModel(input_dim=train_sta.shape).to(device)
                    sta_dev = torch.from_numpy(train_sta).float().to(device)
                    model.kernel.data = sta_dev * init_w.to(device)
                    model.bias.data   = init_b.to(device)
                else:

                    L, V = get_eigenvalues_and_vectors(train_ds, cell_to_fit)
                    eig_indices = config["eig_indices"]   # passed in via your search space

                    # 2) fit the per-generator weights & shared bias on train+val
                    w0, b0, intial_metrics = get_initial_weight_and_bias_energy(
                        train_ds, val_ds,
                        cell_to_fit,
                        V.T.float(),   # full (D, D) matrix of eigenvectors
                        eig_indices,
                        device=device
                    )

                    # 3) build the EnergyModel and initialize its kernels & bias
                    input_dim = train_sta.shape             # (n_lags, H, W)
                    n_gens    = len(eig_indices)
                    model     = EnergyModel(input_dim, n_gens).to(device)

                    # slice out exactly the same eigen-maps:
                    evecs = V.T.float()[eig_indices]            # (n_gens, D)
                    evecs = evecs.view(n_gens, *input_dim).to(device)

                    # scale each map by its fitted weight
                    model.kernels.data = evecs * w0[:, None, None, None].to(device)
                    model.bias.data    = b0.to(device)

                # 4) Retrain with the same optimizer used in the trial
                if best_config.get("optimizer", "lbfgs") == "adamw":
                    final_metrics = train_with_adamw(
                        model,
                        laplacian_coeff_space=best_lap_space,
                        laplacian_coeff_time=best_lap_time,
                        local_coeff=best_local,
                        l1_coeff=best_l1,
                        cell_to_fit=cell_to_fit,
                        train_dset_loaded_centered=train_ds,
                        # val_dset_loaded_centered=val_ds,
                        val_dset_loaded_centered=test_ds,
                        using_ray=False,
                        ortho_coeff=float(best_config.get("ortho_coeff", 0.0)),
                        lr=best_config.get("lr", 1e-3),
                        patience=best_config.get("patience", 100),
                        tolerance=best_config.get("tolerance", 5e-4),
                        max_iter=2000,
                        use_best_val_bps=use_best_val_bps
                    )
                else:  # lbfgs
                    final_metrics = train_with_lbfgs(
                        model,
                        laplacian_coeff_space=best_lap_space,
                        laplacian_coeff_time=best_lap_time,
                        local_coeff=best_local,
                        l1_coeff=best_l1,
                        cell_to_fit=cell_to_fit,
                        train_dset_loaded_centered=train_ds,
                        # val_dset_loaded_centered=val_ds,
                        val_dset_loaded_centered=test_ds,
                        using_ray=False,
                        ortho_coeff=float(best_config.get("ortho_coeff", 0.0)),
                        max_iter=2000,
                        use_best_val_bps=use_best_val_bps
                    )
                print(f"Final Val BPS = {final_metrics['val_bps']:.4f}")

                # 6) Save the model weights
                torch.save(model.state_dict(), os.path.join(out_dir, "best_model.pt"))

                # 7) Save STA & kernel visualizations
                save_sta_visualization(
                    train_sta, cell_to_fit,
                    os.path.join(out_dir, "original_sta.png")
                )
                if config["model_type"] == "ln":
                    save_kernel_visualization_ln(
                        model, cell_to_fit,
                        final_metrics["val_bps"], 
                        best_lap_space, 
                        best_l1,
                        os.path.join(out_dir, "best_kernel.png"),
                        initial_val_bps=intial_metrics["val_bps"],
                        laplacian_time_coeff=best_lap_time,
                        local_coeff=best_local,
                        ortho_coeff=float(best_config.get("ortho_coeff", 0.0))
                    )
                else:
                    save_kernel_visualization_energy(
                        model,
                        V.T.float()[eig_indices].reshape(n_gens, *input_dim).cpu(),
                        eig_indices,
                        cell_to_fit,
                        intial_metrics["val_bps"],
                        final_metrics["val_bps"],
                        best_lap_space,
                        best_lap_time,
                        best_local, 
                        best_l1, 
                        float(best_config.get("ortho_coeff", 0.0)),
                        os.path.join(out_dir, "best_kernels.png")
                    )

                # Save training history plots side by side
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
                ax1.plot(final_metrics['history']['train_poisson_loss'], label='Train Poisson Loss')
                ax1.plot(final_metrics['history']['val_poisson_loss'], label='Val Poisson Loss')
                ax1.set_title('Loss History')
                ax1.legend()

                ax2.plot(final_metrics['history']['train_bps'], label='Train BPS')
                ax2.plot(final_metrics['history']['val_bps'], label='Val BPS')
                ax2.set_title('BPS History')
                ax2.legend()

                plt.tight_layout()
                plt.savefig(os.path.join(out_dir, "training_history.png"), dpi=300, bbox_inches='tight')
                plt.close()

                # 8) Dump a JSON summary of results
                
                # Get global best val_bps using dataframe approach
                if use_best_val_bps:
                    global_best_df = analysis.dataframe(metric="val_bps", mode="max")
                    global_best_val_bps = global_best_df['val_bps'].max()
                else:
                    global_best_df = analysis.dataframe()
                    global_best_val_bps = global_best_df['val_bps'].max()
                
                res = {
                    "cell_to_fit": cell_to_fit,
                    "best_params": {"laplacian_space": best_lap_space, 
                                    "laplacian_time": best_lap_time,
                                    "local": best_local,
                                    "l1": best_l1, 
                                    "ortho_coeff": float(best_config.get("ortho_coeff", 0.0))},
                    # "val_bps": final_metrics["val_bps"],
                    "test_bps": final_metrics["val_bps"],
                    "val_bps": best_final_bps,
                    "global_best_val_bps": global_best_val_bps,
                    "intial_val_bps": intial_metrics["val_bps"],
                    "train_bps": final_metrics["train_bps"],
                    "initial_train_bps": intial_metrics["train_bps"],
                    "all_trials_info": get_all_trials_info(analysis, use_best_val_bps=use_best_val_bps),
                    "use_best_val_bps": use_best_val_bps
                }
                with open(os.path.join(out_dir, "results.json"), "w") as f:
                    json.dump(res, f, indent=4)

                print(f"All artifacts saved in {out_dir}/")


                #move tensors to CPU
                for key in train_ds:
                    train_ds[key] = train_ds[key].cpu()
                    val_ds[key] = val_ds[key].cpu()
                #clear GPU memory
                torch.cuda.empty_cache()
                del train_ds
                del val_ds
            except Exception as e:
                print(f"Error fitting cell {cell_to_fit} in session {sess.name}: {e}")
                continue
    except Exception as e:
        print(f"Error fitting session {sess.name}")
        print(e)
        continue
        # raise e
#%%


# #%%
# torch.cuda.empty_cache()
# sess = get_session('Allen', '2022-04-13')
# # saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
# gaborium_dset = DictDataset.load(sess.sess_dir / 'shifter' / 'gaborium_shifted.dset')
# gaborium_dset['stim'] = gaborium_dset['stim'].float()
# gaborium_dset['stim'] = (gaborium_dset['stim'] - gaborium_dset['stim'].mean()) / 255
# # gaborium_dset['stim'] = (gaborium_dset['stim'].float() - 127) / 255

# # Define utilities
# n_lags = 18
# def get_inds(dset, n_lags, speed_thresh = 40):
#     dpi_valid = dset['dpi_valid']
#     new_trials = torch.diff(dset['trial_inds'], prepend=torch.tensor([-1])) != 0
#     dfs = ~new_trials
#     dfs &= (dpi_valid > 0)

#     dt = np.diff(gaborium_dset['t_bins'])
#     velocity = np.diff(gaborium_dset['eyepos'], axis=0) / dt[:, None]
#     speed = np.linalg.norm(velocity, axis=1)

#     dfs_speed = speed < speed_thresh
#     dfs_speed = np.concatenate([np.zeros(1, dtype = bool), dfs_speed])
#     dfs &= dfs_speed
    
#     for iL in range(n_lags):
#         dfs &= torch.roll(dfs, 1)
    
#     dfs = dfs.float()
#     dfs = dfs[:, None]
#     return dfs

# n_units = gaborium_dset['robs'].shape[1]
# n_y, n_x = gaborium_dset['stim'].shape[1:3]
# gaborium_dset['dfs'] = get_inds(gaborium_dset, n_lags)
# gaborium_inds = gaborium_dset['dfs'].squeeze().nonzero(as_tuple=True)[0]


# from DataYatesV1 import CombinedEmbeddedDataset

# # Define which keys and lags to use
# keys_lags = {
#     'robs': 0,
#     'stim': np.arange(n_lags),
#     'dfs': 0,
# }


# # Split data into training and validation sets
# train_val_split = 0.6
# gaborium_train_inds, gaborium_val_inds = split_inds_by_trial(gaborium_dset, gaborium_inds, train_val_split, seed=1002)
# print(f'Gaborium sample split: {len(gaborium_train_inds) / len(gaborium_inds):.3f} train, {len(gaborium_val_inds) / len(gaborium_inds):.3f} val')

# train_dset = CombinedEmbeddedDataset([gaborium_dset], [gaborium_train_inds], keys_lags)
# val_dset = CombinedEmbeddedDataset([gaborium_dset], [gaborium_val_inds], keys_lags)


# # Load datasets
# train_dset_loaded = train_dset[:]
# val_dset_loaded = val_dset[:]


# # # Calculate STAs and STEs
# sta_file = f'sta_ste_precomputed/{sess.name}/stas_split_{train_val_split}.npy'
# ste_file = f'sta_ste_precomputed/{sess.name}/stes_split_{train_val_split}.npy'
# # if os.path.exists(sta_file) and os.path.exists(ste_file):
# #     stas = np.load(sta_file)
# #     stes = np.load(ste_file)
# # else:
# os.makedirs(os.path.dirname(sta_file), exist_ok=True)
# stas = calc_sta(gaborium_dset['stim'].detach().cpu(), 
#             gaborium_dset['robs'].cpu(), 
#             range(n_lags), 
#             dfs=gaborium_dset['dfs'].cpu().repeat(1, gaborium_dset['robs'].shape[-1]), 
#             progress=True).cpu().squeeze().numpy()

# stes = calc_sta(gaborium_dset['stim'].detach().cpu(), 
#         gaborium_dset['robs'].cpu(), 
#         range(n_lags), 
#         dfs=gaborium_dset['dfs'].cpu().repeat(1, gaborium_dset['robs'].shape[-1]), 
#         stim_modifier=lambda x: x**2, 
#         progress=True).cpu().squeeze().numpy()
# #save stas and stes
# np.save(sta_file, stas)
# np.save(ste_file, stes)

# # #load precomputed stas and stes
# # stas = np.load('sta_ste_precomputed/stas.npy')
# # stes = np.load('sta_ste_precomputed/stes.npy')


# if verbosity > 1 or True:
#     peak_lags_for_cells = 7
#     # Visualize STAs and STEs at a specific lag
#     sta_single_lag = stas[:, peak_lags_for_cells]
#     stas_norm = sta_single_lag / np.max(np.abs(sta_single_lag), axis=(1,2), keepdims=True)
#     fig, axs = plot_sta_images(stas_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1})
#     plt.show()

#     ste_single_lag = stes[:, peak_lags_for_cells]
#     stes_norm = 2 * (ste_single_lag - np.min(ste_single_lag, axis=(1,2), keepdims=True)) / np.ptp(ste_single_lag, axis=(1,2), keepdims=True) - 1
#     fig, axs = plot_sta_images(stes_norm, {'cmap': 'coolwarm', 'vmin': -1, 'vmax': 1})
#     plt.show()

# #%%
# # EXAMPLE USAGE FOR TRAINING A SINGLE CELL
# # #%%
# cell_to_fit = 101
# train_dset_loaded_centered, val_dset_loaded_centered, train_sta = get_dataset_for_cell(cell_to_fit, train_val_split)
# device = 'cuda:0' 
# # Move data to GPU
# for key in train_dset_loaded_centered:
#     train_dset_loaded_centered[key] = train_dset_loaded_centered[key].to(device)
#     val_dset_loaded_centered[key] = val_dset_loaded_centered[key].to(device)

# #%%
# nl_to_use = F.relu
# verbosity = 1
# # Set up model and initialize with pre-trained weights
# init_w, init_b, metric_holders = get_initial_weight_and_bias_ln(cell_to_fit, train_dset_loaded_centered, val_dset_loaded_centered, train_sta)
# #%%
# ln = LinearNonLinearModel(input_dim=train_sta.shape)
# ln.kernel.data = torch.from_numpy(train_sta).float() * init_w
# ln.bias.data = init_b
# ln.to(device)


# # Hyperparameters
# laplacian_coeff_space = 0.0009313699276561689
# laplacian_coeff_time = 8.587624406412717e-05 #1e-4
# local_coeff = 0.10040469117189853
# l1_coeff = 0.0
# ortho_coeff = 0.0
# optimizer_type = "lbfgs" 
# if optimizer_type == "adamw":
#     final_metrics = train_with_adamw(
#         ln,
#         laplacian_coeff_space=laplacian_coeff_space,
#         laplacian_coeff_time=laplacian_coeff_time,
#         local_coeff=local_coeff,
#         l1_coeff=l1_coeff,
#         cell_to_fit=cell_to_fit,
#         train_dset_loaded_centered=train_dset_loaded_centered,
#         val_dset_loaded_centered=val_dset_loaded_centered,
#         using_ray=False,
#         ortho_coeff=ortho_coeff,
#         lr=1e-3,
#         patience=100,
#         tolerance=5e-4,
#         max_iter=2000
#     )
# else:  # lbfgs
#     final_metrics = train_with_lbfgs(
#         ln,
#         laplacian_coeff_space=laplacian_coeff_space,
#         laplacian_coeff_time=laplacian_coeff_time,
#         local_coeff=local_coeff,
#         l1_coeff=l1_coeff,
#         cell_to_fit=cell_to_fit,
#         train_dset_loaded_centered=train_dset_loaded_centered,
#         val_dset_loaded_centered=val_dset_loaded_centered,
#         using_ray=False,
#         ortho_coeff=ortho_coeff,
#         max_iter=2000,
#         use_best_val_bps=True
#     )

# # Plot metrics
# plt.plot(final_metrics['history']['train_poisson_loss'], label='Train Poisson Loss')
# plt.plot(final_metrics['history']['val_poisson_loss'], label='Val Poisson Loss')
# plt.legend()
# plt.show()

# plt.plot(final_metrics['history']['train_bps'], label='Train BPS')
# plt.plot(final_metrics['history']['val_bps'], label='Val BPS')
# plt.legend()
# plt.show()

# # Plot initial STA vs final kernel
# sta_norm = train_sta / np.max(np.abs(train_sta))
# kernel_norm = ln.kernel.detach().cpu().numpy() / np.max(np.abs(ln.kernel.detach().cpu().numpy()))
# plot_tensor = torch.stack([torch.from_numpy(sta_norm), torch.from_numpy(kernel_norm)], dim=0)[:, :, None]
# fig, axs = plot_stas(plot_tensor, row_labels=['Initial STA', 'Trained Kernel'], col_labels=[f'lag {i}' for i in range(plot_tensor.shape[1])])
# plt.suptitle(f"Cell {cell_to_fit}: STA vs Trained Kernel")
# plt.show()

# print(f"Val BPS: {final_metrics['val_bps']:.3f}")


# #%%
# #load model from here: /mnt/ssd/YatesMarmoV1/standard_model_fits/ray_ln_sweep_6040_30trials_noleak_only_fixations3/Allen_2022-04-13/cell_101_best/best_model.pt, this is the state_dict of the model
# ln = LinearNonLinearModel(input_dim=train_sta.shape)
# ln.load_state_dict(torch.load('/mnt/ssd/YatesMarmoV1/standard_model_fits/ray_ln_sweep_6040_30trials_noleak_only_fixations3/Allen_2022-04-13/cell_101_best/best_model.pt'))
# ln.to(device)

# #%%
# #compute the val_bps using evaluate_model
# val_bps = evaluate_model(ln, 
#                          laplacian_coeff_space, 
#                          laplacian_coeff_time, 
#                          local_coeff, 
#                          l1_coeff, 
#                          train_dset_loaded_centered, 
#                          val_dset_loaded_centered,
#                          cell_to_fit,
#                          ortho_coeff=ortho_coeff)
# print(f"Val BPS: {val_bps['val_bps']:.3f}")
# #%%




# #%%

# loss_val, train_poisson_loss, train_reg, train_batch = forward(ln, 
#                                                                train_dset_loaded_centered, 
#                                                                laplacian_coeff_space, 
#                                                                laplacian_coeff_time, 
#                                                                local_coeff, 
#                                                                l1_coeff, 
#                                                                cell_to_fit, 
#                                                                ortho_coeff=ortho_coeff)


# #%%
# def plot_nonlinearity(generator, spikes, cell_to_fit, n_bins=50):
#     """
#     Plot the nonlinearity by binning generator values and computing mean spike rate.
    
#     Args:
#         generator: tensor of generator values
#         spikes: tensor of spike counts
#         n_bins: number of bins for histogram
#     """
#     # Convert to numpy and flatten
#     gen = generator.detach().cpu().numpy().flatten()
#     spk = spikes.detach().cpu().numpy().flatten()
    
#     # Create bins
#     bins = np.linspace(gen.min(), gen.max(), n_bins)
#     bin_centers = (bins[:-1] + bins[1:]) / 2
    
#     # Compute mean spike rate in each bin
#     mean_spikes = np.zeros(len(bin_centers))
#     std_spikes = np.zeros(len(bin_centers))
#     for i in range(len(bin_centers)):
#         mask = (gen >= bins[i]) & (gen < bins[i+1])
#         if np.any(mask):
#             mean_spikes[i] = np.mean(spk[mask])
#             std_spikes[i] = np.std(spk[mask])
    
#     # Plot
#     plt.figure(figsize=(10, 6))
#     plt.errorbar(bin_centers, mean_spikes, yerr=std_spikes, fmt='o', alpha=0.5, label='Data')
    
#     # Plot softplus fit
#     x = np.linspace(gen.min(), gen.max(), 1000)
#     y = F.softplus(torch.tensor(x + ln.bias.detach().cpu().numpy())).numpy()
#     plt.plot(x, y, 'r-', label='Softplus with bias')
    
#     plt.xlabel('Generator value')
#     plt.ylabel('Mean spike rate')
#     plt.title(f'Cell {cell_to_fit}')
#     plt.legend()
#     plt.grid(True)
#     plt.show()
    
#     # # Compute correlation
#     # corr = np.corrcoef(gen, spk)[0,1]
#     # print(f"Correlation between generator and spikes: {corr:.3f}")

# # Plot nonlinearity
# plot_nonlinearity(train_batch['generator'], train_dset_loaded_centered['robs'][:, cell_to_fit], cell_to_fit, 20)

#%%
# # EXAMPLE USAGE FOR TRAINING A SINGLE CELL
# # #%%
# cell_to_fit = 90
# train_dset_loaded_centered, val_dset_loaded_centered, train_sta = get_dataset_for_cell(cell_to_fit)
# device = 'cuda:0' 
# # Move data to GPU
# for key in train_dset_loaded:
#     train_dset_loaded_centered[key] = train_dset_loaded_centered[key].to(device)
#     val_dset_loaded_centered[key] = val_dset_loaded_centered[key].to(device)

# #%%
# verbosity = 1
# # Set up model and initialize with pre-trained weights
# intial_weight, initial_bias = get_initial_weight_and_bias(cell_to_fit, train_dset_loaded_centered, train_sta)
# ln = LinearNonLinearModel(input_dim=train_sta.shape)
# ln.kernel.data = torch.from_numpy(train_sta).float() * intial_weight
# ln.bias.data = initial_bias
# ln.to(device)
# # Hyperparameters
# laplacian_coeff = 5e-4
# l1_coeff = 1e-6

# # final_metrics = train_with_lbfgs(ln, laplacian_coeff, l1_coeff, cell_to_fit, train_dset_loaded_centered, val_dset_loaded_centered)
# final_metrics = train_with_adamw(ln, laplacian_coeff, l1_coeff, cell_to_fit, train_dset_loaded_centered, val_dset_loaded_centered)


# #%%
# #EXAMPLE USAGE FOR TRAINING A SINGLE CELL WITH ENERGY MODEL
# cid = 112
# verbosity = 0
# train_data, val_data, train_sta = get_dataset_for_cell(cid, dim_for_centering=32)

# device = 'cuda'
# for key in train_data: train_data[key] = train_data[key].to(device)

# L, V = get_eigenvalues_and_vectors(train_data, cid)
# #%%
# for key in val_data: val_data[key] = val_data[key].to(device)
# torch.cuda.empty_cache()
# #%%
# verbosity = 1
# eig_indices = [0,1]
# weights, bias, initial_metrics = get_initial_weight_and_bias_energy(
#     train_data,
#     val_data, 
#     cid, 
#     V.T.float(),               # pass the full V.T
#     eig_indices=eig_indices,  # for top 3 components, for example
#     device=device
# )
# #%%
# torch.cuda.empty_cache()

# # train_dev = {k: v.to(device) for k, v in train_data.items()}
# # val_dev   = {k: v.to(device) for k, v in val_data.items()}
# # 1) build the model
# input_dim = train_data['stim'].shape[1:]   # (n_lags, H, W)
# n_gens    = len(eig_indices)
# model     = EnergyModel(input_dim, n_gens).to(device)

# # 2) grab the same eigen‐vectors you used in get_initial_…
# #    V.T is (D, D) so rows = eigen-maps
# evecs = V.T.float()[eig_indices]           # (n_gens, D)
# evecs = evecs.view(n_gens, *input_dim).to(device)     # (n_gens, n_lags, H, W)

# # 3) initialize kernels by scaling each map by its fitted weight
# #    (just like ln.kernel.data = sta * initial_weight)
# model.kernels.data = (evecs * weights[:, None, None, None].to(device))

# # 4) initialize per-generator biases
# model.bias.data = bias.to(device)

# verbosity = 2
# final_metrics = train_with_lbfgs(
#     model,
#     # laplacian_coeff_space=3e-3,
#     # laplacian_coeff_time=1e-3,
#     # local_coeff=33,
#     laplacian_coeff_space=3.438387551352766e-02,
#     laplacian_coeff_time=0.0042312872594172275,
#     local_coeff=26.31461108608449,
#     # l1_coeff=3e-5,
#     l1_coeff=0.0,
#     cell_to_fit=cid,
#     train_dset_loaded_centered=train_data,
#     val_dset_loaded_centered=val_data,
#     using_ray=False,
#     ortho_coeff=0.0
# )

# # for key in train_data: train_data[key] = train_data[key].cpu(); val_data[key] = val_data[key].cpu()
# torch.cuda.empty_cache()

# #%%
# #create the plot_tensors from the model.kernels and plot it
# plot_tensors = torch.stack(
#     [*[ model.kernels[i].cpu() for i in eig_indices ]],
#     dim=0
# ).detach().cpu()[:, :, None]
# from DataYatesV1 import plot_stas
# plot_stas(
#     evecs[:, :, None].cpu(),
# )

# plot_stas(
#     plot_tensors
# )
# plt.title('Tejas')

# # #%%
# # # dims = train_data['stim'].shape[-3:]
# # # save_kernel_visualization_energy(
# # #     model,
# # #     V.T.float()[eig_indices].reshape(n_gens, *dims).cpu(),
# # #     eig_indices,
# # #     cid,
# # #     final_metrics["val_bps"],
# # #     0, 0, 0,
# # #     "energy_kernels.png"
# # # )
# # # %%

# # %%

