#!/usr/bin/env python3
"""
Test script for the unified convnet configuration approach.
Tests DenseNet, ResNet, and VanillaCNN with both legacy and new unified formats.
"""

import torch
import torch.nn as nn
from DataYatesV1.models.modules.convnet import DenseNet, ResNet, VanillaCNN, X3DNet

def test_densenet_legacy():
    """Test DenseNet with legacy growth_rate format."""
    print("Testing DenseNet with legacy format...")
    
    config = {
        'dim': 3,
        'initial_channels': 6,
        'growth_rate': 8,
        'num_blocks': 3,
        'checkpointing': False,
        'block_config': {
            'conv_params': {
                'type': 'depthwise',
                'kernel_size': [3, 5, 5],
                'padding': [1, 2, 2]
            },
            'norm_type': 'rms',
            'act_type': 'mish',
            'pool_params': {}
        }
    }
    
    model = DenseNet(config)
    print(f"  Output channels: {model.get_output_channels()}")
    
    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Legacy DenseNet works!")
    return model

def test_densenet_unified():
    """Test DenseNet with new unified channels format."""
    print("\nTesting DenseNet with unified format...")
    
    config = {
        'dim': 3,
        'initial_channels': 6,
        'channels': [8, 16, 12],  # Explicit channel counts
        'checkpointing': False,
        'block_config': {
            'conv_params': {
                'type': 'depthwise',
                'kernel_size': [3, 5, 5],
                'padding': [1, 2, 2]
            },
            'norm_type': 'rms',
            'act_type': 'mish',
            'pool_params': {}
        }
    }
    
    model = DenseNet(config)
    print(f"  Output channels: {model.get_output_channels()}")
    
    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Unified DenseNet works!")
    return model

def test_vanilla_cnn_legacy():
    """Test VanillaCNN with legacy layer_configs format."""
    print("\nTesting VanillaCNN with legacy format...")
    
    config = {
        'dim': 3,
        'initial_channels': 6,
        'base_channels': 32,
        'layer_configs': [
            {
                'out_channels': 16,
                'conv_params': {
                    'type': 'standard',
                    'kernel_size': [3, 3, 3],
                    'padding': [1, 1, 1]
                },
                'norm_type': 'batch',
                'act_type': 'relu'
            },
            {
                'out_channels': 32,
                'conv_params': {
                    'type': 'standard',
                    'kernel_size': [3, 3, 3],
                    'padding': [1, 1, 1]
                },
                'norm_type': 'batch',
                'act_type': 'relu'
            }
        ]
    }
    
    model = VanillaCNN(config)
    print(f"  Output channels: {model.get_output_channels()}")
    
    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Legacy VanillaCNN works!")
    return model

def test_vanilla_cnn_unified():
    """Test VanillaCNN with new unified format."""
    print("\nTesting VanillaCNN with unified format...")
    
    config = {
        'dim': 3,
        'initial_channels': 6,
        'channels': [16, 32, 24],
        'block_config': {
            'conv_params': {
                'type': 'standard',
                'kernel_size': [3, 3, 3],
                'padding': [1, 1, 1]
            },
            'norm_type': 'batch',
            'act_type': 'relu'
        }
    }
    
    model = VanillaCNN(config)
    print(f"  Output channels: {model.get_output_channels()}")
    
    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Unified VanillaCNN works!")
    return model

def test_resnet_legacy():
    """Test ResNet with legacy layer_configs format."""
    print("\nTesting ResNet with legacy format...")
    
    config = {
        'dim': 3,
        'initial_channels': 6,
        'base_channels': 32,
        'layer_configs': [
            {
                'out_channels': 16,
                'conv_params': {
                    'type': 'standard',
                    'kernel_size': [3, 3, 3],
                    'padding': [1, 1, 1]
                },
                'norm_type': 'batch',
                'act_type': 'relu'
            },
            {
                'out_channels': 32,
                'conv_params': {
                    'type': 'standard',
                    'kernel_size': [3, 3, 3],
                    'padding': [1, 1, 1]
                },
                'norm_type': 'batch',
                'act_type': 'relu'
            }
        ]
    }
    
    model = ResNet(config)
    print(f"  Output channels: {model.get_output_channels()}")
    
    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Legacy ResNet works!")
    return model

def test_resnet_unified():
    """Test ResNet with new unified format."""
    print("\nTesting ResNet with unified format...")
    
    config = {
        'dim': 3,
        'initial_channels': 6,
        'channels': [16, 32, 24],
        'block_config': {
            'conv_params': {
                'type': 'standard',
                'kernel_size': [3, 3, 3],
                'padding': [1, 1, 1]
            },
            'norm_type': 'batch',
            'act_type': 'relu'
        }
    }
    
    model = ResNet(config)
    print(f"  Output channels: {model.get_output_channels()}")
    
    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Unified ResNet works!")
    return model

def test_x3dnet_legacy():
    """Test X3DNet with legacy depth/width format."""
    print("\nTesting X3DNet with legacy format...")

    config = {
        'dim': 3,
        'initial_channels': 6,
        'depth': [2, 3, 2],
        'width': [32, 64, 128],
        't_kernel': 5,
        's_kernel': 3,
        'exp_ratio': 4,
        'norm_type': 'grn',
        'act_type': 'silu',
        'stride_stages': [1, 2, 2],
        'checkpointing': False
    }

    model = X3DNet(config)
    print(f"  Output channels: {model.get_output_channels()}")

    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Legacy X3DNet works!")
    return model

def test_x3dnet_unified():
    """Test X3DNet with new unified channels format."""
    print("\nTesting X3DNet with unified format...")

    config = {
        'dim': 3,
        'initial_channels': 6,
        'channels': [32, 64, 128],  # Unified format
        'depth': [2, 3, 2],         # X3D-specific
        't_kernel': 5,
        's_kernel': 3,
        'exp_ratio': 4,
        'norm_type': 'grn',
        'act_type': 'silu',
        'stride_stages': [1, 2, 2],
        'checkpointing': False
    }

    model = X3DNet(config)
    print(f"  Output channels: {model.get_output_channels()}")

    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Unified X3DNet works!")
    return model

def test_x3dnet_simple_unified():
    """Test X3DNet with simple unified format (channels only)."""
    print("\nTesting X3DNet with simple unified format...")

    config = {
        'dim': 3,
        'initial_channels': 6,
        'channels': [32, 64, 96],  # Only channels specified
        't_kernel': 3,
        's_kernel': 3,
        'exp_ratio': 2,
        'norm_type': 'grn',
        'act_type': 'silu',
        'checkpointing': False
    }

    model = X3DNet(config)
    print(f"  Output channels: {model.get_output_channels()}")

    # Test forward pass
    x = torch.randn(2, 6, 8, 32, 32)
    y = model(x)
    print(f"  Input shape: {x.shape}")
    print(f"  Output shape: {y.shape}")
    print("  ✓ Simple unified X3DNet works!")
    return model

if __name__ == "__main__":
    print("Testing unified convnet configuration approach...\n")

    # Test all formats
    test_densenet_legacy()
    test_densenet_unified()
    test_vanilla_cnn_legacy()
    test_vanilla_cnn_unified()
    test_resnet_legacy()
    test_resnet_unified()
    test_x3dnet_legacy()
    test_x3dnet_unified()
    test_x3dnet_simple_unified()

    print("\n🎉 All tests passed! The unified convnet configuration works correctly.")
